# 自动发送逻辑优化测试指南

## 优化内容总结

### 1. useArticleCacheStore 优化
- ✅ 添加了 `AutoSendChatData` 接口
- ✅ 添加了 `autoSendChatData` 状态管理
- ✅ 添加了 `setAutoSendChatData`、`getAutoSendChatData`、`clearAutoSendChatData` 方法
- ✅ 添加了统一的 `setNewArticleChatData` 方法，根据内容类型自动配置

### 2. ChatComponent 优化
- ✅ 重构了自动发送逻辑，优先使用 store 中的配置
- ✅ 支持不同类型的 endpoint 配置：
  - video: `/llm/video/describe`
  - audio: `/llm/audio/describe`
  - doc: `null` (使用默认 endpoint)
- ✅ 根据类型决定是否包含用户消息：
  - video/audio: 不包含用户消息
  - doc: 包含用户消息
- ✅ 添加了内容刷新回调支持

### 3. ArticlePage 组件优化
- ✅ 移除了本地的 `setNewArticleChatData` 状态
- ✅ 使用 store 中的统一方法
- ✅ 添加了内容刷新机制
- ✅ 优化了自动发送完成处理

### 4. 内容数据刷新机制
- ✅ 在 video/audio 处理完成后自动刷新内容数据
- ✅ 使用 React Query 的缓存失效机制
- ✅ 添加了用户友好的提示信息

## 测试场景

### 场景 1: Video 类型自动发送
1. 上传一个视频文件（没有 fileUrl）
2. 应该自动触发视频描述请求
3. 验证：
   - 使用 `/llm/video/describe` endpoint
   - 消息列表中不包含用户消息
   - 完成后刷新内容数据

### 场景 2: Audio 类型自动发送
1. 上传一个音频文件（没有 fileUrl）
2. 应该自动触发音频描述请求
3. 验证：
   - 使用 `/llm/audio/describe` endpoint
   - 消息列表中不包含用户消息
   - 完成后刷新内容数据

### 场景 3: Doc 类型自动发送
1. 通过其他方式触发 doc 类型的自动发送
2. 验证：
   - 使用默认 endpoint (null)
   - 消息列表中包含用户消息
   - 不触发内容数据刷新

## 关键改进点

1. **统一配置管理**: 所有自动发送配置都通过 store 管理，避免了组件间的状态同步问题
2. **类型驱动的行为**: 根据内容类型自动配置不同的 endpoint 和消息处理方式
3. **智能刷新机制**: 只在需要时（video/audio 处理完成）刷新内容数据
4. **向后兼容**: 保留了旧的 props 方式作为备用方案
5. **错误处理**: 改进了错误处理和用户反馈

## 代码结构

```
useArticleCacheStore
├── autoSendChatData: Record<number, AutoSendChatData>
├── setAutoSendChatData(contentId, data)
├── getAutoSendChatData(contentId)
├── clearAutoSendChatData(contentId)
└── setNewArticleChatData(contentId, contentData, options?)
    ├── 自动检测内容类型
    ├── 配置对应的 endpoint
    └── 设置消息包含策略
```

## 使用方式

```typescript
// 在 ArticlePage 中
setNewArticleChatData(contentId, contentData); // 自动配置

// 在 ChatComponent 中
const autoSendData = getAutoSendChatData(contentId);
if (autoSendData?.autoSend) {
  await handleSendMessage(
    autoSendData.initialInputValue,
    selectedModel,
    autoSendData
  );
}
```

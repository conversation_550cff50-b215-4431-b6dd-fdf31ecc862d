<!--
 * @Date: 2025-06-11 21:43:29
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-11 21:56:12
 * @FilePath: /tutoro-ai-front/DOCKER_README.md
-->
# TutoroAI Frontend Docker 配置说明

## 📋 配置文件说明

### 1. 环境变量配置
- `config/env.ts` - TypeScript 环境配置文件，用于应用程序中访问环境变量
- `docker.env` - Docker 专用环境变量文件

### 2. 核心环境变量
```bash
NEXT_PUBLIC_API_BASE_URL=https://api.tutoroai.com
NODE_ENV=production
PORT=3000
NEXT_TELEMETRY_DISABLED=1
```

### 3. Docker 配置
- `Dockerfile` - 多阶段构建配置，包含依赖安装、构建和运行阶段
- `docker-build.sh` - 自动化构建脚本

## 🚀 使用方法

### 方法一：使用构建脚本（推荐）
```bash
# 确保 Docker 已安装并运行
./docker-build.sh
```

### 方法二：手动构建
```bash
# 构建镜像
docker build -t tutoro-ai-front:latest .

# 保存为 tar 文件
docker save -o tutoro-ai-front.tar tutoro-ai-front:latest
```

### 方法三：使用 docker-compose
```bash
# 使用 docker-compose 构建和运行
docker-compose up --build
```

## 📦 文件说明

构建完成后会生成：
- `tutoro-ai-front:latest` - Docker 镜像
- `tutoro-ai-front.tar` - 镜像文件（可用于部署）

## 🔧 部署说明

### 本地运行
```bash
docker run -p 3000:3000 tutoro-ai-front:latest
```

### 导入镜像文件
```bash
# 在目标服务器上导入镜像
docker load -i tutoro-ai-front.tar

# 运行容器
docker run -d -p 3000:3000 --name tutoro-ai tutoro-ai-front:latest
```

### 使用自定义环境变量
```bash
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_API_BASE_URL=https://your-api-url.com \
  tutoro-ai-front:latest
```

## 🛠️ 故障排除

### 1. 构建失败
- 检查 Docker 是否正常运行
- 确保网络连接正常（需要下载依赖）
- 检查 `package.json` 中的依赖是否正确

### 2. 运行时错误
- 检查环境变量是否正确设置
- 确保 API 服务 (https://api.tutoroai.com) 可访问

### 3. 端口冲突
```bash
# 使用其他端口
docker run -p 8080:3000 tutoro-ai-front:latest
```

## 📝 注意事项

1. 确保 `next.config.mjs` 中包含 `output: 'standalone'` 配置
2. 环境变量 `NEXT_PUBLIC_API_BASE_URL` 必须在构建时就设置好
3. 镜像大小约为 150-300MB，根据依赖数量有所不同
4. 生产环境建议使用具体的版本标签而不是 `latest` 
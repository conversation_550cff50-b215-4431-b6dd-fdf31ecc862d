<!--
 * @Date: 2025-06-11 21:57:47
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-11 21:58:57
 * @FilePath: /tutoro-ai-front/deploy/README.md
-->
# 🚀 TutoroAI Frontend 部署文件

本文件夹包含了 TutoroAI Frontend 项目的所有部署相关文件和脚本。

## 📁 文件结构

```
deploy/
├── README.md                 # 本说明文档
├── DOCKER_README.md         # Docker详细说明文档
├── docker.env              # Docker环境变量配置
├── docker-build.sh         # 完整Docker构建脚本
├── docker-build-simple.sh  # 简化Docker构建脚本
└── check-docker.sh         # Docker安装检查脚本
```

## 🎯 快速开始

### 1. 检查Docker环境
```bash
cd deploy
./check-docker.sh
```

### 2. 构建Docker镜像
```bash
# 使用简化版本（推荐）
./docker-build-simple.sh

# 或使用完整版本
./docker-build.sh
```

### 3. 运行容器
```bash
docker run -p 3000:3000 tutoro-ai-front:latest
```

## 📦 输出文件

构建成功后会生成：
- **tutoro-ai-front:latest** - Docker镜像
- **tutoro-ai-front.tar** - 镜像tar文件（在项目根目录）

## 🔧 环境配置

主要环境变量（在 `docker.env` 中配置）：
- `NEXT_PUBLIC_API_BASE_URL=https://api.tutoroai.com`
- `NODE_ENV=production`
- `PORT=3000`
- `NEXT_TELEMETRY_DISABLED=1`

## 📖 详细文档

更多详细信息请查看：
- [DOCKER_README.md](./DOCKER_README.md) - Docker构建和部署详细说明

## 🆘 常见问题

### Docker未安装
运行 `./check-docker.sh` 检查并按提示安装Docker。

### 构建失败
1. 确保网络连接正常
2. 检查Docker是否正在运行
3. 查看构建日志排查具体错误

### 端口冲突
```bash
# 使用其他端口运行
docker run -p 8080:3000 tutoro-ai-front:latest
```

## 📞 支持

如遇问题，请检查：
1. Docker版本是否兼容
2. 网络连接是否正常
3. 环境变量配置是否正确 
/*
 * @Date: 2025-06-02 22:24:16
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-02 22:26:42
 * @FilePath: /tutoro-ai-front/types/app.ts
 */
// App状态接口
export interface AppState {
  // 模态框状态
  isLanguageModalOpen: boolean;
  isFeedbackModalOpen: boolean;
  isUpgradeModalOpen: boolean;
  
  // 聊天窗口状态
  isChatOpen: boolean;
  isMobile: boolean;
  
  // 文章标题
  articleTitle: string;
  
  // 答题状态
  examProgress: {
    totalQuestions: number;
    answeredQuestions: number;
    currentProgress: number; // 0-100
    isLoading: boolean;
  };
  
  // 操作方法
  openLanguageModal: () => void;
  closeLanguageModal: () => void;
  toggleLanguageModal: () => void;
  
  openFeedbackModal: () => void;
  closeFeedbackModal: () => void;
  toggleFeedbackModal: () => void;
  
  openUpgradeModal: () => void;
  closeUpgradeModal: () => void;
  toggleUpgradeModal: () => void;
  
  // 聊天窗口操作
  openChat: () => void;
  closeChat: () => void;
  toggleChat: () => void;
  setMobile: (isMobile: boolean) => void;
  
  // 文章标题操作
  setArticleTitle: (title: string) => void;
  clearArticleTitle: () => void;
  
  // 答题状态操作
  setExamProgress: (totalQuestions: number, answeredQuestions: number) => void;
  setExamLoading: (isLoading: boolean) => void;
  resetExamProgress: () => void;
  
  // 关闭所有模态框
  closeAllModals: () => void;
} 
export interface ArticleStub {
  id: string
  title: string
  thumbnailUrl?: string
}

export interface ExamPreferences {
  numberOfQuestions: number
  questionTypes: ("multiple-choice" | "fill-in-the-blank" | "essay" | "all")[] // "all" could mean a mix
  durationMinutes?: number // Optional
  difficulty?: "easy" | "medium" | "hard"
}

export type ReferenceMaterialType = "upload" | "paste" | null

export interface ReferenceMaterial {
  type: ReferenceMaterialType
  content?: string // For pasted text or URL
  file?: File // For uploaded file
}

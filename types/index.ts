/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-11 14:38:51
 * @FilePath: /tutoro-ai-front/types/index.ts
 */
export interface Chapter {
  id: string
  title: string
  progress: number // 0-100
}

export interface Flashcard {
  id: string
  question: string
  answer: string
  hint?: string
  explanation?: string
}

export type QuestionType = "multiple-choice" | "fill-in-the-blank" | "free-response"

export interface BaseQuestion {
  id: string
  question: string
  correctAnswer: string
  hint?: string
  pageReference?: string // e.g., "Page 1-2"
  explanation?: string // Explanation for the correct answer
}

export interface MultipleChoiceQuestion extends BaseQuestion {
  type: "multiple-choice"
  options: string[]
}

export interface FillInTheBlankQuestion extends BaseQuestion {
  type: "fill-in-the-blank"
  questionParts: [string, string] // Text before blank, text after blank
}

export interface FreeResponseQuestion extends BaseQuestion {
  type: "free-response"
  // Potentially keywords or concepts for AI grading if implemented
}

export type QuizQuestion = MultipleChoiceQuestion | FillInTheBlankQuestion | FreeResponseQuestion

export interface ChatMessage {
  id: string
  sender: "user" | "ai"
  text: string
  timestamp: Date
}

export interface GeneratedQuiz {
  id: string
  title: string
  description: string
  questions: QuizQuestion[]
  progress: number // 0-100
  currentQuestionIndex: number
  userAnswers: Record<string, string>
  questionResults: Record<string, boolean | null>
  groupId?: number // 测验分组ID，用于获取题目
}

export interface Note {
  id: string
  title: string
  content: ChatMessage[]
  tags: string[]
  createdAt: Date
  updatedAt: Date
  source: "chat" | "manual" | "article"
}

import {withSentryConfig} from '@sentry/nextjs';
/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-07-07 14:53:33
 * @FilePath: /tutoro-ai-front/next.config.mjs
 */
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  eslint: {
    ignoreDuringBuilds: true
  },
  typescript: {
    ignoreBuildErrors: true
  },
  images: {
    unoptimized: true
  },
  webpack: (config, { isServer }) => {
    // Handle PDF.js worker and canvas issues
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        canvas: false,
        fs: false,
        stream: false,
        util: false,
        path: false,
        assert: false,
        buffer: false,
        constants: false,
        crypto: false,
        domain: false,
        events: false,
        http: false,
        https: false,
        os: false,
        punycode: false,
        process: false,
        querystring: false,
        string_decoder: false,
        sys: false,
        timers: false,
        tty: false,
        url: false,
        zlib: false
      };
    }

    // Add externals for problematic modules
    config.externals = config.externals || [];
    config.externals.push({
      canvas: 'canvas'
    });

    // Ignore specific warnings from PDF.js
    config.ignoreWarnings = [
      /Failed to parse source map/,
      /Critical dependency: the request of a dependency is an expression/,
      /Module not found: Can't resolve 'canvas'/
    ];

    return config;
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        // destination: 'https://api.tutoroai.com/:path*',
        destination: 'https://tutoro-ai-api.catfoodworks.com/:path*'
      }
    ];
  },
  // 添加高级配置来处理超时
  env: {
    // 设置环境变量来延长超时
    TIMEOUT: '300000',
  }
};

export default withSentryConfig(nextConfig, {
// For all available options, see:
// https://www.npmjs.com/package/@sentry/webpack-plugin#options

org: "it-0k",
project: "tutoro-ai-front",

// Only print logs for uploading source maps in CI
silent: !process.env.CI,

// For all available options, see:
// https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

// Upload a larger set of source maps for prettier stack traces (increases build time)
widenClientFileUpload: true,

// Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
// This can increase your server load as well as your hosting bill.
// Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
// side errors will fail.
tunnelRoute: "/monitoring",

// Automatically tree-shake Sentry logger statements to reduce bundle size
disableLogger: true,

// Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
// See the following for more information:
// https://docs.sentry.io/product/crons/
// https://vercel.com/docs/cron-jobs
automaticVercelMonitors: true,
});
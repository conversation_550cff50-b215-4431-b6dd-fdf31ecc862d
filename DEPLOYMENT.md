# 📦 TutoroAI Frontend 部署指南

## 🚀 快速部署

### 1. 检查Docker环境
```bash
./check-docker.sh
```

### 2. 构建镜像
```bash
# 使用简化脚本（推荐）
./docker-build-simple.sh

# 或使用完整脚本
./docker-build.sh
```

### 3. 运行容器
```bash
docker run -p 3000:3000 tutoro-ai-front:latest
```

## 📁 部署文件

项目根目录中的部署相关文件：
- `docker-build.sh` - 完整Docker构建脚本
- `docker-build-simple.sh` - 简化Docker构建脚本
- `check-docker.sh` - Docker环境检查脚本
- `docker.env` - Docker环境变量配置
- `Dockerfile` - Docker构建配置

详细文档请查看：[deploy/README.md](./deploy/README.md)

## 🌐 访问应用

构建并运行成功后，访问：http://localhost:3000 
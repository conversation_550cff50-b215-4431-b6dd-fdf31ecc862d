# 自动发送调试指南

## 问题分析

从日志可以看出：
1. `setNewArticleChatData` 被调用了多次
2. 但是没有成功触发自动发送

## 修复内容

### 1. 修复了模型选择逻辑
- 现在会优先检查 store 中的 `initialSelectedModelId`
- 确保模型在自动发送前已正确设置

### 2. 修复了 contentData 依赖问题
- 对于新创建的内容，使用 fallback 数据 `{ type: 'doc' }`
- 不再等待 contentData 完全加载

### 3. 添加了详细的调试日志
- ArticlePage: 显示设置自动发送数据的过程
- Store: 显示 setNewArticleChatData 的调用和生成的数据
- ChatComponent: 显示自动发送检查的详细信息

## 调试步骤

1. **打开浏览器开发者工具**
2. **重新测试创建内容并自动发送**
3. **查看控制台日志，应该看到：**

```
📝 Setting auto-send data: {
  contentId: 77,
  fallbackContentData: { type: 'doc' },
  options: {
    initialInputValue: '我要学习C++',
    initialSelectedModelId: 'Tutoro-2.0',
    autoSend: true
  }
}

🏪 Store: setNewArticleChatData called: {
  contentId: 77,
  contentData: { type: 'doc' },
  options: { ... }
}

🏪 Store: Generated autoSendData: {
  initialInputValue: '我要学习C++',
  initialSelectedModelId: 'Tutoro-2.0',
  autoSend: true,
  type: 'doc',
  endpoint: null,
  includeUserMessage: true
}

🔍 Auto-send check: {
  contentId: 77,
  autoSendData: { ... },
  selectedModel: 'Tutoro-2.0',
  isLoading: false,
  messagesLength: 0
}

🚀 Auto-send triggered with store data: { ... }
```

## 可能的问题

如果仍然不工作，检查：

1. **模型是否正确加载**
   - 确保 `models` 数组不为空
   - 确保能找到对应的模型

2. **组件渲染时机**
   - ChatComponent 是否已经渲染
   - 是否在正确的 tab 中

3. **依赖数组**
   - useEffect 的依赖是否正确触发

## 下一步

如果问题仍然存在，请提供完整的控制台日志，我们可以进一步诊断问题。

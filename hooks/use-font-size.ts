"use client"

import { useState, useEffect, useCallback } from "react"

type FontSizeScale = "xs" | "sm" | "base" | "lg" | "xl"

interface FontSizeConfig {
  scale: FontSizeScale
  multiplier: number
}

const FONT_SCALES: Record<FontSizeScale, number> = {
  xs: 0.8,
  sm: 0.9,
  base: 1.0,
  lg: 1.1,
  xl: 1.2,
}

export function useFontSize() {
  const [currentScale, setCurrentScale] = useState<FontSizeScale>("base")
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    // Load saved font scale from localStorage
    const savedScale = localStorage.getItem("font-scale") as FontSizeScale
    if (savedScale && FONT_SCALES[savedScale]) {
      setCurrentScale(savedScale)
      applyFontScale(savedScale)
    }
  }, [])

  const applyFontScale = useCallback(
    (scale: FontSizeScale) => {
      if (!isClient) return

      const multiplier = FONT_SCALES[scale]
      const root = document.documentElement

      // Apply the font scale with smooth transition
      root.style.setProperty("--font-scale-user", multiplier.toString())

      // Update CSS custom property for user preference
      const currentScale = getComputedStyle(root).getPropertyValue("--font-scale").trim()
      const baseScale = Number.parseFloat(currentScale) || 1
      const finalScale = baseScale * multiplier

      root.style.setProperty("--font-scale-final", finalScale.toString())

      // Apply to all font size variables
      const fontSizes = ["xs", "sm", "base", "lg", "xl", "2xl", "3xl", "4xl", "5xl", "6xl"]

      fontSizes.forEach((size) => {
        const baseSizeMap: Record<string, string> = {
          xs: "0.75rem",
          sm: "0.875rem",
          base: "1rem",
          lg: "1.125rem",
          xl: "1.25rem",
          "2xl": "1.5rem",
          "3xl": "1.875rem",
          "4xl": "2.25rem",
          "5xl": "3rem",
          "6xl": "3.75rem",
        }

        const baseSize = baseSizeMap[size]
        if (baseSize) {
          root.style.setProperty(`--font-size-${size}`, `calc(${baseSize} * var(--font-scale) * ${multiplier})`)
        }
      })
    },
    [isClient],
  )

  const setFontScale = useCallback(
    (scale: FontSizeScale) => {
      setCurrentScale(scale)
      applyFontScale(scale)

      if (isClient) {
        localStorage.setItem("font-scale", scale)
      }
    },
    [applyFontScale, isClient],
  )

  const increaseFontSize = useCallback(() => {
    const scales: FontSizeScale[] = ["xs", "sm", "base", "lg", "xl"]
    const currentIndex = scales.indexOf(currentScale)
    if (currentIndex < scales.length - 1) {
      setFontScale(scales[currentIndex + 1])
    }
  }, [currentScale, setFontScale])

  const decreaseFontSize = useCallback(() => {
    const scales: FontSizeScale[] = ["xs", "sm", "base", "lg", "xl"]
    const currentIndex = scales.indexOf(currentScale)
    if (currentIndex > 0) {
      setFontScale(scales[currentIndex - 1])
    }
  }, [currentScale, setFontScale])

  const resetFontSize = useCallback(() => {
    setFontScale("base")
  }, [setFontScale])

  return {
    currentScale,
    setFontScale,
    increaseFontSize,
    decreaseFontSize,
    resetFontSize,
    availableScales: Object.keys(FONT_SCALES) as FontSizeScale[],
  }
}

import { useState, useEffect, useRef } from 'react';
import type { GeneratedQuiz } from '@/types';
import { useArticleCacheStore } from '@/lib/stores/article-cache-store';
import type { QuizType, QuizDifficulty, QuizPreferences } from '@/components/article/quiz/quiz-settings-dialog';

export type QuizStage = 'preferences' | 'generating' | 'list' | 'taking' | 'results';

const initialQuizPreferences: QuizPreferences = {
  types: ['multiple-choice'],
  difficulty: 'medium'
};

export function useQuizState(contentId: number) {
  // 测验阶段
  const [quizStage, setQuizStage] = useState<QuizStage>('preferences');
  // 测验首选项
  const [quizPreferences, setQuizPreferences] = useState<QuizPreferences>(initialQuizPreferences);
  // 生成的测验列表
  const [generatedQuizzes, setGeneratedQuizzes] = useState<GeneratedQuiz[]>([]);
  // 当前测验
  const [activeQuiz, setActiveQuiz] = useState<GeneratedQuiz | null>(null);
  // 当前问题索引
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  // 用户答案
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({});
  // 问题结果
  const [questionResults, setQuestionResults] = useState<Record<string, boolean | null>>({});
  // 配置弹窗状态
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);
  // 弹窗中的配置
  const [dialogQuizPreferences, setDialogQuizPreferences] = useState<QuizPreferences>(initialQuizPreferences);
  // 控制是否应该获取quiz groups
  const [shouldFetchQuizGroups, setShouldFetchQuizGroups] = useState(false);
  // 防止重复请求的 ref
  const hasTriggeredLoad = useRef(false);
  // 提交答案状态
  const [isAnswerSubmitting, setIsAnswerSubmitting] = useState(false);

  // 使用缓存store
  const { quizGroups: cachedQuizGroups, hasQuizGroups, setQuizGroups: setCachedQuizGroups } = useArticleCacheStore();

  // 获取当前内容的quiz groups
  const quizGroups = cachedQuizGroups[contentId] || [];

  // 当前问题
  const currentQuestion = activeQuiz?.questions[currentQuestionIndex];

  // 计算答案状态
  const isAnswered = currentQuestion
    ? questionResults[currentQuestion.id] !== undefined
    : false;

  // 修改 isCorrect 逻辑：只有明确为 true 时才是正确，跳过状态(null)不算错误
  const isCorrect = currentQuestion ? questionResults[currentQuestion.id] === true : false;

  // 初始化状态：根据 quizGroups 数据来设置初始阶段
  useEffect(() => {
    if (quizGroups && quizGroups.length > 0) {
      console.log('📋 QuizState: Setting up quizzes from cached data');
      const quizzes: GeneratedQuiz[] = quizGroups.map((group: any, index: number) => ({
        id: `quiz-${group.id || index + 1}`,
        title: group.title || `Quiz ${index + 1}`,
        description: group.title || `Quiz ${index + 1}`,
        questions: [],
        progress: parseFloat(group.progress || '0'),
        currentQuestionIndex: 0,
        userAnswers: {},
        questionResults: {},
        groupId: group.id
      }));
      setGeneratedQuizzes(quizzes);
      setQuizStage('list');
    } else if (hasQuizGroups(contentId)) {
      console.log('📋 QuizState: No quiz groups found, setting to preferences');
      setGeneratedQuizzes([]);
      setQuizStage('preferences');
    } else {
      console.log('📋 QuizState: No cached data, staying in preferences stage');
      setQuizStage('preferences');
    }
  }, [quizGroups, contentId, hasQuizGroups]);

  // 重置状态的辅助函数
  const resetQuizState = () => {
    setCurrentQuestionIndex(0);
    setUserAnswers({});
    setQuestionResults({});
    setIsAnswerSubmitting(false);
  };

  // 更新活跃测验
  const updateActiveQuiz = (quiz: GeneratedQuiz) => {
    setActiveQuiz(quiz);
    setGeneratedQuizzes((prev) => prev.map((q) => (q.id === quiz.id ? quiz : q)));
  };

  return {
    // 状态
    quizStage,
    quizPreferences,
    generatedQuizzes,
    activeQuiz,
    currentQuestionIndex,
    userAnswers,
    questionResults,
    isConfigDialogOpen,
    dialogQuizPreferences,
    shouldFetchQuizGroups,
    hasTriggeredLoad,
    isAnswerSubmitting,
    quizGroups,
    currentQuestion,
    isAnswered,
    isCorrect,

    // 状态更新函数
    setQuizStage,
    setQuizPreferences,
    setGeneratedQuizzes,
    setActiveQuiz,
    setCurrentQuestionIndex,
    setUserAnswers,
    setQuestionResults,
    setIsConfigDialogOpen,
    setDialogQuizPreferences,
    setShouldFetchQuizGroups,
    setIsAnswerSubmitting,
    setCachedQuizGroups,

    // 辅助函数
    resetQuizState,
    updateActiveQuiz,
    hasQuizGroups,

    // 常量
    initialQuizPreferences
  };
}
import { useEffect, useRef } from 'react';
import { toast } from 'sonner';
import {
  useGenerateQuizGroup,
  useUpdateQuizSettings,
  useGenerateQuizQuestions,
  useCreateQuizAnswer,
  useGetQuizGroups
} from '@/lib/api/hooks';
import { useTranslation } from '@/lib/translation-context';
import type { QuizQuestion, GeneratedQuiz, MultipleChoiceQuestion, FreeResponseQuestion } from '@/types';
import type { QuizPreferences } from '@/components/article/quiz/quiz-settings-dialog';
import type { QuizStage } from './use-quiz-state';
import { useAppStore } from '@/lib/stores';

interface UseQuizDataProps {
  contentId: number;
  shouldFetchQuizGroups: boolean;
  setShouldFetchQuizGroups: (value: boolean) => void;
  setCachedQuizGroups: (contentId: number, groups: any[]) => void;
  onLoadComplete?: () => void;
  setQuizStage: (stage: QuizStage) => void;
  setGeneratedQuizzes: React.Dispatch<React.SetStateAction<GeneratedQuiz[]>>;
  setActiveQuiz: React.Dispatch<React.SetStateAction<GeneratedQuiz | null>>;
  setCurrentQuestionIndex: (index: number) => void;
  setUserAnswers: (answers: Record<string, string>) => void;
  setQuestionResults: (results: Record<string, boolean | null>) => void;
  setIsAnswerSubmitting: (value: boolean) => void;
  updateActiveQuiz: (quiz: GeneratedQuiz) => void;
  activeQuiz: GeneratedQuiz | null;
  currentQuestionIndex: number;
  userAnswers: Record<string, string>;
  questionResults: Record<string, boolean | null>;
}

export function useQuizData({
  contentId,
  shouldFetchQuizGroups,
  setShouldFetchQuizGroups,
  setCachedQuizGroups,
  onLoadComplete,
  setQuizStage,
  setGeneratedQuizzes,
  setActiveQuiz,
  setCurrentQuestionIndex,
  setUserAnswers,
  setQuestionResults,
  setIsAnswerSubmitting,
  updateActiveQuiz,
  activeQuiz,
  currentQuestionIndex,
  userAnswers,
  questionResults
}: UseQuizDataProps) {
  const { t } = useTranslation();

  // 使用 zustand store 替代本地状态
  const { openUpgradeModal } = useAppStore();
  // 使用 ref 来稳定 onLoadComplete 回调
  const onLoadCompleteRef = useRef(onLoadComplete);
  onLoadCompleteRef.current = onLoadComplete;

  // 获取quiz groups的查询
  const {
    data: quizGroupsResponse,
    isLoading: isLoadingQuizGroups,
    error: quizGroupsError,
    refetch: refetchQuizGroups
  } = useGetQuizGroups(shouldFetchQuizGroups ? contentId : 0);

  // 当获取到quiz groups数据时，保存到缓存中
  useEffect(() => {
    if (quizGroupsResponse?.data && Array.isArray(quizGroupsResponse.data)) {
      console.log('Quiz groups fetched for contentId:', contentId);
      setCachedQuizGroups(contentId, quizGroupsResponse.data);
      onLoadCompleteRef.current?.();
      setShouldFetchQuizGroups(false);
    } else if (quizGroupsResponse && !quizGroupsResponse.data) {
      setCachedQuizGroups(contentId, []);
      onLoadCompleteRef.current?.();
      setShouldFetchQuizGroups(false);
    }
  }, [quizGroupsResponse, contentId, setCachedQuizGroups, setShouldFetchQuizGroups]);

  // 更新测验题型配置的 mutation
  const updateQuizSettingsMutation = useUpdateQuizSettings({
    onSuccess: (data) => {
      console.log('Quiz settings updated successfully:', data);
    },
    onError: (error) => {
      console.error('Failed to update quiz settings:', error);
      toast.error(t('article.quiz.settingsUpdateFailed'));
      setQuizStage('preferences');
    }
  });

  // 生成测验分组的 mutation
  const generateQuizGroupMutation = useGenerateQuizGroup({
    onSuccess: (data) => {
      console.log('Quiz group generated successfully:', data);
      setQuizStage('list');
      if (data?.data && Array.isArray(data.data)) {
        setCachedQuizGroups(contentId, data.data);
      } else {
        setCachedQuizGroups(contentId, []);
      }
    },
    onError: (error) => {
      console.error('Failed to generate quiz group:', error);
      toast.error(t('article.quiz.generateFailed'));
      setQuizStage('preferences');
    }
  });

  // 生成测验题目的 mutation
  const generateQuizQuestionsMutation = useGenerateQuizQuestions({
    onSuccess: (data) => {
      console.log('Quiz questions generated successfully:', data);
    },
    onError: (error) => {
      console.error('Failed to generate quiz questions:', error);
      toast.error('Question generation failed');
      setQuizStage('list');
    }
  });

  // 创建问题回答的 mutation
  const createQuizAnswerMutation = useCreateQuizAnswer({
    onSuccess: (data) => {
      console.log('Quiz answer created successfully:', data);
      if (data?.code === '30502') {
        openUpgradeModal();
        return;
      }
      if (data?.data && activeQuiz) {
        const isCorrect = data.data.correct || false;
        const currentQuestion = activeQuiz.questions[currentQuestionIndex];

        if (currentQuestion) {
          const newResults = { ...questionResults, [currentQuestion.id]: isCorrect };
          setQuestionResults(newResults);

          const updatedQuiz = { ...activeQuiz, questionResults: newResults };
          updateActiveQuiz(updatedQuiz);
        }
      }
    },
    onError: (error) => {
      console.error('Failed to create quiz answer:', error);
      toast.error('提交回答失败');
    }
  });

  // 数据转换函数
  const transformApiQuestionToQuizQuestion = (apiQuestion: any): QuizQuestion => {
    const baseQuestion = {
      id: apiQuestion.id?.toString() || '',
      question: apiQuestion.question || '',
      correctAnswer: apiQuestion.answer || '',
      explanation: apiQuestion.explanation || '',
      hint: '',
      pageReference: ''
    };

    if (apiQuestion.quizType === 'multiple_choice' && apiQuestion.options) {
      const correctOption = apiQuestion.options.find((opt: any) => opt.correct);
      const correctAnswer = correctOption?.option || '';

      return {
        ...baseQuestion,
        type: 'multiple-choice',
        options: apiQuestion.options.map((opt: any) => opt.option),
        correctAnswer
      } as MultipleChoiceQuestion;
    } else if (apiQuestion.quizType === 'free_responses') {
      return {
        ...baseQuestion,
        type: 'free-response'
      } as FreeResponseQuestion;
    } else {
      return {
        ...baseQuestion,
        type: 'multiple-choice',
        options: []
      } as MultipleChoiceQuestion;
    }
  };

  // 获取题目的函数
  const fetchQuestionsForQuiz = async (quiz: GeneratedQuiz, reset: boolean = false) => {
    if (!quiz.groupId) {
      toast.error('测验分组ID不存在');
      setQuizStage('list');
      return;
    }

    try {
      console.log('Generating questions for group:', quiz.groupId, 'with reset:', reset);
      await generateQuizQuestionsMutation.mutateAsync({
        groupId: quiz.groupId,
        reset: reset ? 'true' : undefined,
        data: {}
      });

      console.log('Fetching questions for group:', quiz.groupId);
      const wendangneirongceyan = await import('@/servers/api/wendangneirongceyan');
      const response = await wendangneirongceyan.getLlmQuizQuestionListQuestionGroupId({
        groupId: quiz.groupId
      });

      if (response?.data && Array.isArray(response.data)) {
        const questions: QuizQuestion[] = response.data.map(transformApiQuestionToQuizQuestion);

        const updatedQuiz = { ...quiz, questions };
        setGeneratedQuizzes((prev: GeneratedQuiz[]) =>
          prev.map((q: GeneratedQuiz) => (q.id === quiz.id ? updatedQuiz : q))
        );

        setActiveQuiz(updatedQuiz);
        setCurrentQuestionIndex(0);
        setUserAnswers({});
        setQuestionResults({});
        setQuizStage('taking');
      } else {
        throw new Error('No questions returned');
      }
    } catch (error) {
      console.error('Failed to fetch questions:', error);
      toast.error('Failed to get the title');
      setQuizStage('list');
    }
  };

  // 生成测验的函数
  const handleGenerateQuiz = (preferences: QuizPreferences) => {
    if (!contentId) {
      toast.error(t('article.quiz.noContentId'));
      return;
    }

    setQuizStage('generating');

    const quizTypes = preferences.types.map((type) =>
      type === 'multiple-choice' ? 'multiple_choice' : 'free_responses'
    );

    // 链式调用：先更新设置，再生成分组
    updateQuizSettingsMutation.mutate(
      {
        contentId,
        data: {
          quizType: quizTypes,
          QuizDifficulty: [preferences.difficulty]
        }
      },
      {
        onSuccess: () => {
          generateQuizGroupMutation.mutate({
            contentId,
            data: {
              quizTypes,
              difficulty: preferences.difficulty
            }
          });
        }
      }
    );
  };

  // 处理答案选择
  const handleAnswerSelection = (answer: string) => {
    if (!activeQuiz) {
      console.log('❌ No activeQuiz, returning early');
      return;
    }

    const currentQuestion = activeQuiz.questions[currentQuestionIndex];

    if (!currentQuestion) {
      console.log('❌ No currentQuestion, returning early');
      return;
    }

    if (questionResults[currentQuestion.id] !== undefined) {
      console.log(
        '❌ Question already answered, returning early. Current result:',
        questionResults[currentQuestion.id]
      );
      return;
    }

    // 检查是否为跳过操作（空字符串）
    if (answer === '') {
      console.log('🚀 ~ handleAnswerSelection ~ Skipping question');
      const newAnswers = { ...userAnswers, [currentQuestion.id]: '' };
      setUserAnswers(newAnswers);

      // 设置跳过状态 (使用 null 表示跳过)
      const newResults = { ...questionResults, [currentQuestion.id]: null };
      setQuestionResults(newResults);

      const updatedQuiz = { ...activeQuiz, userAnswers: newAnswers, questionResults: newResults };
      updateActiveQuiz(updatedQuiz);
      return;
    }

    setIsAnswerSubmitting(true);
    const newAnswers = { ...userAnswers, [currentQuestion.id]: answer };
    setUserAnswers(newAnswers);

    console.log('🚀 ~ handleAnswerSelection ~ newAnswers:', newAnswers);

    const questionId = parseInt(currentQuestion.id);
    console.log('🚀 ~ handleAnswerSelection ~ questionId parsed:', questionId, 'from:', currentQuestion.id);

    if (isNaN(questionId)) {
      console.log('❌ Invalid questionId, returning early');
      toast.error('题目ID无效');
      setIsAnswerSubmitting(false);
      return;
    }

    let apiAnswer = answer;
    if (currentQuestion.type === 'multiple-choice') {
      const question = currentQuestion as MultipleChoiceQuestion;
      const optionIndex = question.options.findIndex((option) => option === answer);
      console.log('🚀 ~ handleAnswerSelection ~ optionIndex:', optionIndex, 'for answer:', answer);
      if (optionIndex !== -1) {
        apiAnswer = (optionIndex + 1).toString();
      }
    }

    createQuizAnswerMutation.mutate(
      {
        quizQuestionId: questionId,
        answer: apiAnswer
      },
      {
        onSettled: () => {
          console.log('🚀 ~ handleAnswerSelection ~ API call settled');
          setIsAnswerSubmitting(false);
        },
        onError: (error) => {
          console.error('Failed to create quiz answer:', error);
          toast.error('提交回答失败');
        }
      }
    );

    setActiveQuiz((prev: GeneratedQuiz | null) => (prev ? { ...prev, userAnswers: newAnswers } : null));
  };

  return {
    // API状态
    isLoadingQuizGroups,
    isGenerating: updateQuizSettingsMutation.isPending || generateQuizGroupMutation.isPending,

    // 操作函数
    handleGenerateQuiz,
    handleAnswerSelection,
    fetchQuestionsForQuiz,

    // mutations
    generateQuizGroupMutation,
    updateQuizSettingsMutation,
    createQuizAnswerMutation
  };
}

#!/bin/bash
###
 # @Date: 2025-06-11 21:49:19
 # @LastEditors: yosan
 # @LastEditTime: 2025-06-11 21:58:38
 # @FilePath: /tutoro-ai-front/deploy/check-docker.sh
### 

echo "🔍 检查Docker安装状态..."

# 检查docker命令是否可用
if command -v docker >/dev/null 2>&1; then
    echo "✅ Docker 命令已安装"
    
    # 检查Docker是否运行
    if docker info >/dev/null 2>&1; then
        echo "✅ Docker 引擎正在运行"
        echo "📋 Docker 版本信息："
        docker --version
        echo ""
        echo "🎉 Docker 安装完成！现在可以构建镜像了。"
        echo "🚀 运行以下命令开始构建："
        echo "   ./docker-build.sh"
    else
        echo "⚠️  Docker 已安装但未运行"
        echo "💡 请启动 Docker Desktop 应用程序"
        echo "   1. 打开 Applications 文件夹"
        echo "   2. 启动 Docker.app"
        echo "   3. 等待Docker引擎启动完成"
        echo "   4. 再次运行此脚本检查"
    fi
else
    echo "❌ Docker 未安装或未在PATH中"
    echo "💡 安装建议："
    echo "   brew install --cask docker"
    echo "   或从官网下载：https://www.docker.com/products/docker-desktop"
fi 
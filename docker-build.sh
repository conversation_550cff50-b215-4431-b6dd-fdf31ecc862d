#!/bin/bash
###
 # @Date: 2025-06-11 21:42:27
 # @LastEditors: yosan
 # @LastEditTime: 2025-06-12 12:55:47
 # @FilePath: /tutoro-ai-front/docker-build.sh
### 

# TutoroAI Frontend Docker 构建脚本
echo "🚀 开始构建 TutoroAI Frontend Docker 镜像..."

# 设置变量
IMAGE_NAME="tutoro-ai-front"
IMAGE_TAG="latest"
TAR_FILE="tutoro-ai-front.tar"

# 清理之前的镜像（可选）
echo "📦 清理旧镜像..."
docker rmi ${IMAGE_NAME}:${IMAGE_TAG} 2>/dev/null || true

# 构建 Docker 镜像 (针对 Linux x64 平台)
echo "🔨 构建 Docker 镜像 (Linux x64)..."
docker build \
  --platform linux/amd64 \
  --build-arg NEXT_PUBLIC_API_BASE_URL=https://api.tutoroai.com \
  --build-arg NODE_ENV=production \
  --build-arg NEXT_TELEMETRY_DISABLED=1 \
  -t ${IMAGE_NAME}:${IMAGE_TAG} \
  .

# 检查构建是否成功
if [ $? -eq 0 ]; then
  echo "✅ Docker 镜像构建成功！"
  
  # 显示镜像信息
  echo "📋 镜像信息："
  docker images ${IMAGE_NAME}:${IMAGE_TAG}
  
    # 保存镜像为 tar 文件
  echo "💾 保存镜像为 tar 文件..."
  docker save -o ${TAR_FILE} ${IMAGE_NAME}:${IMAGE_TAG}
  
  if [ $? -eq 0 ]; then
    echo "✅ 镜像已保存为: ${TAR_FILE}"
    echo "📏 文件大小:"
    ls -lh ${TAR_FILE}
  else
    echo "❌ 保存镜像文件失败"
    exit 1
  fi
else
  echo "❌ Docker 镜像构建失败"
  exit 1
fi

echo ""
echo "🎉 构建完成！"
echo "📦 Docker 镜像: ${IMAGE_NAME}:${IMAGE_TAG} (Linux x64)"
echo "📁 镜像文件: ${TAR_FILE}"
echo ""
echo "🚀 在 Linux x64 服务器上运行容器命令:"
echo "   docker run -p 3000:3000 ${IMAGE_NAME}:${IMAGE_TAG}"
echo ""
echo "📥 在 Linux x64 服务器上导入镜像命令:"
echo "   docker load -i ${TAR_FILE}" 
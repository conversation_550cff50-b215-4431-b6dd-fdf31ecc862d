version: '3.8'

services:
  tutoro-ai-front:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_BASE_URL=https://api.tutoroai.com
        - NODE_ENV=production
        - NEXT_TELEMETRY_DISABLED=1
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - NEXT_PUBLIC_API_BASE_URL=https://api.tutoroai.com
    restart: unless-stopped
    container_name: tutoro-ai-frontend
    volumes:
      - /app/node_modules
    networks:
      - tutoro-network

networks:
  tutoro-network:
    driver: bridge 
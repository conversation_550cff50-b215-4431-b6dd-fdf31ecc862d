import { NextRequest, NextResponse } from 'next/server';

// 导入优化后的工具模块
import {
  detectAndProcessLocale,
  type LocaleDetectionResult
} from './lib/middleware/locale-detector';
import {
  checkAuthenticationStatus,
  checkRoutePermissions,
  shouldSkipMiddleware,
  type AuthCheckResult,
  type RoutePermissionResult
} from './lib/middleware/auth-checker';
import {
  createRedirectResponse,
  createRewriteResponse,
  createEnhancedResponse,
  createLocaleRedirectResponse,
  enhanceRewriteResponse,
  createDebugInfo
} from './lib/middleware/response-utils';











/**
 * 优化后的中间件主函数
 * @param request Next.js请求对象
 * @returns 响应对象
 */
export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // 跳过静态文件和API路由
  if (shouldSkipMiddleware(pathname)) {
    return NextResponse.next();
  }

  // 检测和处理语言
  const localeResult: LocaleDetectionResult = detectAndProcessLocale(request);
  const { locale, routePath, hasLocale, shouldRewrite } = localeResult;

  // 检查认证状态
  const authResult: AuthCheckResult = checkAuthenticationStatus(request);
  const { isAuthenticated, authSource } = authResult;

  // 开发环境调试信息
  if (process.env.NODE_ENV === 'development') {
    const debugInfo = createDebugInfo(request, locale, isAuthenticated, authSource);
    console.log(`[Middleware] ${debugInfo.timestamp}`);
    console.log(`  Path: ${pathname} -> Route: ${routePath}`);
    console.log(`  Locale: ${locale} (hasLocale: ${hasLocale}, shouldRewrite: ${shouldRewrite})`);
    console.log(`  Auth: ${isAuthenticated} (source: ${authSource})`);
  }

  // 检查路由权限
  const permissionResult: RoutePermissionResult = checkRoutePermissions(routePath, isAuthenticated);
  const { shouldRedirect, redirectTo, reason } = permissionResult;

  if (shouldRedirect && redirectTo) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`  Redirecting: ${reason} -> ${redirectTo}`);
    }
    return createRedirectResponse(request, redirectTo, locale, pathname);
  }

  // 如果需要重写（默认语言不显示前缀）
  if (shouldRewrite) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`  Rewriting for default locale: ${locale}`);
    }
    const response = createRewriteResponse(request, locale);
    return enhanceRewriteResponse(response, locale, isAuthenticated, authSource);
  }

  // 如果需要添加locale（非默认语言），进行重定向
  if (!hasLocale) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`  Adding locale: ${locale}`);
    }
    return createLocaleRedirectResponse(request, locale);
  }

  // 返回增强的响应
  return createEnhancedResponse(locale, isAuthenticated, authSource);
}

export const config = {
  matcher: [
    // 匹配所有路径，除了内部Next.js路径和静态文件
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|.*\\..*).*)'
  ]
};

'use client';

import { createContext, useContext, type ReactNode } from 'react';
import type { Locale } from '@/i18n.config';

type Dictionary = any; // Type this properly based on your dictionary structure

interface TranslationContextType {
  locale: Locale;
  dictionary: Dictionary;
  t: (key: string) => string;
}

const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

interface TranslationProviderProps {
  children: ReactNode;
  locale: Locale;
  dictionary: Dictionary;
}

export function TranslationProvider({ children, locale, dictionary }: TranslationProviderProps) {
  const t = (key: string): string => {
    const keys = key.split('.');
    let value = dictionary;

    for (const k of keys) {
      if (value[k] === undefined) {
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
      value = value[k];
    }

    return typeof value === 'string' ? value : key;
  };

  return (
    <TranslationContext.Provider value={{ locale, dictionary, t }}>
      {children}
    </TranslationContext.Provider>
  );
}

export function useTranslation() {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
} 
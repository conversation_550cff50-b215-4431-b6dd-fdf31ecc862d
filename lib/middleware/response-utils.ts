/**
 * 中间件响应处理工具
 */
import { NextRequest, NextResponse } from 'next/server';

/**
 * 创建重定向响应
 * @param request Next.js请求对象
 * @param redirectPath 重定向路径
 * @param locale 语言代码
 * @param originalPath 原始路径（用于登录后重定向）
 * @returns 重定向响应
 */
export function createRedirectResponse(
  request: NextRequest,
  redirectPath: string,
  locale: string,
  originalPath?: string
): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = `/${locale}${redirectPath}`;

  // 如果是重定向到登录页，保存原始路径
  if (redirectPath === '/login' && originalPath && originalPath !== '/login') {
    url.searchParams.set('redirect', originalPath);
  }

  return NextResponse.redirect(url);
}

/**
 * 创建重写响应（用于默认语言）
 * @param request Next.js请求对象
 * @param locale 语言代码
 * @returns 重写响应
 */
export function createRewriteResponse(
  request: NextRequest,
  locale: string
): NextResponse {
  const url = request.nextUrl.clone();
  // 重写到 /en/xxx 但不改变用户看到的URL
  url.pathname = `/${locale}${url.pathname}`;
  return NextResponse.rewrite(url);
}

/**
 * 创建增强的Next响应（添加自定义头部）
 * @param locale 当前语言
 * @param isAuthenticated 是否已认证
 * @param authSource 认证来源
 * @returns 增强的响应
 */
export function createEnhancedResponse(
  locale: string, 
  isAuthenticated: boolean, 
  authSource: string
): NextResponse {
  const response = NextResponse.next();

  // 设置响应头供客户端使用
  response.headers.set('x-user-authenticated', isAuthenticated ? 'true' : 'false');
  response.headers.set('x-current-locale', locale);
  response.headers.set('x-auth-source', authSource);

  // 设置语言偏好cookie（如果没有的话）
  if (!response.cookies.get('preferred_locale')) {
    response.cookies.set('preferred_locale', locale, {
      path: '/',
      maxAge: 365 * 24 * 60 * 60, // 1年
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    });
  }

  return response;
}

/**
 * 创建语言重定向响应
 * @param request Next.js请求对象
 * @param locale 目标语言
 * @returns 语言重定向响应
 */
export function createLocaleRedirectResponse(
  request: NextRequest,
  locale: string
): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = `/${locale}${url.pathname}`;
  return NextResponse.redirect(url);
}

/**
 * 为重写响应添加头部信息
 * @param response 重写响应
 * @param locale 当前语言
 * @param isAuthenticated 是否已认证
 * @param authSource 认证来源
 * @returns 增强的重写响应
 */
export function enhanceRewriteResponse(
  response: NextResponse,
  locale: string,
  isAuthenticated: boolean,
  authSource: string
): NextResponse {
  // 设置响应头
  response.headers.set('x-user-authenticated', isAuthenticated ? 'true' : 'false');
  response.headers.set('x-current-locale', locale);
  response.headers.set('x-auth-source', authSource);
  
  // 设置语言偏好cookie
  if (!response.cookies.get('preferred_locale')) {
    response.cookies.set('preferred_locale', locale, {
      path: '/',
      maxAge: 365 * 24 * 60 * 60, // 1年
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    });
  }
  
  return response;
}

/**
 * 创建API错误响应
 * @param message 错误消息
 * @param status 状态码
 * @returns 错误响应
 */
export function createErrorResponse(message: string, status: number = 400): NextResponse {
  return NextResponse.json(
    { error: message },
    { status }
  );
}

/**
 * 创建成功响应
 * @param data 响应数据
 * @param status 状态码
 * @returns 成功响应
 */
export function createSuccessResponse(data: any, status: number = 200): NextResponse {
  return NextResponse.json(data, { status });
}

/**
 * 检查是否为AJAX请求
 * @param request Next.js请求对象
 * @returns 是否为AJAX请求
 */
export function isAjaxRequest(request: NextRequest): boolean {
  const contentType = request.headers.get('content-type');
  const accept = request.headers.get('accept');
  const xRequestedWith = request.headers.get('x-requested-with');
  
  return !!(
    contentType?.includes('application/json') ||
    accept?.includes('application/json') ||
    xRequestedWith === 'XMLHttpRequest'
  );
}

/**
 * 获取客户端IP地址
 * @param request Next.js请求对象
 * @returns IP地址
 */
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return 'unknown';
}

/**
 * 获取用户代理信息
 * @param request Next.js请求对象
 * @returns 用户代理字符串
 */
export function getUserAgent(request: NextRequest): string {
  return request.headers.get('user-agent') || 'unknown';
}

/**
 * 检查是否为移动设备
 * @param request Next.js请求对象
 * @returns 是否为移动设备
 */
export function isMobileDevice(request: NextRequest): boolean {
  const userAgent = getUserAgent(request).toLowerCase();
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'tablet'];
  
  return mobileKeywords.some(keyword => userAgent.includes(keyword));
}

/**
 * 创建调试信息对象
 * @param request Next.js请求对象
 * @param locale 当前语言
 * @param isAuthenticated 是否已认证
 * @param authSource 认证来源
 * @returns 调试信息
 */
export function createDebugInfo(
  request: NextRequest,
  locale: string,
  isAuthenticated: boolean,
  authSource: string
) {
  return {
    timestamp: new Date().toISOString(),
    pathname: request.nextUrl.pathname,
    locale,
    isAuthenticated,
    authSource,
    userAgent: getUserAgent(request),
    ip: getClientIP(request),
    isMobile: isMobileDevice(request)
  };
}

/**
 * 认证状态检查工具
 */
import { NextRequest } from 'next/server';

// 公开可访问的路由（未登录用户可以访问）
export const PUBLIC_ROUTES = [
  '/', // 首页
  '/login', // 登录页
  '/contact', // 联系我们
  '/guide', // 引导页
  '/terms-of-service', // 服务条款
  '/privacy-policy', // 隐私政策
  '/pricing', // 费用
  '/invite', // 邀请
  '/forgot-password', // 忘记密码
];

// 认证相关的路由（已登录用户不应该访问）
export const AUTH_ROUTES = ['/login'];

// 始终需要认证的路由
export const PROTECTED_ROUTES = [
  '/profile', 
  '/exam', 
  '/history', 
  '/notes', 
  '/invite', 
  '/pricing', 
  '/space', 
  '/demo'
];

/**
 * 认证状态检查结果
 */
export interface AuthCheckResult {
  isAuthenticated: boolean;
  authSource: 'cookie' | 'header' | 'none';
}

/**
 * 路由权限检查结果
 */
export interface RoutePermissionResult {
  shouldRedirect: boolean;
  redirectTo?: string;
  reason?: string;
}

/**
 * 检查用户认证状态（优化版 - 只检查登录状态，不处理token）
 * @param request Next.js请求对象
 * @returns 认证状态检查结果
 */
export function checkAuthenticationStatus(request: NextRequest): AuthCheckResult {
  // 检查cookie中的登录状态（不再检查token）
  const cookieAuth = request.cookies.get('isAuthenticated')?.value;
  if (cookieAuth === 'true') {
    return {
      isAuthenticated: true,
      authSource: 'cookie'
    };
  }

  // 检查Authorization header（用于API调用）
  const authHeader = request.headers.get('authorization');
  if (authHeader?.startsWith('Bearer ')) {
    return {
      isAuthenticated: true,
      authSource: 'header'
    };
  }

  return { isAuthenticated: false, authSource: 'none' };
}

/**
 * 检查路由权限
 * @param routePath 路由路径
 * @param isAuthenticated 是否已认证
 * @returns 路由权限检查结果
 */
export function checkRoutePermissions(
  routePath: string,
  isAuthenticated: boolean
): RoutePermissionResult {
  // 已登录用户访问认证路由（如登录页）
  if (isAuthenticated && isAuthRoute(routePath)) {
    return {
      shouldRedirect: true,
      redirectTo: '/',
      reason: 'authenticated_user_accessing_auth_route'
    };
  }

  // 未登录用户访问受保护路由
  if (!isAuthenticated) {
    const isPublic = isPublicRoute(routePath);
    const isProtected = isProtectedRoute(routePath);

    if (!isPublic && isProtected) {
      return {
        shouldRedirect: true,
        redirectTo: '/login',
        reason: 'unauthenticated_user_accessing_protected_route'
      };
    }
  }

  return { shouldRedirect: false };
}

/**
 * 检查是否为公开路由
 * @param routePath 路由路径
 * @returns 是否为公开路由
 */
export function isPublicRoute(routePath: string): boolean {
  return PUBLIC_ROUTES.some((route) => {
    if (route === '/') return routePath === '/';
    return routePath.startsWith(route);
  });
}

/**
 * 检查是否为认证路由
 * @param routePath 路由路径
 * @returns 是否为认证路由
 */
export function isAuthRoute(routePath: string): boolean {
  return AUTH_ROUTES.some((route) => routePath.startsWith(route));
}

/**
 * 检查是否为受保护路由
 * @param routePath 路由路径
 * @returns 是否为受保护路由
 */
export function isProtectedRoute(routePath: string): boolean {
  return PROTECTED_ROUTES.some((route) => routePath.startsWith(route));
}

/**
 * 获取路由类型
 * @param routePath 路由路径
 * @returns 路由类型
 */
export function getRouteType(routePath: string): 'public' | 'auth' | 'protected' | 'unknown' {
  if (isPublicRoute(routePath)) return 'public';
  if (isAuthRoute(routePath)) return 'auth';
  if (isProtectedRoute(routePath)) return 'protected';
  return 'unknown';
}

/**
 * 检查路径是否需要跳过中间件处理
 * @param pathname 路径
 * @returns 是否需要跳过
 */
export function shouldSkipMiddleware(pathname: string): boolean {
  return (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.') ||
    pathname.startsWith('/favicon') ||
    pathname.startsWith('/robots.txt') ||
    pathname.startsWith('/sitemap')
  );
}

/**
 * 验证重定向路径的安全性
 * @param redirectPath 重定向路径
 * @returns 是否为安全的重定向路径
 */
export function isSafeRedirectPath(redirectPath: string): boolean {
  // 防止开放重定向攻击
  if (redirectPath.startsWith('http://') || redirectPath.startsWith('https://')) {
    return false;
  }
  
  // 确保是相对路径
  return redirectPath.startsWith('/');
}

/**
 * 标准化路由路径
 * @param path 原始路径
 * @returns 标准化后的路径
 */
export function normalizePath(path: string): string {
  // 移除末尾的斜杠（除了根路径）
  if (path.length > 1 && path.endsWith('/')) {
    return path.slice(0, -1);
  }
  return path;
}

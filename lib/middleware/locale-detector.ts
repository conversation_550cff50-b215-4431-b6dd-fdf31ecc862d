/**
 * 语言检测和处理工具
 */
import { NextRequest } from 'next/server';
import { locales, defaultLocale } from '@/i18n.config';

// 地理位置到语言的映射
const COUNTRY_LOCALE_MAP = {
  // 繁体中文地区
  HK: 'zh-HK',
  TW: 'zh-HK', 
  MO: 'zh-HK',
  // 简体中文地区
  CN: 'zh',
  SG: 'zh'
} as const;

/**
 * 语言检测结果
 */
export interface LocaleDetectionResult {
  locale: string;
  pathname: string;
  routePath: string;
  hasLocale: boolean;
  shouldRewrite: boolean;
}

/**
 * 检测和处理语言
 * @param request Next.js请求对象
 * @returns 语言检测结果
 */
export function detectAndProcessLocale(request: NextRequest): LocaleDetectionResult {
  const pathname = request.nextUrl.pathname;

  // 检查URL中是否已经包含locale
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (pathnameHasLocale) {
    const locale = pathname.split('/')[1];
    const routePath = pathname.replace(`/${locale}`, '') || '/';
    return { 
      locale, 
      pathname, 
      routePath, 
      hasLocale: true, 
      shouldRewrite: false 
    };
  }

  // 获取用户首选语言
  const locale = getUserPreferredLocale(request);
  
  // 如果是默认语言（en）且是根路径或根路径下的路由，不需要重定向，只需要重写
  if (locale === defaultLocale) {
    return {
      locale,
      pathname,
      routePath: pathname,
      hasLocale: false,
      shouldRewrite: true // 标记需要重写而不是重定向
    };
  }
  
  return {
    locale,
    pathname,
    routePath: pathname,
    hasLocale: false,
    shouldRewrite: false
  };
}

/**
 * 获取用户首选语言（优化版）
 * @param request Next.js请求对象
 * @returns 用户首选语言
 */
export function getUserPreferredLocale(request: NextRequest): string {
  // 0. 从 headers 获取 IP 地理位置
  const ipCountry = request.headers.get('x-vercel-ip-country') || 
                   request.headers.get('CF-IPCountry');
  const country = ipCountry?.toUpperCase() as keyof typeof COUNTRY_LOCALE_MAP;

  // 1. 优先检查请求头中的语言偏好（客户端设置）
  const preferredLocale = request.headers.get('x-preferred-locale');
  if (preferredLocale && locales.includes(preferredLocale as any)) {
    return preferredLocale;
  }

  // 2. 检查cookie中的语言偏好
  const cookieLocale = request.cookies.get('preferred_locale')?.value;
  if (cookieLocale && locales.includes(cookieLocale as any)) {
    return cookieLocale;
  }

  // 3. 从Accept-Language头获取首选语言，并结合地理位置
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const detectedLocale = parseAcceptLanguage(acceptLanguage, country);
    if (detectedLocale) {
      return detectedLocale;
    }
  }

  // 4. 根据地理位置进行回退
  if (country && country in COUNTRY_LOCALE_MAP) {
    return COUNTRY_LOCALE_MAP[country];
  }

  return defaultLocale;
}

/**
 * 解析Accept-Language头（优化版）
 * @param acceptLanguage Accept-Language头的值
 * @param country 用户所在国家代码
 * @returns 检测到的语言或null
 */
export function parseAcceptLanguage(
  acceptLanguage: string, 
  country?: string
): string | null {
  const languages = acceptLanguage
    .split(',')
    .map((lang) => {
      const [code, quality = '1'] = lang.trim().split(';q=');
      return { code: code.trim(), quality: parseFloat(quality) };
    })
    .sort((a, b) => b.quality - a.quality);

  for (const { code } of languages) {
    // 精确匹配
    if (locales.includes(code as any)) {
      return code;
    }

    // 处理变体语言
    if (code.startsWith('zh-')) {
      if (code.includes('HK') || code.includes('TW') || code.includes('Hant')) {
        return 'zh-HK';
      }
      return 'zh';
    }

    // 处理基础语言代码
    const baseCode = code.split('-')[0];
    if (baseCode === 'zh') {
      // 结合国家代码判断
      if (country && ['HK', 'TW', 'MO'].includes(country)) {
        return 'zh-HK';
      }
      return 'zh';
    }
    if (baseCode === 'en') return 'en';
  }

  return null;
}

/**
 * 检查路径是否包含语言前缀
 * @param pathname 路径
 * @returns 是否包含语言前缀
 */
export function hasLocalePrefix(pathname: string): boolean {
  return locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );
}

/**
 * 从路径中提取语言和路由
 * @param pathname 包含语言前缀的路径
 * @returns 语言和路由信息
 */
export function extractLocaleFromPath(pathname: string): {
  locale: string;
  routePath: string;
} {
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  if (locales.includes(firstSegment as any)) {
    return {
      locale: firstSegment,
      routePath: '/' + segments.slice(1).join('/') || '/'
    };
  }
  
  return {
    locale: defaultLocale,
    routePath: pathname
  };
}

/**
 * 构建带语言前缀的路径
 * @param locale 语言代码
 * @param path 路径
 * @returns 完整路径
 */
export function buildLocalizedPath(locale: string, path: string): string {
  // 如果是默认语言，不添加前缀
  if (locale === defaultLocale) {
    return path;
  }
  
  // 确保路径以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `/${locale}${normalizedPath}`;
}

/**
 * 验证语言代码是否有效
 * @param locale 语言代码
 * @returns 是否有效
 */
export function isValidLocale(locale: string): boolean {
  return locales.includes(locale as any);
}

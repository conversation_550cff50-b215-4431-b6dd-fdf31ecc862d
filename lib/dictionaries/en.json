{"common": {"generating": "Generating...", "back": "Back", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "create": "Create", "close": "Close", "functionUnderDevelopment": "Function Under Development", "error": "An error occurred, please try again later."}, "sidebar": {"home": "Home", "addContent": "Add Content", "history": "History", "recentActivity": "Recent Activity", "spaces": "Spaces", "addSpace": "Add Space", "space": "Space", "helpAndTools": "Help & Tools", "feedback": "<PERSON><PERSON><PERSON>", "chat": "AI Assistant", "quickGuide": "Quick Guide", "chromeExtension": "Chrome Extension", "discordServer": "Discord Server", "inviteAndEarn": "Invite & Earn", "freePlan": "Free Plan", "profile": "Profile", "upgradePlan": "Upgrade Plan", "billingSettings": "Billing <PERSON>s", "logout": "Logout", "collapse": "Collapse Sidebar", "expand": "Expand Sidebar", "personalCenter": "Personal Center", "fees": "Fees", "contact": "Contact Us", "terms": "Terms of Service", "privacy": "Privacy Policy", "noHistory": "No Recent Activity", "showMore": "Show More", "showLess": "Show Less", "notes": "Notes", "welcomeTitle": "Welcome to TutoroAI", "welcomeDescription": "Your personalized AI tutor.", "welcomeDetails": "Understand documents, YouTube videos, website links, or recorded lectures through key concepts, engaging learning tools (like flashcards), and interactive conversations.", "welcomeFeedback": "We are constantly improving the platform, and if you have any feedback, we would love to hear it.", "loginPrompt": "Login to continue", "loginButton": "<PERSON><PERSON>", "logoutSuccess": "Successfully logged out", "proPlan": "Pro Plan"}, "header": {"search": "Search", "login": "<PERSON><PERSON>", "upgrade": "Upgrade"}, "mainContent": {"welcomeTitle": "What would you like to learn today?", "uploadTitle": "Upload", "uploadDesc": "Files, Audio, Video", "pasteTitle": "Paste", "pasteDesc": "YouTube, Website, Text", "recordTitle": "Record", "recordDesc": "Course, Video Call", "askPlaceholder": "Or ask AI tutor", "mySpaces": "My Spaces", "exploreTopics": "Explore Topics", "programming": "Programming", "learningMethods": "Learning Methods", "chemistry": "Chemistry", "meetSection": "Meet", "continueLearning": "Continue Learning", "viewAll": "View All", "search": "Search", "addSpace": "Add Space", "addingSpace": "Creating...", "history": "History", "noHistory": "No History", "yourHistoryIsEmpty": "Your history is empty.", "publishFirstContent": "Publish First Content", "publishNewContent": "Publish New Content", "shareYourLearning": "Share your learning心得、问题或发现...", "image": "Image", "video": "Video", "document": "Document", "cancel": "Cancel", "publish": "Publish"}, "historyPage": {"title": "History", "search": {"placeholder": "Search history records..."}, "filters": {"all": "All Types", "article": "Articles", "exam": "<PERSON><PERSON>", "practice": "Practice", "other": "Other"}, "loading": "Loading...", "error": "Failed to load history records", "retry": "Retry", "noTitle": "Untitled", "empty": {"title": "No History Records", "description": "Your history is empty."}, "pagination": {"total": "Total {{total}} records, page {{current}} of {{pages}}", "previous": "Previous", "next": "Next"}}, "spacePage": {"noDescription": "No description", "chat": "Space Chat", "viewResults": "View Results", "items": "Items", "newSpaceTitle": "Untitled Space", "noContent": "No Content", "deleteSpace": "Delete Space", "deleteSpaceSuccess": "Space deleted successfully", "deleteSpaceWithContent": "Space deleted successfully. {{count}} related content(s) also removed.", "deleteSpaceFailed": "Failed to delete space", "updateSpaceSuccess": "Space updated successfully", "createExam": {"step1": "Select exam content from below", "step2": "Do you have any mock exams or reference materials?", "step3": "Select your preferences", "step4": "Create Exam", "step1Description": "We will use this information to make the exam as accurate as possible", "step2Description": "The exam will be generated based on this content", "step3Description": "Customize your exam experience", "selectAll": "Select All", "continue": "Continue", "upload": "Upload", "uploadDesc": "Files, Audio, Video", "paste": "Paste", "pasteDesc": "YouTube, Website, Text", "skip": "<PERSON><PERSON>", "processing": "Processing...", "start": "Start", "numberOfQuestions": "Number of Questions", "questionTypes": "Question Types", "selectType": "Select Type", "both": "Both (Multiple Choice and Fill in the Blank)", "multipleChoice": "Multiple Choice", "fillInTheBlank": "Fill in the Blank", "durationMinutes": "Duration (Minutes)", "durationMinutesPlaceholder": "e.g., 60", "difficulty": "Difficulty Level", "selectDifficulty": "Select Difficulty", "easy": "Easy", "medium": "Medium", "hard": "Hard"}}, "chat": {"aiTitle": "TutoroAI Learning Assistant", "online": "Online", "collectNote": "Collect Note", "editNote": "Edit Note", "welcomeMessage": "Hello! I'm your AI learning assistant. I can help you answer questions, create tests, summarize content, and more. What can I help you with?", "placeholder": "Ask anything...", "learnPlus": "Learn+", "search": "Search", "voice": "Voice", "titlePlaceholder": "Enter note title", "tags": "Tags", "addTagPlaceholder": "Add tag", "preview": "Preview", "edit": "Edit", "editing": "Editing", "contentPlaceholder": "Enter or edit content here...", "saving": "Saving...", "updating": "Updating...", "saveNote": "Save Note", "updateNote": "Update Note", "cancel": "Cancel", "emptyStateTitle": "Learn with <PERSON> Tutor", "assistantTitle": "AI Assistant", "export": "Export", "clear": "Clear", "you": "You", "copied": "Content copied", "copy": "Copy", "share": "Share", "collectNoteAction": "Collect Note", "shareTitle": "AI Assistant Reply", "copiedSuccess": "Copied to clipboard, you can share manually", "shareError": "Share failed", "clearSuccess": "Cha<PERSON> cleared", "exportSuccess": "Chat exported", "copySuccess": "Copied to clipboard", "copyError": "Co<PERSON> failed", "aiResponseError": "Error getting AI response", "noteSaved": "Note saved successfully", "noteUpdated": "Note updated successfully", "noteSaveError": "Failed to save note", "noteUpdateError": "Failed to update note", "selectedContent": "Selected Content", "noContentSelected": "No content selected", "noContent": "No content available", "title": "Title", "mode": {"default": "<PERSON><PERSON><PERSON>", "creative": "Creative", "precise": "Precise"}, "responses": {"welcome": "Hello! I'm your AI learning assistant. I can help you answer questions, create tests, summarize content, and more. What can I help you with?", "helpLearn": "I'd be happy to help you learn! What topic would you like to explore?", "goodQuestion": "That's a great question. Let me explain it in detail.", "suggestion": "Based on your question, I suggest understanding it from these aspects:", "summary": "I can help you summarize the key points of this concept:"}}, "languageModal": {"title": "Select Language"}, "feedbackModal": {"title": "<PERSON><PERSON><PERSON>", "name": "Name", "namePlaceholder": "Enter your name", "feedbackType": "Feedback Type", "selectType": "Select feedback type", "bugReport": "Bug Report", "featureSuggestion": "Feature Suggestion", "improvementSuggestion": "Improvement Suggestion", "other": "Other", "email": "<PERSON><PERSON> Address (Optional)", "emailPlaceholder": "<EMAIL>", "yourFeedback": "Your Feedback", "feedbackPlaceholder": "Please describe your feedback in detail...", "cancel": "Cancel", "submit": "Submit", "submitting": "Submitting...", "submitSuccess": "<PERSON><PERSON><PERSON> submitted successfully!", "submitError": "Failed to submit feedback. Please try again.", "errors": {"nameRequired": "Name is required", "emailRequired": "Email is required", "emailInvalid": "Invalid email format", "categoryRequired": "Feedback type is required", "feedbackRequired": "Feedback is required"}}, "upgradeModal": {"discount": "25% Discount", "useCode": "Use code at checkout. Offer valid until September 30.", "upgradeTitle": "Upgrade Your Plan", "choosePlan": "Choose the plan that best fits your needs", "copyCode": "Copy Code", "copyCodeSuccess": "Code copied to clipboard", "copyCodeError": "Copy code failed", "mostPopular": "Most Popular", "mostPopularDesc": "Most popular plan, suitable for most users.", "proDescription": "Pro", "teamDescription": "Team", "toggleBilling": "Switch to annual plan", "freeDescription": "Free plan", "faq": {"title": "Frequently Asked Questions", "contactUs": "Can't find the answer?", "contactLink": "Contact us", "question1": "How can I upgrade my plan?", "answer1": "You can upgrade your plan anytime by clicking the 'Upgrade' button.", "question2": "How can I cancel my plan?", "answer2": "You can cancel your plan anytime by clicking the 'Cancel' button."}, "features": {"uploads": "Unlimited uploads, pastes and recordings", "aiChat": "Unlimited AI chat (100/month, with Learn+ mode)", "quizzes": "Unlimited quiz generation", "exams": "Unlimited mock exams", "voiceMode": "Unlimited use of voice mode", "fileSize": "Upload files up to 2000 pages/300 MB per file", "support": "Priority customer support"}, "monthly": "Monthly", "annual": "Annual", "save": "Save 40%", "selectPlan": "Select Plan", "joinCommunity": "Join over 1 million learners studying smarter with TutoroAI.", "needTeamPlan": "Need a team plan?", "contactUs": "Contact us", "startButton": "Get Started", "contactButton": "Contact Us", "plans": {"free": {"name": "Free", "billingCycle": "/month", "features": {"uploads": "3 uploads, pastes and recordings/day", "aiChat": "5 AI chat responses/day", "quizAnswers": "10 quiz answers/day", "exams": "2 mock exams/month", "voiceChat": "5 AI tutor chat responses", "fileSize": "Maximum file size of 100 pages/10 MB per upload"}}, "pro": {"name": "Pro", "billingCycleMonthly": "/month", "billingCycleAnnually": "/month", "features": {"uploads": "Unlimited uploads, pastes and recordings", "aiChat": "Unlimited AI chat responses", "quizzes": "Unlimited quiz generation", "exams": "Unlimited mock exams", "voiceMode": "Unlimited use of voice mode", "fileSize": "Maximum file size of 2000 pages/300 MB per file", "support": "Priority customer support"}}, "team": {"name": "Team", "price": "Custom", "features": {"everything": "Everything in Pro +", "billing": "Team centralized billing", "members": "Add team members", "storage": "Unlimited storage for custom options", "collaboration": "Collaborative space features"}}}, "success": {"title": "Subscription Successful!", "description": "Your subscription to the Pro plan has been activated successfully.", "manageBilling": "Manage your billing information", "continue": "Continue"}, "canceled": {"title": "Order Canceled", "button": "Continue Shopping", "message": "Order canceled -- continue to shop around and checkout when you're ready."}, "subscription": {"title": "Subscription Management", "description": "Manage your current subscription and billing information", "currentPlan": "Current Plan", "noPlan": "No Active Plan", "active": "Active", "canceled": "Canceled", "unknown": "Unknown", "manageBilling": "Manage Billing", "manageDescription": "Manage your subscription, billing information, and payment methods", "proDescription": "You're currently on the Pro plan with unlimited access to all features", "freeDescription": "You're currently on the free plan with limited features", "upgradeToPro": "Upgrade to Pro", "upgradePrompt": "Want to unlock more features?", "upgradePromptDescription": "Upgrade to Pro to get unlimited access to all features", "viewPlans": "View All Plans"}, "errors": {"alreadyFree": "You are already on the free plan!", "checkoutFailed": "Failed to create checkout session. Please try again.", "portalFailed": "Failed to access billing portal", "noSubscription": "No subscription found"}}, "pasteModal": {"title": "YouTube, Websites, etc.", "subtitle": "Enter YouTube links/playlists, website URLs, documents, ArXiv, etc.", "urlPlaceholder": "https://youtu.be/dQw4w9WgXcQ", "or": "or", "pasteTextTitle": "Paste Text", "pasteTextDesc": "Copy and paste text to add content", "textPlaceholder": "Paste your notes here", "cancel": "Cancel", "add": "Add", "processing": "Processing...", "success": "Content added successfully!", "error": "Error adding content. Please try again."}, "upload": {"uploading": "Uploading...", "success": "File uploaded successfully!", "error": "Upload failed. Please try again."}, "record": {"title": "Record Audio", "selectAudioSource": "Select Audio Source", "microphone": "Microphone", "microphoneDescription": "Record your voice or lessons", "browserTab": "Browser Tab", "browserTabDescription": "Capture audio playing in browser tabs", "microphoneRecording": "Microphone Recording", "browserTabRecording": "Browser Tab Recording", "requestPermission": "Request Permission", "permissionTitle": "Screen Recording Permission Required", "permissionDescription": "To record audio from browser tabs, we need access to your screen recording permission. Click the button below and the system permission window will appear.", "requestingPermission": "Requesting...", "allowPermission": "Allow Permission", "recordingTitle": "Recording", "clickToStart": "Click to start recording", "recording": "Recording...", "paused": "Recording paused", "completed": "Recording completed", "retry": "Retry", "save": "Save", "saving": "Saving...", "saveSuccess": "Recording saved successfully", "saveError": "Failed to save recording", "microphoneAccessDenied": "Microphone access denied", "browserTabAccessDenied": "Browser tab audio access denied", "browserNotSupported": "Browser tab recording is not supported in this environment", "permissionDenied": "Permission denied. Please allow screen recording to continue.", "userCancelled": "Recording cancelled by user", "notSupported": "Not Supported", "requiresHttps": "Requires HTTPS environment or newer browser version", "mobileRecordingStarted": "Mobile recording started"}, "searchModal": {"searchPlaceholder": "Search spaces and content...", "recentActivities": "Recent Activities", "spaces": "Spaces", "noResults": "No results found", "noResultsDescription": "Try different keywords or check your spelling", "searching": "Searching...", "loading": "Loading...", "viewAll": "View all", "typeStudy": "Study", "typeExam": "Exam", "typeChat": "Cha<PERSON>", "typeSpace": "Space", "typeOther": "Other", "inSpace": "in", "today": "Today", "yesterday": "Yesterday", "daysAgo": "days ago", "emptyState": "No content yet", "emptyStateDescription": "Create some content to see it here"}, "searchResults": {"resultsFor": "results for", "filter": "Filter", "all": "All", "articles": "Articles", "videos": "Videos", "spaces": "Spaces"}, "contact": {"title": "Contact Us", "subtitle": "We're here to help! Get in touch with our team for any questions, support, or feedback.", "formTitle": "Send us a message", "name": "Name", "nameHelper": "Enter your full name", "email": "Email", "emailHelper": "Enter your email address", "feedbackType": "Feedback Type", "selectFeedbackType": "Select feedback type", "feedbackTypeHelper": "Choose the category that best describes your inquiry", "message": "Message", "messagePlaceholder": "Well, I need help...", "messageHelper": "Describe your information in detail", "submit": "Submit", "submitting": "Submitting...", "submitSuccess": "Message sent successfully! We'll get back to you soon.", "submitError": "Failed to send message. Please try again.", "contactInfo": "Contact Information", "emailLabel": "Email", "phoneLabel": "Phone", "addressLabel": "Address", "businessHours": "Business Hours", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "closed": "Closed", "timezoneNote": "All times are in PST (UTC-8)", "responseTime": "Response Time", "responseTimeDesc": "We typically respond to inquiries within 24 hours during business days.", "generalInquiry": "General Inquiry", "technicalSupport": "Technical Support", "billingQuestion": "Billing Question", "featureRequest": "Feature Request", "bugReport": "Bug Report", "partnership": "Partnership", "other": "Other", "emailCopied": "Email copied to clipboard", "errors": {"nameRequired": "Name is required", "emailRequired": "Email is required", "emailInvalid": "Invalid email format", "feedbackTypeRequired": "Feedback type is required", "messageRequired": "Message is required"}}, "invite": {"title": "Earn 30% commission as an Affiliate", "subtitle": "Become part of our affiliate program and enjoy a 30% commission on every successful referral.", "earnWithTutoro": "Earn with TutoroAI", "viewDashboard": "View Dashboard", "tiktokLink": "#TutoroAI on TikTok >", "stats": {"referralLink": "Unique Referral Link", "yourLink": "Your Unique Link", "shareLink": "Share this link to start earning", "referralTracking": "Referral Tracking", "successfulReferrals": "Successful referrals this month", "earnedCommission": "Earned Commission", "pendingCommission": "Pending commission"}, "faq": {"title": "Affiliate Program FAQ", "contactUs": "Can't find the answer?", "contactLink": "Contact us", "questions": {"howToBecome": "How can I become an affiliate?", "howToBecomeAnswer": "Getting started is easy! Just register through [this link], and once you're approved, we'll provide everything you need to start sharing TutoroAI and earning rewards.", "commissionReceive": "What commission will I receive?", "commissionReceiveAnswer": "You'll earn 30% commission on every successful purchase made by users who sign up through your referral link — no strings attached.", "payoutLimit": "Is there a payout limit?", "payoutLimitAnswer": "Nope! You can earn as much as you like. The more referrals you bring in, the more you'll make.", "currency": "What currency will I be paid in?", "currencyAnswer": "All affiliate payouts are made in US dollars (USD).", "checkPerformance": "How can I check my performance and earnings?", "checkPerformanceAnswer": "You'll get access to an Affiliate Dashboard — track referrals, view commissions, and grab your custom marketing assets in one place.", "receivePayments": "When will I receive my payments?", "receivePaymentsAnswer": "We send out affiliate payments once a month, covering the commissions earned from the previous month.", "socialMediaPromotion": "Can I promote my affiliate link on social media?", "socialMediaPromotionAnswer": "Absolutely! Share your unique link on your blog, social media, website, or any digital channel where you connect with your audience.", "needHelp": "Need help?", "needHelpAnswer": "If you've got any questions or run into issues, just drop us a <NAME_EMAIL> — we're happy to help.", "support": "Who can I contact for support?", "supportAnswer": "If you have any questions or need assistance with our affiliate program, please contact our support team at [support email]."}}}, "notes": {"title": "My Notes", "newNote": "New Note", "loading": "Loading notes...", "error": "Error loading notes", "retry": "Retry", "search": {"placeholder": "Search notes..."}, "tags": {"title": "Tags", "clear": "Clear", "noTags": "No tags available"}, "filters": {"title": "Filters", "recentlyUpdated": "Recently Updated", "fromChat": "From Chat", "fromArticle": "From Article"}, "view": {"grid": "Grid", "list": "List", "noteCount": "notes"}, "empty": {"title": "No Notes", "description": "Use the 'Save Note' button in chat to save important content", "action": "New Note"}, "pagination": {"total": "Total {{count}} records, Page {{current}} of {{total}}", "previous": "Previous", "next": "Next"}, "source": "Source", "noTitle": "Untitled", "parseError": "Failed to parse note content", "delete": {"title": "Delete Note", "message": "will be permanently deleted.", "cancel": "Cancel", "confirm": "Delete", "deleting": "Deleting..."}}, "profile": {"title": "Profile", "back": "Back", "userInfo": {"title": "User Information", "editAvatar": "Edit Avatar", "noEmail": "No email set", "createdAt": "Created on", "unknown": "Unknown", "avatarUpdateSuccess": "Avatar updated successfully", "avatarUpdateError": "Avatar update failed", "stats": {"streak": "Learning Streak", "content": "Created Content", "activeDays": "Total Active Days"}}, "personalInfo": {"title": "Personal Information", "name": "Name", "email": "Email", "language": "Language Preference", "aiModel": "AI Model Preference", "saveChanges": "Save Changes", "saveError": "Save failed, please try again"}, "appSettings": {"title": "Application Settings", "darkMode": {"title": "Dark Mode", "description": "Toggle application theme appearance"}, "notifications": {"title": "Notification Settings", "email": {"title": "Email Notifications", "description": "Receive learning reminders and updates"}, "push": {"title": "Push Notifications", "description": "Browser push notifications"}}}, "referral": {"title": "Referral Rewards", "description": "Invite friends to join TutoroAI and both you and your friend will receive a 15% discount. Each referral gets 1 month of discount.", "copyLink": "Copy Link"}, "dangerZone": {"title": "Danger Zone", "description": "Deleting your account will permanently remove all your data, including learning records and created content. This action cannot be undone.", "deleteAccount": "Delete Account"}}, "auth": {"showPassword": "Show Password", "hidePassword": "Hide Password", "register": {"title": "Create Account", "subtitle": "Let's start your learning journey.", "googleAuth": "Sign up with Google", "or": "or continue with", "username": "Enter your username", "password": "Enter password", "confirmPassword": "Confirm password", "submit": "Sign Up", "loading": "Signing up...", "hasAccount": "Already have an account?", "login": "Sign In"}, "login": {"title": "Welcome Back", "subtitle": "Sign in to your account with email verification", "googleAuth": "Continue with Google", "or": "Or", "email": "Enter your email address", "captcha": "Enter verification code", "submit": "Sign In / Sign Up", "loading": "Signing in...", "logging": "Signing In...", "captchaInfo": "By continuing, you will either sign in to your existing account or create a new one automatically."}, "captcha": {"send": "Send Code", "sending": "Sending...", "sent": "Verification code sent successfully", "sentSuccess": "Verification code sent to your email"}, "forgotPassword": {"title": "Reset Password", "subtitle": "Enter your email address to receive a verification code", "email": "Enter your email address", "sendCode": "Send Verification Code", "verificationCode": "Enter verification code", "newPassword": "Enter new password", "confirmPassword": "Confirm new password", "submit": "Reset Password", "loading": "Processing...", "backToLogin": "Back to Login", "codeSent": "Verification code sent to your email", "codeExpiry": "The verification code will expire in 5 minutes", "resendCode": "Resend Code", "resendIn": "Resend in", "seconds": "seconds", "steps": {"email": "Email Verification", "code": "Enter Code", "password": "Set New Password"}}, "errors": {"username": {"required": "Please enter your username", "minLength": "Username must be at least 3 characters"}, "password": {"required": "Please enter your password", "minLength": "Password must be at least 6 characters"}, "confirmPassword": {"required": "Please confirm your password", "mismatch": "Passwords do not match"}, "email": {"required": "Please enter your email address", "invalid": "Please enter a valid email address"}, "captcha": {"required": "Verification code is required", "minLength": "Verification code must be at least 4 characters", "sendFailed": "Failed to send verification code"}, "verificationCode": {"required": "Please enter the verification code", "invalid": "Please enter a valid verification code"}, "google": {"default": "Google login failed", "cancelled": "Login cancelled by user", "denied": "Access denied by user", "config": "Google client configuration error, please contact administrator"}, "register": "Registration failed, please check your information", "login": "<PERSON><PERSON> failed, please check your credentials", "forgotPassword": "Password reset failed, please try again"}}, "guidePage": {"title": "Quick Guide", "subtitle": "Learn how to use TutoroAI to improve your learning efficiency", "quickStart": {"title": "Quick Start", "step": "Step", "completed": "Completed", "start": "Start", "step1": {"title": "Upload Learning Materials", "description": "Upload documents, videos, or paste web links to start learning"}, "step2": {"title": "Generate Learning Content", "description": "AI will automatically generate summaries, flashcards, and quizzes"}, "step3": {"title": "Start Learning", "description": "Use the generated content for learning and practice"}, "step4": {"title": "Join <PERSON>ussions", "description": "Communicate with other learners in learning spaces"}}, "features": {"title": "Key Features", "feature1": {"title": "Smart Content Generation", "description": "AI automatically generates summaries, flashcards, and quizzes from your learning materials"}, "feature2": {"title": "Learning Spaces", "description": "Create and manage your learning spaces, share insights with others"}, "feature3": {"title": "AI Tutor", "description": "24/7 available AI tutor to answer your learning questions"}, "feature4": {"title": "Progress Tracking", "description": "Detailed learning progress and achievement tracking to help you stay motivated"}}, "videoTutorial": {"title": "Video Tutorial", "description": "Watch our getting started video to quickly learn how to use TutoroAI", "playButton": "Play Tutorial Video"}, "faq": {"title": "Frequently Asked Questions", "question1": "How do I get started with TutoroAI?", "answer1": "First, upload your learning materials (documents, videos, or web links), then AI will automatically generate relevant learning content. You can use the generated summaries, flashcards, and quizzes to learn.", "question2": "What file formats are supported?", "answer2": "We support PDF, Word documents, PowerPoint, video files (MP4, AVI, etc.), audio files, as well as web links and YouTube videos.", "question3": "How accurate is the AI-generated content?", "answer3": "Our AI uses advanced natural language processing technology with high accuracy. However, we recommend verifying important information during your learning process.", "question4": "How can I communicate with other learners?", "answer4": "You can post content, comment, and participate in discussions in learning spaces. You can also join relevant study groups.", "question5": "What are the limitations of the free version?", "answer5": "The free version has daily limits on uploads and AI interactions. Upgrade to the Pro version for unlimited usage."}}, "exam": {"take": {"skip": "<PERSON><PERSON>", "submit": "Submit Exam", "question": "Question", "fillAnswer": "Fill in your answer here", "enterAnswer": "Please enter your answer...", "submitAlert": "Please answer all questions before submitting.", "submitSuccess": "Exam submitted!"}, "results": {"title": "Exam Results", "score": "Score", "timeSpent": "Time Spent", "passed": "Passed", "categoryPerformance": "Category Performance", "detailedResults": "Detailed Results", "correctAnswers": "Correct Answers", "incorrectAnswers": "Incorrect Answers", "sectionBreakdown": "Section Breakdown", "recommendations": {"title": "Study Recommendations", "focusOn": "Focus on reviewing", "and": "and", "routing": "routing", "suggestReading": "We suggest reading", "reactDocs": "React documentation", "lifecycle": "lifecycle section", "tryMore": "Try to complete more", "exercises": "exercises"}, "tabs": {"summary": "Summary", "details": "Details", "analytics": "Analytics"}, "details": {"all": "All", "correct": "Correct", "incorrect": "Incorrect", "yourAnswer": "Your Answer", "correctAnswer": "Correct Answer", "viewExplanation": "View Explanation"}, "chart": {"categoryPerformance": "Category Performance", "timeDistribution": "Time Distribution", "categoryPerformanceTitle": "Category Performance Accuracy", "timeDistributionTitle": "Time Distribution", "accuracy": "Accuracy", "timeDistributionNote": "Note: Time distribution data is currently example data. In actual application, it should be dynamically loaded from exam results.", "performanceAnalysis": "Performance Analysis", "bestPerformance": "You performed best in", "stateManagement": "state management", "withAccuracy": "with an accuracy of", "needImprovement": "You need improvement in", "routing": "routing", "averageTime": "You spent an average of", "slowerThanAverage": "per question, which is slower than average", "suggestFocus": "We suggest focusing on reviewing", "relatedKnowledge": "related knowledge"}, "actions": {"feedback": "Provide <PERSON>", "feedbackShort": "<PERSON><PERSON><PERSON>", "share": "Share Result", "shareShort": "Share", "retry": "Retake Exam", "newQuestions": "Generate New Questions", "createExam": "Create Exam"}, "feedback": {"title": "<PERSON><PERSON>", "difficultyQuestion": "How difficult was this exam?", "tooEasy": "Too Easy", "justRight": "Just Right", "tooDifficult": "<PERSON>", "feedbackQuestion": "What feedback or suggestions do you have for this exam?", "feedbackPlaceholder": "Please enter your feedback...", "cancel": "Cancel", "submit": "Submit <PERSON>", "submitting": "Submitting..."}}, "share": {"button": "Share Exam", "title": "Share Public Link for Exam", "copyLink": "Copy Link", "copied": "<PERSON>pied", "copyError": "Unable to copy link, please copy manually."}}, "article": {"upgrade": "Upgrade", "showArticle": "Show Article", "hideArticle": "Hide Article", "defaultTitle": "Learning Content", "loading": "Loading...", "loadError": "Failed to load content", "notFound": "Content Not Found", "notFoundDesc": "The requested content could not be found.", "invalidId": "Invalid Content ID", "invalidIdDesc": "Please provide a valid content ID.", "fileContentInfo": "This content was uploaded from a file. AI analysis tools are available in the sidebar.", "viewOriginalFile": "View original file", "contentAnalysis": "Content Analysis", "aiAnalysisDesc": "AI has analyzed your uploaded content and generated learning materials. Use the tools on the right to enhance your learning experience.", "availableFeatures": "Available Features", "feature": {"chapters": "Chapter breakdown and navigation", "chat": "AI-powered chat assistance", "flashcards": "Auto-generated flashcards", "quiz": "Interactive quizzes", "summary": "Smart content summaries"}, "uploadedAt": "Uploaded at", "noContent": "No Content Available", "noContentDesc": "This content doesn't have any associated data.", "titleUpdateNotSupported": "Title editing is not yet supported", "chaptersGenerated": "Chapters generated successfully", "chaptersGenerationFailed": "Failed to generate chapters", "flashcardsGenerated": "Flashcards generated successfully", "flashcardsGenerationFailed": "Failed to generate flashcards", "summaryGenerated": "Summary generated successfully", "summaryGenerationFailed": "Failed to generate summary", "quizGroupsFetchFailed": "Failed to fetch quiz groups", "tabs": {"chat": "Cha<PERSON>", "flashcards": "Flashcards", "quiz": "Quiz", "summary": "Summary", "chapters": "Chapters", "instructions": "Instructions"}, "content": {"toolbar": {"menu": "<PERSON><PERSON>", "search": "Search", "darkMode": "Dark Mode", "share": "Share", "download": "Download", "maximize": "Maximize", "exitFullscreen": "Exit Fullscreen", "pageFit": "Page fit", "fitToWidth": "Fit to width", "fitToPage": "Fit to page", "actualSize": "Actual size"}, "search": {"placeholder": "Search", "searching": "Searching...", "noMatches": "No matches found for \"{keyword}\"", "tryDifferent": "Try different keywords", "page": "Page"}, "category": "Frontend Interview", "readingGuide": "[Reading Guide]", "readingGuideText": "Recently interviewing, making a summary of some frontend high-frequency interview questions or knowledge points, on one hand for my own review and summary, on the other hand to share with friends who are interviewing or in need.", "tags": {"frontend": "Frontend Development", "interview": "Interview Preparation", "html": "HTML"}, "lastUpdated": "Last updated: May 28, 2024"}, "chat": {"title": "Learn with <PERSON> Tutor", "newChat": "New Chat", "newChatCreated": "New chat created successfully!", "quickActions": {"quiz": "Quiz", "quizPrompt": "Generate a quiz about this content", "mindMap": "Mind Map", "mindMapPrompt": "Generate a mind map about this content", "voiceMode": "Voice Mode", "voiceModePrompt": "Use voice mode to learn", "flashcards": "Flashcards", "flashcardsPrompt": "Generate flashcards about this content", "search": "Search", "searchPrompt": "Search for information about this content", "schedule": "Schedule", "schedulePrompt": "Generate a schedule for this content"}, "yourMessage": "You", "inputPlaceholder": "Ask anything...", "modes": {"default": "<PERSON><PERSON><PERSON>", "learn": "Learn+", "voice": "Voice", "search": "Search"}, "attachments": {"camera": "Camera", "file": "File", "mention": "Mention"}}, "flashcards": {"title": "Flashcards", "noCards": "No flashcards available", "flip": "Flip", "next": "Next", "previous": "Previous", "reset": "Reset", "card": "Card", "of": "of", "hint": "Hint", "explanation": "Explanation", "showExplanation": "Show Explanation", "hideExplanation": "Hide Explanation", "clickToFlip": "Click to Flip"}, "quiz": {"submit": "Submit", "title": "Quiz", "preferences": "Quiz Preferences", "numberOfQuestions": "Number of Questions", "difficulty": "Difficulty Level", "easy": "Easy", "medium": "Medium", "hard": "Hard", "questionTypes": "Question Types", "multipleChoice": "Multiple Choice", "freeResponse": "Free Response", "both": "Both", "generateQuiz": "Generate Quiz", "generating": "Generating quiz...", "generateSuccess": "Quiz group generated successfully", "generateFailed": "Failed to generate quiz group", "settingsUpdateSuccess": "Quiz settings updated successfully", "settingsUpdateFailed": "Failed to update quiz settings", "noContentId": "Content ID is required to generate quiz", "question": "Question", "of": "/", "hint": "Hint", "showHint": "Show Hint", "hideHint": "<PERSON><PERSON>", "checkAnswer": "Check Answer", "nextQuestion": "Next Question", "prevQuestion": "Previous Question", "submitAnswer": "Submit Answer", "yourAnswer": "Your Answer", "enterAnswer": "Please enter your answer here...", "correct": "Correct!", "incorrect": "Incorrect", "correctAnswer": "Correct Answer", "explanation": "Explanation", "quizComplete": "Quiz Complete!", "score": "Score", "correctAnswers": "Correct Answers", "tryAgain": "Try Again", "newQuiz": "New Quiz", "viewResults": "View Results", "complete": "Complete", "continue": "Continue", "start": "Start", "noAnswer": "No answer provided", "dontKnow": "Don't Know", "nextPage": "Next", "enterYourAnswer": "Enter your answer", "feedback": {"correct": "Correct", "incorrect": "Incorrect", "skipped": "Skipped", "correctDefault": "Correct answer!", "incorrectDefault": "Correct answer is:", "skippedDefault": "You skipped this question. We recommend reviewing this topic again.", "pageReference": "Page"}, "aiInteraction": {"placeholder": "Ask anything...", "giveHint": "Give me a hint", "walkThrough": "Walk me through", "keepSimple": "Keep it simple", "mode": "<PERSON><PERSON><PERSON>"}, "list": {"title": "Quiz List", "noQuizzes": "No quizzes available. Please generate one first.", "clickToStart": "Click to start quiz", "loading": "Loading...", "regenerateQuestions": "Regenerate Questions", "restartAll": "Restart All", "restart": "<PERSON><PERSON>", "regenerate": "Regenerate"}, "taking": {"back": "Back", "completed": "Completed", "congratulations": "You have completed this quiz. Continue to the next one!", "restartQuiz": "<PERSON><PERSON> Quiz", "startNew": "Start New", "backToList": "Back to List"}, "settings": {"title": "Quiz Preferences", "regenerate": "Regenerate", "restart": "<PERSON><PERSON>"}}, "summary": {"title": "Summary", "modes": {"detailed": "Detailed", "all": "All"}, "personalizedSummary": "Personalized Summary", "generating": "Generating summary...", "noSummary": "No summary available"}, "chapters": {"title": "Chapters", "page": "Page", "noChapters": "No chapters available"}, "instructions": {"title": "Instructions", "content": "Instructions content will be displayed here"}}, "contentBlock": {"status": {"completed": "Completed", "inProgress": "In Progress", "viewed": "Viewed", "draft": "Draft"}, "actions": {"move": "Move", "share": "Share", "delete": "Delete", "deleting": "Deleting...", "deleteSuccess": "Delete Success", "deleteFailed": "Delete Failed", "updateSuccess": "Update Success", "updateFailed": "Update Failed"}}, "terms": {"title": "Terms of Service", "subtitle": "Please read these terms carefully before using TutoroAI. Your use of our service constitutes acceptance of these terms.", "lastUpdated": "Last updated: January 2025", "introduction": "TutoroAI emphasizes compliance with legal and institutional regulations regarding the recording and sharing of educational content. Users are required to adhere strictly to the following guidelines and by using this platform, users agree to indemnify TutoroAI against any legal claims arising from unauthorized recordings or material sharing.", "compliance": {"title": "Compliance with Legal and Institutional Regulations", "institutionalPolicies": {"title": "Compliance with Institutional Policies", "content": "Users must not record or upload any content that violates their school, college, workplace, university, or instructor's policies. It is the user's responsibility to stay informed of and comply with their institution's rules concerning educational recordings and material sharing."}, "copyrightLaws": {"title": "Adherence to Copyright Laws", "content": "Users must not record or upload copyrighted content without explicit permission from the copyright owner. This includes text, images, graphics, and any other protected material."}, "privacyRespect": {"title": "Respect for Privacy and Sensitive Conversations", "content": "Users may not record or share sensitive discussions or information that would violate privacy regulations, including but not limited to confidential conversations or private communications protected by law. TutoroAI does not condone and is not liable for any content recorded or uploaded in violation of academic or legal policies. Users are fully responsible for ensuring their conduct complies with all applicable laws."}}, "license": {"title": "License to Use TutoroAI Content", "content": "Subject to compliance with these Terms, TutoroAI grants users a limited, personal, non-exclusive, non-commercial, revocable, and non-transferable license to access and use content provided through the platform for personal learning only.", "restrictions": "Users agree not to access, scrape, download, or extract content through automated tools (e.g., bots, scripts, crawlers) or use data mining technologies without written permission from TutoroAI.", "usage": "Subscriptions or licenses are for individual use and may not be shared. TutoroAI reserves the right to impose reasonable limits on usage, including the amount of content accessed or the number of devices used."}, "fulfillment": {"title": "Fulfillment Policy", "refund": {"title": "Refund Policy", "content": "Refunds are available only in cases of technical malfunction that cannot be resolved. Contact our support team within 30 days of purchase with issue details to request a refund."}, "delivery": {"title": "Delivery Policy", "content": "As a digital service, access is granted immediately upon successful purchase. Details and instructions will be sent via email."}, "return": {"title": "Return Policy", "content": "Due to the nature of our service, returns are not applicable. Please read all service details before purchasing. For issues, contact our support team."}, "cancellation": {"title": "Cancellation Policy", "content": "Users may cancel subscriptions at any time through the account settings page. Access to premium features continues until the end of the current billing cycle."}}, "copyright": {"title": "Content & Copyright Policy", "content": "TutoroAI respects intellectual property rights of all educators and institutions. Users must comply with copyright laws and uphold academic ethics. TutoroAI content is AI-generated, summarized, and designed to assist learning while respecting copyright.", "prohibited": {"title": "Prohibited Activities:", "item1": "Uploading copyrighted material without permission", "item2": "Uploading faculty-generated materials without consent", "item3": "Sharing direct lecture recordings or full transcripts (we do not store transcripts)", "item4": "Uploading content that violates institutional policies"}, "compliance": {"title": "Copyright Compliance", "content": "We comply with the DMCA. Copyright complaints may be <NAME_EMAIL> with: Contact information, Description of the infringed work, Location of the material on our platform, A statement of good faith belief, Your electronic or physical signature."}, "violations": {"title": "Violations may result in:", "item1": "Content removal", "item2": "Account suspension", "item3": "Service termination", "item4": "Legal action"}}, "collaboration": {"title": "Academic Collaboration", "content": "We welcome collaboration with schools and universities to guide the proper use of TutoroAI. Educators may contact us for institutional partnership arrangements."}, "honor": {"title": "Honor Code", "content": "We strongly support academic integrity. TutoroAI is a tool for study assistance—not a shortcut for assignments or exams.", "prohibited": {"title": "Prohibited:", "item1": "Sharing or soliciting test content or answers", "item2": "Uploading plagiarized content", "item3": "Any form of academic dishonesty"}, "violations": {"title": "Violations may result in:", "item1": "Content removal", "item2": "Account deactivation", "item3": "Institutional notification"}, "closing": "We recognize academic pressure but affirm that shortcuts hinder true learning. TutoroAI stands for integrity and education."}, "footer": {"contact": "If you have any questions about these Terms of Service, please contact us.", "email": "Email"}}, "privacy": {"title": "Privacy Policy", "subtitle": "Your privacy is important to us. This Privacy Policy explains how we collect, use, disclose, and protect your information when you use our services.", "lastUpdated": "Last updated: January 2025", "commitment": {"title": "Our Commitment to Privacy", "content": "TutoroAI values your privacy and commits to safeguarding your personal data. This Privacy Policy explains how we collect, use, disclose, and protect your information when you use our services. By using TutoroAI, you consent to the practices described herein."}, "information": {"title": "Information We Collect", "personal": {"title": "Personal Information You Provide", "description": "We collect information you voluntarily submit when registering or using our services:", "name": "Name", "email": "Email address", "username": "Usernames", "payment": "Payment information (processed securely via Stripe)", "academic": "Academic verification data (optional for student discounts)"}, "automatic": {"title": "Automatically Collected Information", "description": "When you access TutoroAI, we automatically collect:", "device": "Device information (IP address, browser, OS, device ID)", "usage": "Usage data (logs, time stamps, viewed pages, interactions)", "cookies": "Cookies and similar tracking technology"}, "cookieNote": "We may use essential, functional, and performance cookies. No third-party marketing cookies are used."}, "usage": {"title": "How We Use Your Information", "description": "We process your personal data to:", "provide": "Provide and improve our services", "authenticate": "Authenticate users and manage accounts", "process": "Process payments and verify subscriptions", "send": "Send important communications (e.g. policy updates)", "prevent": "Prevent fraud and ensure platform integrity", "fulfill": "Fulfill legal obligations"}, "legal": {"title": "Legal Bases for Processing (GDPR/UK GDPR)", "description": "We process your data based on:", "consent": "Your consent", "contractual": "Contractual necessity", "compliance": "Legal compliance", "legitimate": "Legitimate business interests", "withdrawal": "Users may withdraw consent anytime without affecting prior lawful processing."}, "sharing": {"title": "Sharing Your Information", "description": "TutoroAI may share your data with:", "providers": "Service providers (e.g. Stripe) under strict contractual terms", "authorities": "Authorities, if required by law or court order", "acquirers": "Potential acquirers in the event of a business transition", "noSale": "We do not sell your personal data to third parties."}, "retention": {"title": "Information Retention", "content": "We retain data only as long as necessary for service delivery, legal compliance, and legitimate business needs. Data is deleted or anonymized after three months of account deactivation unless longer retention is required by law."}, "security": {"title": "Security", "description": "We apply industry-standard security measures to protect your data, including:", "encrypted": "Encrypted transmissions", "access": "Role-based access controls", "audits": "Regular security audits", "disclaimer": "Despite best efforts, no system is 100% secure. Use the platform at your own discretion."}, "children": {"title": "Children's Privacy", "content": "TutoroAI does not knowingly collect information from children under 18. By using the Services, you represent that you are at least 18 or that you are the parent or guardian of such a child and consent to such child dependent's use of the Services. If you become aware of any data we may have collected from children under age 18, you have the right to request removal of unwanted data that you publicly post on the Services. Please contact <NAME_EMAIL>"}, "rights": {"title": "Your Privacy Rights", "description": "Depending on your jurisdiction, you may:", "access": "Access, update, or delete your personal data", "object": "Object to or restrict data processing", "withdraw": "Withdraw consent", "export": "Export your data (data portability)", "requests": "Requests may be <NAME_EMAIL>"}, "cookies": {"title": "Cookies and Tracking Technologies", "usage": "TutoroAI uses cookies for essential platform functionality and user experience improvement. You can manage cookie settings in your browser. Disabling cookies may impact service availability.", "tracking": "We may use click-through URLs in emails to track engagement. Avoid clicking if you prefer not to be tracked."}, "compliance": {"title": "Compliance with Copyright and Institutional Policies", "description": "TutoroAI users must not:", "copyright": "Upload copyrighted material without permission", "faculty": "Upload faculty-generated content without consent", "transcripts": "Share verbatim transcripts or recorded lectures", "aiGenerated": "TutoroAI does not host or distribute lecture recordings. All summaries and flashcards are AI-generated and intended for personal educational use."}, "international": {"title": "International Users", "content": "Your data may be processed and stored in Hong Kong or other jurisdictions. By using TutoroAI, you agree to the transfer and processing of your data in these locations."}, "updates": {"title": "Policy Updates", "content": "We may revise this Privacy Policy from time to time. Updates are effective upon posting. Significant changes may be communicated via email or the platform."}, "footer": {"contact": "For privacy inquiries, contact:", "email": "Email", "integration": "This Privacy Policy is integrated with and subject to our", "terms": "Terms of Service"}}}
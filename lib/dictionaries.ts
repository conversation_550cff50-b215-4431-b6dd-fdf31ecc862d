import 'server-only';
import type { Locale } from '../i18n.config';

// Import the translations from the existing files
const dictionaries = {
  en: () => import('./dictionaries/en.json').then((module) => module.default),
  zh: () => import('./dictionaries/zh.json').then((module) => module.default),
  'zh-HK': () => import('./dictionaries/zh-HK.json').then((module) => module.default),
};

export const getDictionary = async (locale: Locale) => {
  return dictionaries[locale]?.() ?? dictionaries.en();
}; 
/**
 * 流式响应 API 方法
 * 专门处理 Server-Sent Events (SSE) 类型的响应
 */

import { TokenManager } from '@/lib/api/client';
import { API_CONFIG } from '@/lib/api/config';
import { getCurrentLocale, getAcceptLanguageHeader } from '@/lib/api/client';

/**
 * SSE 流完成检测配置
 * Comprehensive completion detection for text/event-stream
 */
interface SSECompletionConfig {
  /** 显式标识 - Explicit completion markers */
  explicitMarkers?: string[];
  /** 业务校验 - Timeout configurations */
  timeouts?: {
    /** 总超时时间 (毫秒) */
    total?: number;
    /** 无数据超时时间 (毫秒) */
    noData?: number;
  };
  /** 连接状态检查 - Connection state validation */
  connectionCheck?: {
    /** 是否启用连接状态检查 */
    enabled?: boolean;
    /** 连接异常重试次数 */
    maxRetries?: number;
  };
}

/**
 * 默认完成检测配置
 */
const DEFAULT_COMPLETION_CONFIG: Required<SSECompletionConfig> = {
  explicitMarkers: ['[DONE]', 'DONE', '[END]', 'END', '😊'],
  timeouts: {
    total: 30000, // 30秒总超时
    noData: 10000 // 10秒无数据超时
  },
  connectionCheck: {
    enabled: true,
    maxRetries: 3
  }
};

/**
 * 检查流是否完成的综合判断函数
 * Comprehensive completion detection for SSE streams
 */
function checkStreamCompletion(
  data: string,
  config: Required<SSECompletionConfig>
): { isComplete: boolean; reason: string } {
  const trimmedData = data.trim();
  for (const marker of config.explicitMarkers) {
    if (trimmedData === marker || data.includes(marker)) {
      return { isComplete: true, reason: `Explicit marker: ${marker}` };
    }
  }

  return { isComplete: false, reason: 'Not complete' };
}

/** 与大模型问答 - 流式响应版本 POST /llm/chat */
export async function postLlmChatContentIdStream(
  body: {
    /** 模型ID */
    modelId?: number;
    /** 问题 */
    question: string;
    /** 上传ID */
    uploadId?: number[];
    /** 空间ID */
    spaceId?: number;
    /** 内容ID */
    contentId?: number;
  },
  onMessage?: (chunk: string) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
  options?: { [key: string]: any },
  completionConfig?: SSECompletionConfig,
  endpoint?: string
): Promise<void> {
  // 🚨 优化：参数验证 - 确保至少有 spaceId 或 contentId
  if (!body.spaceId && !body.contentId) {
    const error = new Error('[参数错误] 缺少空间ID或内容ID');
    console.error('❌ postLlmChatContentIdStream parameter validation failed:', error.message);
    onError?.(error);
    return;
  }

  // 优化：过滤掉无效的 uploadId（null, undefined, 0）
  const validUploadIds = body.uploadId?.filter((id) => id && id > 0) || [];

  // 优化：构建请求体，只包含有效字段，减少请求大小
  const requestBody: any = {
    question: body.question
  };

  // 只有当字段有有效值时才添加到请求体中
  if (body.modelId) {
    requestBody.modelId = body.modelId;
  }

  if (body.spaceId) {
    requestBody.spaceId = body.spaceId;
  }

  if (body.contentId) {
    requestBody.contentId = body.contentId;
  }

  if (validUploadIds.length > 0) {
    requestBody.uploadId = validUploadIds;
  }

  // 合并配置
  const config = {
    ...DEFAULT_COMPLETION_CONFIG,
    ...completionConfig,
    timeouts: {
      ...DEFAULT_COMPLETION_CONFIG.timeouts,
      ...completionConfig?.timeouts
    },
    connectionCheck: {
      ...DEFAULT_COMPLETION_CONFIG.connectionCheck,
      ...completionConfig?.connectionCheck
    }
  };

  // 构建URL - 支持动态切换endpoint
  const baseURL = API_CONFIG.BASE_URL;
  const apiEndpoint = endpoint || '/llm/chat'; // 默认使用 /llm/chat，可通过参数切换
  const fullURL = baseURL.startsWith('http')
    ? `${baseURL}${apiEndpoint}`
    : `${typeof window !== 'undefined' ? window.location.origin : ''}${baseURL}${apiEndpoint}`;
  const url = new URL(fullURL);

  // 获取token
  const token = TokenManager.getToken();

  // 获取当前语言设置
  const locale = getCurrentLocale();

  // 优化：记录开始时间用于性能监控
  const startTime = Date.now();
  let messageCount = 0;

  try {
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive', // 优化：保持连接活跃
        'Accept-Language': getAcceptLanguageHeader(locale),
        ...(token && { Authorization: `Bearer ${token}` }),
        ...(options?.headers || {})
      },
      body: JSON.stringify(requestBody),
      // 优化：设置信号以支持请求取消
      signal: options?.signal,
      ...(options || {})
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 检查是否是流式响应
    const contentType = response.headers.get('content-type');

    if (!contentType?.includes('text/event-stream')) {
      // 如果不是流式响应，尝试读取普通响应
      const text = await response.text();

      // 尝试解析响应文本，检查是否是错误响应
      try {
        const parsed = JSON.parse(text);
        // 检查是否是业务错误（code !== '0'）
        if (parsed.code && parsed.code !== '0') {
          onError?.(parsed);
          return;
        }

        // 如果是成功响应，继续正常处理
        if (parsed.data || parsed.content || parsed.message) {
          const content = parsed.data || parsed.content || parsed.message || text;
          onMessage?.(typeof content === 'string' ? content : JSON.stringify(content));
        } else {
          onMessage?.(text);
        }
      } catch (parseError) {
        // 如果不是 JSON 格式，直接作为普通文本处理
        onMessage?.(text);
      }

      onComplete?.();
      return;
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error('Response body is not readable');
    }

    let buffer = '';
    let hasReceivedData = false;
    let lastDataTime = Date.now();

    // 使用配置中的超时设置
    const TIMEOUT_MS = config.timeouts.total!;
    const NO_DATA_TIMEOUT_MS = config.timeouts.noData!;

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done && config.connectionCheck.enabled) {
          break;
        }

        // 业务校验 - 超时检查
        const now = Date.now();
        if (now - lastDataTime > TIMEOUT_MS) {
          break;
        }

        if (hasReceivedData && now - lastDataTime > NO_DATA_TIMEOUT_MS) {
          break;
        }

        // 如果有数据，更新时间戳
        if (value && value.length > 0) {
          lastDataTime = now;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');

        // 保留最后一行（可能不完整）
        buffer = lines.pop() || '';

        for (const line of lines) {
          // 跳过空行和SSE元数据
          if (!line.trim() || line.startsWith('event:') || line.startsWith('id:') || line.startsWith('retry:')) {
            continue;
          }

          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            // 检查显式完成标记
            const completion = checkStreamCompletion(data, config);
            if (completion.isComplete) {
              onComplete?.();
              return;
            }

            if (data.trim()) {
              hasReceivedData = true;
              lastDataTime = Date.now();

              try {
                // 尝试解析 JSON
                const parsed = JSON.parse(data);

                // 🚨 优化：处理业务错误码
                if (parsed.code && parsed.msg) {
                  // 收到错误响应，触发错误处理
                  const errorMsg = `[${parsed.code}] ${parsed.msg}`;
                  console.error('❌ Stream received error response:', errorMsg);
                  onError?.(new Error(errorMsg));
                  return;
                }

                // 处理后端格式
                if (parsed.type === 'action' && parsed.content === 'done') {
                  onComplete?.();
                  return;
                }

                if (parsed.type === 'text' || parsed.type === 'content') {
                  const content = parsed.content || parsed.text || parsed.message || '';
                  if (content) {
                    onMessage?.(content);
                    messageCount++; // 优化：统计消息数量
                  }
                } else if (typeof parsed === 'string' && parsed.trim()) {
                  // 优化：处理直接返回字符串的情况
                  onMessage?.(parsed);
                  messageCount++;
                }
              } catch (parseError) {
                // JSON 解析失败，先检查是否是错误格式的数据
                // 检查是否包含错误码格式的字符串
                if (data.includes('"code"') && data.includes('"msg"')) {
                  // 可能是格式错误的错误响应，作为错误处理
                  console.error('❌ Stream received malformed error response:', data);
                  onError?.(new Error(`Malformed error response: ${data}`));
                  return;
                }

                // 如果不是错误格式，且确实不是 JSON，才当作普通文本处理
                if (data.trim()) {
                  onMessage?.(data);
                  messageCount++;
                }
              }
            }
          } else if (line.trim() && !line.startsWith(':')) {
            // 处理没有 "data: " 前缀的行
            hasReceivedData = true;
            lastDataTime = Date.now();

            // 先检查是否是错误响应
            if (line.includes('"code"') && line.includes('"msg"')) {
              try {
                const parsed = JSON.parse(line.trim());
                if (parsed.code && parsed.msg) {
                  const errorMsg = `[${parsed.code}] ${parsed.msg}`;
                  console.error('❌ Stream received error response (no data prefix):', errorMsg);
                  onError?.(new Error(errorMsg));
                  return;
                }
              } catch {
                // 解析失败但包含错误格式，作为错误处理
                console.error('❌ Stream received malformed error response (no data prefix):', line);
                onError?.(new Error(`Malformed error response: ${line}`));
                return;
              }
            }

            const completion = checkStreamCompletion(line, config);
            if (completion.isComplete) {
              if (line.trim()) {
                onMessage?.(line.trim());
                messageCount++;
              }
              onComplete?.();
              return;
            }

            if (line.trim()) {
              onMessage?.(line.trim());
              messageCount++; // 优化：统计消息数量
            }
          }
        }
      }

      // 处理缓冲区中剩余的数据
      if (buffer.trim()) {
        if (buffer.startsWith('data: ')) {
          const data = buffer.slice(6);
          const completion = checkStreamCompletion(data, config);
          if (!completion.isComplete && data.trim()) {
            hasReceivedData = true;
            try {
              const parsed = JSON.parse(data);

              // 🚨 优化：处理业务错误码
              if (parsed.code && parsed.msg) {
                const errorMsg = `[${parsed.code}] ${parsed.msg}`;
                console.error('❌ Stream buffer received error response:', errorMsg);
                onError?.(new Error(errorMsg));
                return;
              }

              if (parsed.type === 'action' && parsed.content === 'done') {
                onComplete?.();
                return;
              }

              if (parsed.type === 'text' || parsed.type === 'content') {
                const content = parsed.content || parsed.text || parsed.message || '';
                if (content) {
                  onMessage?.(content);
                }
              }
            } catch (parseError) {
              // JSON 解析失败，先检查是否是错误格式的数据
              if (data.includes('"code"') && data.includes('"msg"')) {
                // 可能是格式错误的错误响应，作为错误处理
                console.error('❌ Stream buffer received malformed error response:', data);
                onError?.(new Error(`Malformed error response: ${data}`));
                return;
              }
              console.log('🚀 ~ data:', data);
              // 如果不是错误格式，才当作普通文本处理
              onMessage?.(data);
            }
          }
        } else if (buffer.trim()) {
          hasReceivedData = true;

          // 先检查是否是错误响应
          if (buffer.includes('"code"') && buffer.includes('"msg"')) {
            try {
              const parsed = JSON.parse(buffer.trim());
              if (parsed.code && parsed.msg) {
                const errorMsg = `[${parsed.code}] ${parsed.msg}`;
                console.error('❌ Stream buffer received error response (final):', errorMsg);
                onError?.(new Error(errorMsg));
                return;
              }
            } catch {
              // 解析失败但包含错误格式，作为错误处理
              console.error('❌ Stream buffer received malformed error response (final):', buffer);
              onError?.(new Error(`Malformed error response: ${buffer}`));
              return;
            }
          }

          const completion = checkStreamCompletion(buffer, config);
          if (completion.isComplete) {
            onMessage?.(buffer.trim());
            onComplete?.();
            return;
          }
          onMessage?.(buffer.trim());
        }
      }

      // 优化：记录性能统计
      const duration = Date.now() - startTime;
      console.log(
        `🚀 Stream completed successfully: ${messageCount} messages processed in ${duration}ms (${(
          messageCount /
          (duration / 1000)
        ).toFixed(2)} msg/s)`
      );

      onComplete?.();
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    // 优化：记录错误统计
    const duration = Date.now() - startTime;
    console.error(`❌ Stream failed after ${duration}ms, processed ${messageCount} messages:`, error);

    onError?.(error instanceof Error ? error : new Error(String(error)));
    onComplete?.();
  }
}

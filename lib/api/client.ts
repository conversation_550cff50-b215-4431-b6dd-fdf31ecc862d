import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { API_CONFIG } from './config';
import { type Locale, defaultLocale } from '@/i18n.config';

// Token管理类（优化版）
export class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';
  private static readonly LOCALE_KEY = 'preferred_locale';

  static getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static setToken(token: string): void {
    if (typeof window === 'undefined') return;

    // 设置localStorage
    localStorage.setItem(this.TOKEN_KEY, token);

    // 同步到cookie，供中间件使用
    this.syncTokenToCookie(token);
  }

  static clearTokens(): void {
    if (typeof window === 'undefined') return;

    // 清除localStorage
    localStorage.removeItem(this.TOKEN_KEY);

    // 清除cookie
    this.clearTokenCookie();
  }

  // 同步token到cookie
  private static syncTokenToCookie(token: string): void {
    if (typeof window === 'undefined') return;

    const isSecure = window.location.protocol === 'https:';
    const maxAge = 7 * 24 * 60 * 60; // 7天
    const secureFlag = isSecure ? '; Secure' : '';

    document.cookie = `auth_token=${token}; path=/; max-age=${maxAge}; SameSite=Lax${secureFlag}`;
  }

  // 清除token cookie
  private static clearTokenCookie(): void {
    if (typeof window === 'undefined') return;

    document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
  }

  // 语言偏好管理
  static getPreferredLocale(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.LOCALE_KEY);
  }

  static setPreferredLocale(locale: string): void {
    if (typeof window === 'undefined') return;

    localStorage.setItem(this.LOCALE_KEY, locale);

    // 同步到cookie，供中间件使用
    const maxAge = 365 * 24 * 60 * 60; // 1年
    const isSecure = window.location.protocol === 'https:';
    const secureFlag = isSecure ? '; Secure' : '';

    document.cookie = `preferred_locale=${locale}; path=/; max-age=${maxAge}; SameSite=Lax${secureFlag}`;
  }

  // 检查token是否即将过期（仅客户端）
  static isTokenExpiring(): boolean {
    if (typeof window === 'undefined') return false;

    const token = this.getToken();
    if (!token) return true;

    try {
      // 解析JWT token（如果是JWT格式）
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000; // 转换为毫秒
      const currentTime = Date.now();
      const timeToExpiry = expirationTime - currentTime;

      // 如果少于5分钟就过期，认为需要刷新
      return timeToExpiry < 5 * 60 * 1000;
    } catch (error) {
      // 如果不是JWT格式或解析失败，检查token是否存在
      console.warn('Token validation failed:', error);
      return false; // 假设非JWT token不会过期，由服务器验证
    }
  }

  // 检查token是否已过期（仅客户端）
  static isTokenExpired(): boolean {
    if (typeof window === 'undefined') return false;

    const token = this.getToken();
    if (!token) return true;

    try {
      // 解析JWT token（如果是JWT格式）
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000; // 转换为毫秒
      const currentTime = Date.now();

      return currentTime >= expirationTime;
    } catch (error) {
      // 如果不是JWT格式或解析失败，返回false
      console.warn('Token expiry check failed:', error);
      return false; // 假设非JWT token需要服务器验证
    }
  }

  // 检查token是否有效（基本格式检查）
  static isTokenValid(): boolean {
    if (typeof window === 'undefined') return false;

    const token = this.getToken();
    if (!token) return false;

    // 基本格式检查
    if (token.length < 10) return false;

    // 如果是JWT，检查格式
    if (token.includes('.')) {
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      try {
        // 尝试解析payload
        JSON.parse(atob(parts[1]));
        return true;
      } catch {
        return false;
      }
    }

    // 非JWT格式，假设有效（需要服务器验证）
    return true;
  }

  // 获取token信息（仅客户端调试用）
  static getTokenInfo(): {
    hasToken: boolean;
    isExpiring?: boolean;
    isExpired?: boolean;
    isValid?: boolean;
    tokenSource?: string;
  } {
    if (typeof window === 'undefined') {
      return { hasToken: false };
    }

    const token = this.getToken();
    if (!token) {
      return { hasToken: false };
    }

    return {
      hasToken: true,
      isExpiring: this.isTokenExpiring(),
      isExpired: this.isTokenExpired(),
      isValid: this.isTokenValid(),
      tokenSource: 'localStorage'
    };
  }
}

// 获取当前语言的工具函数
export function getCurrentLocale(): Locale {
  if (typeof window === 'undefined') {
    // 服务端渲染时，尝试从环境变量或默认值获取
    return defaultLocale;
  }

  // 1. 优先从URL路径获取locale
  const pathname = window.location.pathname;
  const segments = pathname.split('/').filter(Boolean);
  if (segments.length > 0) {
    const firstSegment = segments[0];
    const supportedLocales: Locale[] = ['en', 'zh', 'zh-HK'];
    if (supportedLocales.includes(firstSegment as Locale)) {
      return firstSegment as Locale;
    }
  }

  // 2. 从TokenManager获取语言偏好（复用现有逻辑）
  const storedLocale = TokenManager.getPreferredLocale();
  if (storedLocale && ['en', 'zh', 'zh-HK'].includes(storedLocale)) {
    return storedLocale as Locale;
  }

  // 3. 从cookie获取语言偏好
  const cookies = document.cookie.split(';').reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split('=');
    acc[key] = value;
    return acc;
  }, {} as Record<string, string>);

  const cookieLocale = cookies['preferred_locale'];
  if (cookieLocale && ['en', 'zh', 'zh-HK'].includes(cookieLocale)) {
    return cookieLocale as Locale;
  }

  return defaultLocale;
}

// 将locale转换为标准的accept-language格式
export function getAcceptLanguageHeader(locale: Locale): string {
  switch (locale) {
    case 'zh':
      return 'zh-CN,zh;q=0.9,en;q=0.8';
    case 'zh-HK':
      return 'zh-HK,zh-TW;q=0.9,zh;q=0.8,en;q=0.7';
    case 'en':
    default:
      return 'en-US,en;q=0.9';
  }
}

// API客户端类
class ApiClient {
  private instance: AxiosInstance;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (error?: any) => void;
  }> = [];

  constructor() {
    this.instance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: API_CONFIG.HEADERS,
      validateStatus: (status) => status < 500 // 对于5xx错误也不要立即失败
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = TokenManager.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        const locale = getCurrentLocale();
        config.headers['Accept-Language'] = getAcceptLanguageHeader(locale);

        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

        // 处理401错误（token过期）
        if (
          error.response?.status === 401 ||
          (error.response?.data === 40000 && !originalRequest._retry && !this.isRefreshing)
        ) {
          return this.handleTokenRefresh(originalRequest);
        }

        return Promise.reject(this.formatError(error));
      }
    );
  }

  private async handleTokenRefresh(originalRequest: AxiosRequestConfig & { _retry?: boolean }) {
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject });
      }).then(() => this.instance(originalRequest));
    }

    originalRequest._retry = true;
    this.isRefreshing = true;

    try {
      // 尝试刷新token
      const response = await this.instance.post('/auth/refresh');
      const { token } = response.data;

      if (token) {
        TokenManager.setToken(token);
        this.processQueue(null);
        return this.instance(originalRequest);
      } else {
        throw new Error('No token in refresh response');
      }
    } catch (refreshError) {
      this.processQueue(refreshError);
      TokenManager.clearTokens();

      // 重定向到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }

      throw refreshError;
    } finally {
      this.isRefreshing = false;
    }
  }

  private processQueue(error: any): void {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
    this.failedQueue = [];
  }

  private formatError(error: AxiosError): any {
    const response = error.response;

    if (response?.data) {
      return response.data;
    }

    return {
      success: false,
      message: error.message || '网络错误',
      code: response?.status || 0
    };
  }

  // HTTP 方法
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get(url, config);
    return response.data;
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post(url, data, config);
    return response.data;
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put(url, data, config);
    return response.data;
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete(url, config);
    return response.data;
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.patch(url, data, config);
    return response.data;
  }

  // 文件上传
  async upload<T = any>(
    url: string,
    file: File,
    onProgress?: (progressEvent: any) => void,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.instance.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress,
      ...config
    });

    return response.data;
  }

  // 创建取消令牌
  createCancelToken() {
    return axios.CancelToken.source();
  }

  // 检查是否为取消错误
  isCancel(error: any): boolean {
    return axios.isCancel(error);
  }

  // 为自动生成的 API 提供通用的 request 函数
  async request<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    // 检查是否为 LLM 相关接口，如果是则使用更长的超时时间
    const isLLMEndpoint =
      url.includes('/llm/') || url.includes('/ai/') || url.includes('/chat/') || url.includes('/generate');

    const finalConfig = {
      ...config,
      // 如果是 LLM 接口且没有指定超时时间，则使用 LLM 专用超时
      timeout: config?.timeout || (isLLMEndpoint ? API_CONFIG.LLM_TIMEOUT : API_CONFIG.TIMEOUT)
    };

    const response = await this.instance.request({ url, ...finalConfig });
    return response.data;
  }
}

// 创建单例实例
const apiClient = new ApiClient();

// 为自动生成的 API 导出 request 函数
const request = <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return apiClient.request<T>(url, config);
};

// 导出单例实例
export { apiClient };

// 为服务端提供的API请求函数，支持传入locale
export const createServerApiClient = (locale?: Locale) => {
  const serverInstance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      ...API_CONFIG.HEADERS,
      'Accept-Language': getAcceptLanguageHeader(locale || defaultLocale),
    },
  });

  return {
    get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
      serverInstance.get(url, config).then(res => res.data),
    post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
      serverInstance.post(url, data, config).then(res => res.data),
    put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
      serverInstance.put(url, data, config).then(res => res.data),
    delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
      serverInstance.delete(url, config).then(res => res.data),
    patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
      serverInstance.patch(url, data, config).then(res => res.data),
  };
};

// 默认导出 request 函数（供自动生成的 API 使用）
export default request;

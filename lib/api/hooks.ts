/**
 * React Query API Hooks
 * 基于自动生成的 API 接口封装的 React Query hooks
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
  UseInfiniteQueryOptions,
  useInfiniteQuery
} from '@tanstack/react-query';
import { API_CONFIG, QUERY_CONFIG } from './config';

// 导入自动生成的API模块
import * as damoxing from '@/servers/api/damoxing';
import * as huiyuanxinxi from '@/servers/api/huiyuanxinxi';
import * as huiyuanzhuceyudenglu from '@/servers/api/huiyuanzhuceyudenglu';
import * as kongjian from '@/servers/api/kongjian';
import * as wendangneirong from '@/servers/api/wendangneirong';
import * as wendangneirongbiji from '@/servers/api/wendangneirongbiji';
import * as wendangneirongceyan from '@/servers/api/wendangneirongceyan';
import * as wendangneirongchourenka from '@/servers/api/wendangneirongchourenka';
import * as wendangneirongliaotian from '@/servers/api/wendangneirongliaotian';
import * as wendangneirongzhangjie from '@/servers/api/wendangneirongzhangjie';
import * as wendangneirongzongjie from '@/servers/api/wendangneirongzongjie';
import * as wendangshangchuan from '@/servers/api/wendangshangchuan';

// ============== 查询键工厂 ==============
export const queryKeys = {
  // 大模型
  models: () => ['models'] as const,
  modelList: () => [...queryKeys.models(), 'list'] as const,

  // 会员信息
  member: () => ['member'] as const,
  memberProfile: (id?: number) => [...queryKeys.member(), 'profile', id] as const,
  memberAuth: () => [...queryKeys.member(), 'auth'] as const,

  // 空间
  space: () => ['space'] as const,
  spaceList: (params?: any) => [...queryKeys.space(), 'list', params] as const,
  spaceDetail: (id: number) => [...queryKeys.space(), 'detail', id] as const,

  // 内容
  content: () => ['content'] as const,
  contentList: (params?: any) => [...queryKeys.content(), 'list', params] as const,
  contentDetail: (id: number) => [...queryKeys.content(), 'detail', id] as const,
  contentNote: (id: number) => [...queryKeys.content(), id, 'note'] as const,

  // 笔记
  notes: (params?: any) => ['notes', params] as const,
  noteTags: (params?: any) => ['notes', 'tags', params] as const,
  noteDetail: (id: number | null) => ['notes', 'detail', id] as const,

  // 测验
  quiz: () => ['quiz'] as const,
  quizGroups: (contentId: number) => [...queryKeys.quiz(), 'groups', contentId] as const,
  quizQuestions: (groupId: number) => [...queryKeys.quiz(), 'questions', groupId] as const,
  quizAnswers: (groupId: number) => [...queryKeys.quiz(), 'answers', groupId] as const
} as const;

// ============== 大模型相关 ==============

export const useGetModelList = (options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn'>) => {
  return useQuery({
    queryKey: queryKeys.modelList(),
    queryFn: () => damoxing.getInfraModelListModel(),
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

// ============== 认证相关 ==============

export const useLogin = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  return useMutation({
    mutationFn: (data: { username: string; password: string }) => huiyuanzhuceyudenglu.postMemberAuthSignIn(data),
    ...options
  });
};

export const useRegister = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  return useMutation({
    mutationFn: (data: { username: string; password: string; confirmPassword: string; profile?: any }) =>
      huiyuanzhuceyudenglu.postMemberAuthSignUp(data),
    ...options
  });
};

export const useRefreshToken = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  return useMutation({
    mutationFn: () => huiyuanzhuceyudenglu.postMemberAuthRenewToken({}),
    ...options
  });
};

// ============== 空间相关 ==============

export const useGetSpaceList = (
  params?: API.getSpacePageSpaceParams,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: queryKeys.spaceList(params),
    queryFn: () => kongjian.getSpacePageSpace(params || {}),
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useGetSpaceDetail = (
  id: number,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: queryKeys.spaceDetail(id),
    queryFn: () => kongjian.getSpaceFindSpaceId({ id }),
    enabled: !!id,
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useCreateSpace = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => kongjian.postSpaceCreateSpace(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.space() });
    },
    ...options
  });
};

export const useUpdateSpace = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) => kongjian.postSpaceUpdateSpaceId({ id }, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.space() });
      queryClient.invalidateQueries({ queryKey: queryKeys.spaceDetail(variables.id) });
    },
    ...options
  });
};

export const useDeleteSpace = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => kongjian.postSpaceDeleteSpaceId({ id }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.space() });
    },
    ...options
  });
};

// ============== 内容相关 ==============

export const useGetContentList = (
  params?: API.getContentPageContentParams,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: queryKeys.contentList(params),
    queryFn: () => wendangneirong.getContentPageContent(params || {}),
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useGetHistoryRecords = (
  params?: API.getContentPageRecentContentParams,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['historyRecords', params],
    queryFn: () => wendangneirong.getContentPageRecentContent(params || {}),
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useGetContentDetail = (
  id: number,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: queryKeys.contentDetail(id),
    queryFn: () => wendangneirong.getContentFindContentId({ id }),
    enabled: !!id,
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useCreateContent = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => wendangneirong.postContentCreateContent(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.content() });
    },
    ...options
  });
};

// ============== 笔记相关 ==============

export const useGetNoteDetail = (
  noteId: number | null,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: queryKeys.noteDetail(noteId),
    queryFn: () => wendangneirongbiji.getNoteFindNoteId({ id: noteId! }),
    enabled: !!noteId,
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useGetContentNote = (
  contentId: number,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: queryKeys.contentNote(contentId),
    queryFn: () => wendangneirongbiji.getNoteFindNoteContentId({ contentId }),
    enabled: !!contentId,
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useUpdateNote = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) => wendangneirongbiji.postNoteUpdateNoteId({ id }, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.content() });
    },
    ...options
  });
};

export const useCreateNote = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { contentId?: number; spaceId?: number; context: string; tags?: string[];title?:string }) =>
      wendangneirongbiji.postNoteCreateNote(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.content() });
      queryClient.invalidateQueries({ queryKey: queryKeys.notes() });
    },
    ...options
  });
};

export const useDeleteNote = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => wendangneirongbiji.postNoteDeleteNoteId({ id }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.content() });
      queryClient.invalidateQueries({ queryKey: queryKeys.notes() });
    },
    ...options
  });
};

export const useGetNotes = (
  params?: API.getNotePageNoteParams,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: queryKeys.notes(params),
    queryFn: () => wendangneirongbiji.getNotePageNote(params || {}),
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useGetNoteTags = (
  params?: API.getNoteListTagParams,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: queryKeys.noteTags(params),
    queryFn: () => wendangneirongbiji.getNoteListTag(params || {}),
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

// ============== 测验相关 ==============

export const useGetQuizGroups = (
  contentId: number,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: queryKeys.quizGroups(contentId),
    queryFn: () => wendangneirongceyan.getLlmQuizGroupListGroupContentId({ contentId }),
    enabled: !!contentId,
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useGetQuizQuestions = (
  groupId: number,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: queryKeys.quizQuestions(groupId),
    queryFn: () => wendangneirongceyan.getLlmQuizQuestionListQuestionGroupId({ groupId }),
    enabled: !!groupId,
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useGetQuizAnswers = (
  quizGroupId: number,
  options?: Omit<UseQueryOptions<any, any, any, any>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: queryKeys.quizAnswers(quizGroupId),
    queryFn: () => wendangneirongceyan.getLlmQuizAnswerListAnswer({ quizGroupId }),
    enabled: !!quizGroupId,
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

// ============== 变更操作 ==============

// 更新测验设置
export const useUpdateQuizSettings = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ contentId, data }: { contentId: number; data: any }) =>
      wendangneirong.postContentUpdateQuizSettingsId({ id: contentId }, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.content() });
      queryClient.invalidateQueries({ queryKey: queryKeys.contentDetail(variables.contentId) });
    },
    ...options
  });
};

export const useGenerateQuizGroup = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ contentId, data, reset }: { contentId: number; data: any; reset?: 'true' | 'false' }) =>
      wendangneirongceyan.postLlmQuizGroupContentId({ contentId, reset }, {
        timeout: API_CONFIG.LLM_TIMEOUT
      }),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.quizGroups(variables.contentId) });
    },
    // 增加重试次数和重试间隔，因为LLM接口可能需要更多时间
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

export const useGenerateQuizQuestions = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupId, data, reset }: { groupId: number; data: any; reset?: 'true' | 'false' }) =>
      wendangneirongceyan.postLlmQuizQuestionGroupId({ groupId, reset }, {
        timeout: API_CONFIG.LLM_TIMEOUT
      }),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.quizQuestions(variables.groupId) });
    },
    // 增加重试次数和重试间隔，因为LLM接口可能需要更多时间
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

export const useCreateQuizAnswer = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ quizQuestionId, answer, modelId }: { quizQuestionId: number; answer: string; modelId?: number }) =>
      wendangneirongceyan.postLlmQuizAnswerCreateAnswer(
        { modelId: modelId || 1 }, 
        { quizQuestionId, answer },
        { timeout: API_CONFIG.LLM_TIMEOUT }
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.quiz() });
    },
    ...options
  });
};

export const useGenerateFreshcard = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  return useMutation({
    mutationFn: async ({
      contentId,
      data,
      reset,
      ...params
    }: {
      contentId: number;
      data: any;
      modelId?: number;
      quantity?: number;
      reset?: boolean;
    }) => {
      const startTime = Date.now();
      console.log('🚀 useGenerateFreshcard: Starting mutation with params:', { 
        contentId, 
        reset, 
        timeout: API_CONFIG.LLM_TIMEOUT,
        ...params 
      });
      
      try {
        const result = await wendangneirongchourenka.postLlmFreshcardContentId(
          {
            contentId,
            reset: reset ? 'true' : 'false',
            quantity: params?.quantity || 20,
            ...params
          },
          data,
          {
            // 使用 LLM 专用超时时间，通过传递 timeout 参数让 client.ts 中的逻辑处理
            timeout: API_CONFIG.LLM_TIMEOUT
          }
        );
        
        const duration = Date.now() - startTime;
        console.log('✅ useGenerateFreshcard: Mutation completed successfully:', { 
          result, 
          duration: `${duration}ms` 
        });
        return result;
      } catch (error: any) {
        const duration = Date.now() - startTime;
        console.error('❌ useGenerateFreshcard: Mutation failed:', { 
          error: error?.message || error, 
          duration: `${duration}ms`,
          errorCode: error?.code,
          status: error?.response?.status,
          isTimeout: error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')
        });
        throw error;
      }
    },
    // 设置合理的重试策略
    retry: (failureCount, error: any) => {
      console.log('🔄 useGenerateFreshcard: Retry decision:', {
        failureCount,
        errorStatus: error?.response?.status,
        errorCode: error?.code,
        errorMessage: error?.message
      });
      
      // 对于某些错误不进行重试
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        console.log('❌ useGenerateFreshcard: No retry - Auth error');
        return false;
      }
      
      // 对于超时错误，我们也重试
      if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
        console.log('⏰ useGenerateFreshcard: Timeout detected, will retry');
      }
      
      // 最多重试2次
      const shouldRetry = failureCount < 2;
      console.log(`🔄 useGenerateFreshcard: ${shouldRetry ? 'Will retry' : 'No more retries'} (attempt ${failureCount + 1})`);
      return shouldRetry;
    },
    retryDelay: (attemptIndex) => {
      const delay = Math.min(1000 * 2 ** attemptIndex, 10000);
      console.log(`⏳ useGenerateFreshcard: Retry delay: ${delay}ms (attempt ${attemptIndex + 1})`);
      return delay;
    },
    ...options
  });
};

export const useChatWithContent = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  return useMutation({
    mutationFn: ({ contentId, data }: { contentId: number; data: any }) =>
      wendangneirongliaotian.postLlmChatContentId({ contentId }, data, {
        timeout: API_CONFIG.LLM_TIMEOUT
      }),
    // 增加重试次数和重试间隔，因为LLM接口可能需要更多时间
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

export const useGenerateChapter = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  return useMutation({
    mutationFn: ({ contentId }: { contentId: number }) =>
      wendangneirongzhangjie.postLlmChapterGenerateChapterContentId({ contentId }, {
        timeout: API_CONFIG.LLM_TIMEOUT
      }),
    // 增加重试次数和重试间隔，因为LLM接口可能需要更多时间
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

export const useGenerateSummary = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  return useMutation({
    mutationFn: ({ contentId }: { contentId: number }) =>
      wendangneirongzongjie.postLlmSummaryGenerateSummaryContentId({ contentId }, {
        timeout: API_CONFIG.LLM_TIMEOUT
      }),
    // 增加重试次数和重试间隔，因为LLM接口可能需要更多时间
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

export const useUploadDocument = (options?: Omit<UseMutationOptions<any, any, any>, 'mutationFn'>) => {
  return useMutation({
    mutationFn: (data: any) => wendangshangchuan.postInfraFilesUpload(data),
    ...options
  });
};

// ============== 无限查询 ==============

export const useInfiniteSpaceList = (
  params?: Omit<API.getSpacePageSpaceParams, 'page'>,
  options?: Omit<
    UseInfiniteQueryOptions<any, any, any, any, any>,
    'queryKey' | 'queryFn' | 'initialPageParam' | 'getNextPageParam'
  >
) => {
  return useInfiniteQuery({
    queryKey: [...queryKeys.spaceList(params), 'infinite'],
    queryFn: ({ pageParam }) => kongjian.getSpacePageSpace({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (lastPage?.data?.hasNext) {
        return lastPage.data.page + 1;
      }
      return undefined;
    },
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

export const useInfiniteContentList = (
  params?: Omit<API.getContentPageContentParams, 'page'>,
  options?: Omit<
    UseInfiniteQueryOptions<any, any, any, any, any>,
    'queryKey' | 'queryFn' | 'initialPageParam' | 'getNextPageParam'
  >
) => {
  return useInfiniteQuery({
    queryKey: [...queryKeys.contentList(params), 'infinite'],
    queryFn: ({ pageParam }) => wendangneirong.getContentPageContent({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (lastPage?.data?.hasNext) {
        return lastPage.data.page + 1;
      }
      return undefined;
    },
    staleTime: QUERY_CONFIG.STALE_TIME,
    ...options
  });
};

// ============== 缓存管理 ==============

export const useCacheInvalidation = () => {
  const queryClient = useQueryClient();

  return {
    invalidateAllContent: () => queryClient.invalidateQueries({ queryKey: queryKeys.content() }),
    invalidateAllSpace: () => queryClient.invalidateQueries({ queryKey: queryKeys.space() }),
    invalidateAllQuiz: () => queryClient.invalidateQueries({ queryKey: queryKeys.quiz() }),
    invalidateAll: () => queryClient.invalidateQueries()
  };
};

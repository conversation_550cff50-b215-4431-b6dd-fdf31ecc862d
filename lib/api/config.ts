/**
 * API 配置文件
 * 集中管理所有 API 相关的配置
 */

export const API_CONFIG = {
  // 基础URL - 从环境变量获取
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  
  // 超时设置（毫秒）
  TIMEOUT: 600000,
  
  // LLM 相关接口的超时设置（更长的超时时间）
  LLM_TIMEOUT: 600000, // 10分钟
  
  // 默认请求头
  // 注意：Accept-Language 头会根据用户当前语言动态添加
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
} as const;

// 查询配置
export const QUERY_CONFIG = {
  // 默认缓存时间（5分钟）
  STALE_TIME: 5 * 60 * 1000,
  
  // 默认重试次数
  RETRY: 3,
  
  // 重试延迟（毫秒）
  RETRY_DELAY: 1000,
} as const;

// API端点配置
export const API_ENDPOINTS = {
  // 用户相关
  USER: {
    PROFILE: '/user/profile',
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REGISTER: '/auth/register',
    REFRESH_TOKEN: '/auth/refresh',
  },
  
  // AI教育相关
  EDUCATION: {
    // 课程管理
    COURSES: '/courses',
    COURSE_DETAIL: (id: string) => `/courses/${id}`,
    COURSE_PROGRESS: (id: string) => `/courses/${id}/progress`,
    
    // AI辅导
    AI_CHAT: '/ai/chat',
    AI_FEEDBACK: '/ai/feedback',
    AI_RECOMMEND: '/ai/recommend',
    
    // 学习记录
    LEARNING_RECORDS: '/learning/records',
    LEARNING_STATS: '/learning/stats',
    
    // 练习题目
    EXERCISES: '/exercises',
    EXERCISE_SUBMIT: '/exercises/submit',
    EXERCISE_RESULT: (id: string) => `/exercises/${id}/result`,
  },
  
  // 文件上传
  UPLOAD: {
    IMAGE: '/upload/image',
    VIDEO: '/upload/video',
    DOCUMENT: '/upload/document',
  },
} as const; 
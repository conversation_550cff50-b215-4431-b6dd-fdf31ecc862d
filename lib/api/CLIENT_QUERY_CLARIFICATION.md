# Client vs Query-Client 区别与修复说明

## 🤔 问题发现

用户发现了两个关键问题：
1. `client.ts` 与 `query-client.tsx` 的职责不清晰
2. `query-client.tsx` 中的 `queryKeys` 定义了不存在的接口

## 📋 文件职责澄清

### `client.ts` - HTTP 客户端层
**职责：**
- HTTP 请求的发送和处理
- 认证 token 的管理（TokenManager）
- 请求/响应拦截器
- 错误处理和重试逻辑
- 基于 Axios 的底层网络通信

**核心功能：**
```typescript
// Token 管理
TokenManager.setToken(token);
TokenManager.getToken();
TokenManager.clearTokens();

// HTTP 请求
apiClient.get('/api/endpoint');
apiClient.post('/api/endpoint', data);
```

### `query-client.tsx` - React Query 配置层
**职责：**
- React Query 的全局配置
- 数据缓存策略设置
- 查询和变更的默认行为
- QueryClient Provider 组件
- 开发工具集成

**核心功能：**
```typescript
// 提供 React Query 上下文
<QueryProvider>
  <App />
</QueryProvider>

// 配置缓存和重试策略
staleTime: 5 * 60 * 1000,
retry: 3,
```

## 🔧 修复内容

### 1. 移除错误的 queryKeys
**问题：**
`query-client.tsx` 中定义的 `queryKeys` 基于假设的接口：
```typescript
// ❌ 错误：这些接口在项目中不存在
courses: { all: ['courses'] },
exercises: { all: ['exercises'] },
ai: { all: ['ai'] },
```

**修复：**
- 移除了 `query-client.tsx` 中的错误 `queryKeys`
- 正确的 `queryKeys` 已在 `hooks.ts` 中基于实际 API 定义：

```typescript
// ✅ 正确：基于实际的自动生成 API
export const queryKeys = {
  models: () => ['models'] as const,
  space: () => ['space'] as const,
  content: () => ['content'] as const,
  quiz: () => ['quiz'] as const,
  // ...
};
```

### 2. 统一配置管理
**修复前：**
- `query-client.tsx` 中硬编码配置值
- 与 `config.ts` 中的配置不一致

**修复后：**
```typescript
// 使用统一的配置
import { QUERY_CONFIG } from './config';

staleTime: QUERY_CONFIG.STALE_TIME,
retry: QUERY_CONFIG.RETRY,
retryDelay: QUERY_CONFIG.RETRY_DELAY,
```

### 3. 消除重复功能
**发现的重复：**
`auth-helpers.ts` 与 `TokenManager` 有重复的 token 管理功能

**修复：**
- 保留 `TokenManager` 作为主要的 token 管理工具
- `auth-helpers.ts` 专注于重定向相关的工具函数
- 移除重复的 token 操作函数

## 🎯 最佳实践

### 1. Token 管理
```typescript
// ✅ 使用 TokenManager（来自 API 客户端）
import { TokenManager } from '@/lib/api';

TokenManager.setToken(token);
const token = TokenManager.getToken();
```

### 2. 查询键使用
```typescript
// ✅ 使用正确的 queryKeys
import { queryKeys } from '@/lib/api';

useQuery({
  queryKey: queryKeys.spaceList(params),
  queryFn: () => getSpaceList(params),
});
```

### 3. 重定向处理
```typescript
// ✅ 使用重定向工具函数
import { handlePostLoginRedirect, buildLoginUrl } from '@/lib/utils/auth-helpers';

// 登录后重定向
handlePostLoginRedirect(router);

// 构建登录URL
const loginUrl = buildLoginUrl('/protected-page');
```

## 📊 优化效果

### 代码质量提升
- **职责分离**：每个文件有明确的职责
- **消除重复**：移除了重复的功能实现
- **配置统一**：使用统一的配置管理

### 维护性改善
- **更清晰的架构**：HTTP 层与缓存层分离
- **正确的类型支持**：基于实际 API 的类型定义
- **更好的可扩展性**：为未来功能扩展提供良好基础

## 🔄 架构图

```
┌─────────────────┐    ┌──────────────────┐
│   Components    │    │   React Query    │
│                 │    │   (缓存/状态)     │
└─────────┬───────┘    └─────────┬────────┘
          │                      │
          │              ┌───────▼────────┐
          │              │ query-client   │
          │              │ (配置层)       │
          │              └───────┬────────┘
          │                      │
    ┌─────▼──────────────────────▼─────┐
    │           hooks.ts               │
    │    (业务逻辑 + queryKeys)        │
    └─────────────┬────────────────────┘
                  │
         ┌────────▼─────────┐
         │    client.ts     │
         │  (HTTP 客户端)   │
         └──────────────────┘
```

## 📝 总结

通过这次修复：
1. **明确了职责边界**：HTTP 客户端 vs React Query 配置
2. **修复了错误的 API 定义**：使用实际的自动生成 API
3. **消除了重复功能**：统一了 token 管理和配置
4. **提高了代码质量**：更清晰的架构和更好的维护性

现在的架构更好地支持了自动生成 API 的使用模式，避免了手写代码与自动生成代码的冲突。 
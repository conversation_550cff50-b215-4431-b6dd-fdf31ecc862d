/**
 * API 模块统一导出
 */

// 导出核心客户端和工具
export { apiClient, TokenManager } from './client';

// 导出配置
export { API_CONFIG, QUERY_CONFIG } from './config';

// 导出通用类型
export type {
  ApiResponse,
  ApiError,
  PaginatedResponse,
  BaseQueryParams,
  QueryOptions,
  MutationOptions,
} from './types';

// 导出所有 hooks（包含 queryKeys）
export * from './hooks';

// 导出 React Query 客户端
export { QueryProvider, getQueryClient } from './query-client'; 
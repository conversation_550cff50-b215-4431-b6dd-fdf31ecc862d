/*
 * @Date: 2025-06-03 21:17:29
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-25 14:54:36
 * @FilePath: /tutoro-ai-front/lib/api/query-client.tsx
 */
'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState, ReactNode } from 'react';
import { QUERY_CONFIG } from './config';
import { toast } from 'sonner';

// 全局错误处理函数
const handleGlobalError = (data: any, error: any) => {
  // 如果是 API 响应且 code 不等于 '0'，显示错误消息
  if (data && typeof data === 'object' && 'code' in data && 'msg' in data) {
    if (data.code !== '0') {
      toast.error(data.msg || 'Request failed');
      console.error('API Error:', data);
      return;
    }
  }

  // 如果是网络错误或其他错误，也显示错误消息
  if (error) {
    const errorMessage = error?.response?.data?.msg || error?.message || 'An unexpected error occurred';
    toast.error(errorMessage);
    console.error('Request Error:', error);
  }
};

// 创建Query Client配置
const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // 使用统一配置
        staleTime: QUERY_CONFIG.STALE_TIME,
        // 缓存时间（10分钟）
        gcTime: 10 * 60 * 1000,
        // 窗口聚焦时重新获取
        refetchOnWindowFocus: false,
        // 网络重连时重新获取
        refetchOnReconnect: false,
        // 禁用重试，避免额外的请求
        retry: false,
        // 移除重试延迟限制
        retryDelay: 0,
        // 全局查询错误处理
        onError: (error: any) => {
          console.error('QueryClient: Query error', error);
          handleGlobalError(null, error);
        },
        // 全局查询成功处理 - 检查响应数据
        onSuccess: (data: any) => {
          handleGlobalError(data, null);
        }
      },
      mutations: {
        // 禁用变更重试（在各个 hook 中单独配置）
        retry: false,
        // 确保mutation在完成后能正确更新状态
        onSettled: (data, error, variables, context) => {
          handleGlobalError(data, error);
          console.log('QueryClient: Mutation settled', { data, error });
        },
        onError: (error, variables, context) => {
          console.error('QueryClient: Mutation error', error);
          handleGlobalError(null, error);
        },
        onSuccess: (data, variables, context) => {
          console.log('QueryClient: Mutation success', data);
          handleGlobalError(data, null);
        }
      }
    }
  });
};

// Query Client Provider组件
export function QueryProvider({ children }: { children: ReactNode }) {
  const [queryClient] = useState(() => createQueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
}

// 导出查询客户端实例（用于手动操作）
export const getQueryClient = () => createQueryClient();

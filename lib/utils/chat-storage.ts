/**
 * 聊天记录本地存储工具
 * 支持不同类型的 key（contentId, spaceId 等）
 */

import type { ChatMessage } from '@/types';

/**
 * 生成聊天存储 key
 * @param type - 存储类型 ('content' | 'space')
 * @param id - ID 值
 * @returns 存储 key
 */
export const getChatStorageKey = (type: 'content' | 'space', id: number): string => {
  return `tutoro-chat-${type}-${id}`;
};

/**
 * 保存聊天记录到本地存储
 * @param type - 存储类型
 * @param id - ID 值
 * @param messages - 聊天消息数组
 */
export const saveChatToStorage = (type: 'content' | 'space', id: number, messages: ChatMessage[]): void => {
  try {
    const key = getChatStorageKey(type, id);
    localStorage.setItem(key, JSON.stringify(messages));
  } catch (error) {
    console.error('Failed to save chat to storage:', error);
  }
};

/**
 * 从本地存储加载聊天记录
 * @param type - 存储类型
 * @param id - ID 值
 * @returns 聊天消息数组
 */
export const loadChatFromStorage = (type: 'content' | 'space', id: number): ChatMessage[] => {
  try {
    const key = getChatStorageKey(type, id);
    const stored = localStorage.getItem(key);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('Failed to load chat from storage:', error);
  }
  return [];
};

/**
 * 清除本地存储的聊天记录
 * @param type - 存储类型
 * @param id - ID 值
 */
export const clearChatFromStorage = (type: 'content' | 'space', id: number): void => {
  try {
    const key = getChatStorageKey(type, id);
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Failed to clear chat from storage:', error);
  }
};

/**
 * 获取所有聊天记录的 keys
 * @param type - 存储类型（可选，如果不提供则获取所有类型）
 * @returns keys 数组
 */
export const getAllChatStorageKeys = (type?: 'content' | 'space'): string[] => {
  try {
    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('tutoro-chat-')) {
        if (!type || key.startsWith(`tutoro-chat-${type}-`)) {
          keys.push(key);
        }
      }
    }
    return keys;
  } catch (error) {
    console.error('Failed to get chat storage keys:', error);
    return [];
  }
};

/**
 * 清除指定类型的所有聊天记录
 * @param type - 存储类型
 */
export const clearAllChatsByType = (type: 'content' | 'space'): void => {
  try {
    const keys = getAllChatStorageKeys(type);
    keys.forEach(key => {
      localStorage.removeItem(key);
    });
  } catch (error) {
    console.error('Failed to clear all chats by type:', error);
  }
}; 
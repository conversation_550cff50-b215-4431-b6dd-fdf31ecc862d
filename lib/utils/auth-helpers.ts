/*
 * @Date: 2025-06-03 21:43:26
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-16 19:27:57
 * @FilePath: /tutoro-ai-front/lib/utils/auth-helpers.ts
 */
/**
 * 认证辅助工具函数（优化版）
 * 注意：Token 管理功能已移到 @/lib/api/client 中的 TokenManager
 */
import { type Locale, locales, defaultLocale } from '@/i18n.config';

/**
 * 获取重定向URL
 */
export function getRedirectUrl(): string | null {
  if (typeof window === 'undefined') return null;

  const searchParams = new URLSearchParams(window.location.search);
  return searchParams.get('redirect');
}

/**
 * 从路径中提取locale和路由路径
 */
export function extractLocaleFromPath(pathname: string): { locale: Locale; routePath: string } {
  const segments = pathname.split('/').filter(Boolean);
  
  if (segments.length === 0) {
    return { locale: defaultLocale, routePath: '/' };
  }

  const firstSegment = segments[0];
  
  if (locales.includes(firstSegment as Locale)) {
    const locale = firstSegment as Locale;
    const routePath = '/' + segments.slice(1).join('/');
    return { locale, routePath: routePath === '/' ? '/' : routePath };
  }

  return { locale: defaultLocale, routePath: pathname };
}

/**
 * 构建带locale的完整路径
 */
export function buildLocalizedPath(routePath: string, locale: Locale): string {
  if (routePath === '/') {
    return `/${locale}`;
  }
  return `/${locale}${routePath}`;
}

/**
 * 处理登录后重定向（优化版）
 */
export function handlePostLoginRedirect(router: any, currentLocale?: Locale): void {
  const redirectTo = getRedirectUrl();
  const locale = currentLocale || defaultLocale;

  if (redirectTo && redirectTo !== '/login') {
    // 如果重定向路径已经包含locale，直接跳转
    const { locale: redirectLocale, routePath } = extractLocaleFromPath(redirectTo);
    
    if (locales.includes(redirectLocale)) {
      router.push(redirectTo);
    } else {
      // 如果重定向路径没有locale，添加当前locale
      router.push(buildLocalizedPath(redirectTo, locale));
    }
  } else {
    // 默认跳转到带locale的首页
    router.push(buildLocalizedPath('/', locale));
  }
}

/**
 * 构建带重定向参数的登录URL（优化版）
 */
export function buildLoginUrl(redirectTo?: string, locale?: Locale): string {
  const currentLocale = locale || defaultLocale;
  const currentRedirect = redirectTo || (typeof window !== 'undefined' ? window.location.pathname : '');
  
  if (currentRedirect && currentRedirect !== '/login') {
    const loginPath = buildLocalizedPath('/login', currentLocale);
    return `${loginPath}?redirect=${encodeURIComponent(currentRedirect)}`;
  }
  
  return buildLocalizedPath('/login', currentLocale);
}

/**
 * 检查路径是否需要认证
 */
export function isProtectedRoute(routePath: string): boolean {
  const protectedRoutes = [
    '/profile',
    '/exam',
    '/history',
    '/notes',
    '/invite',
    '/pricing',
    '/space',
    '/demo'
  ];
  
  return protectedRoutes.some(route => routePath.startsWith(route));
}

/**
 * 检查路径是否是公开路由
 */
export function isPublicRoute(routePath: string): boolean {
  const publicRoutes = ['/', '/login', '/contact', '/guide'];
  
  return publicRoutes.some(route => {
    if (route === '/') {
      return routePath === '/';
    }
    return routePath.startsWith(route);
  });
}

/**
 * 检查路径是否是认证路由（已登录用户不应访问）
 */
export function isAuthRoute(routePath: string): boolean {
  const authRoutes = ['/login'];
  return authRoutes.some(route => routePath.startsWith(route));
}

/**
 * 获取语言偏好（从多个来源）
 */
export function getPreferredLocale(): Locale {
  if (typeof window === 'undefined') return defaultLocale;

  // 1. 检查localStorage
  const stored = localStorage.getItem('preferred_locale');
  if (stored && locales.includes(stored as Locale)) {
    return stored as Locale;
  }

  // 2. 检查cookie
  const cookies = document.cookie.split(';').reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split('=');
    acc[key] = value;
    return acc;
  }, {} as Record<string, string>);

  const cookieLocale = cookies['preferred_locale'];
  if (cookieLocale && locales.includes(cookieLocale as Locale)) {
    return cookieLocale as Locale;
  }

  // 3. 检查浏览器语言
  const browserLang = navigator.language;
  if (browserLang.startsWith('zh-HK') || browserLang.startsWith('zh-TW')) {
    return 'zh-HK';
  }
  if (browserLang.startsWith('zh')) {
    return 'zh';
  }
  if (browserLang.startsWith('en')) {
    return 'en';
  }

  return defaultLocale;
}

/**
 * 验证和规范化locale
 */
export function validateLocale(locale: string): Locale {
  if (locales.includes(locale as Locale)) {
    return locale as Locale;
  }
  return defaultLocale;
}

/**
 * 邀请人本地存储工具
 * 支持7天过期时间，登录/注册后清除
 */

const REFERRAL_KEY = 'tutoro-referral-data';
const REFERRAL_EXPIRY_DAYS = 7;

interface ReferralData {
  recommenderId: string;
  timestamp: number;
  expiresAt: number;
}

/**
 * 保存邀请人信息到本地存储
 * @param recommenderId - 邀请人ID
 */
export const saveReferralData = (recommenderId: string): void => {
  try {
    const timestamp = Date.now();
    const expiresAt = timestamp + (REFERRAL_EXPIRY_DAYS * 24 * 60 * 60 * 1000); // 7天后过期
    
    const referralData: ReferralData = {
      recommenderId,
      timestamp,
      expiresAt
    };
    
    localStorage.setItem(REFERRAL_KEY, JSON.stringify(referralData));
    console.log('Referral data saved:', recommenderId);
  } catch (error) {
    console.error('Failed to save referral data:', error);
  }
};

/**
 * 获取邀请人信息（检查是否过期）
 * @returns 邀请人ID，如果过期或不存在则返回null
 */
export const getReferralData = (): string | null => {
  try {
    const stored = localStorage.getItem(REFERRAL_KEY);
    if (!stored) {
      return null;
    }
    
    const referralData: ReferralData = JSON.parse(stored);
    const now = Date.now();
    
    // 检查是否过期
    if (now > referralData.expiresAt) {
      console.log('Referral data expired, removing...');
      clearReferralData();
      return null;
    }
    
    console.log('Valid referral data found:', referralData.recommenderId);
    return referralData.recommenderId;
  } catch (error) {
    console.error('Failed to get referral data:', error);
    return null;
  }
};

/**
 * 清除邀请人信息（登录/注册后调用）
 */
export const clearReferralData = (): void => {
  try {
    localStorage.removeItem(REFERRAL_KEY);
    console.log('Referral data cleared');
  } catch (error) {
    console.error('Failed to clear referral data:', error);
  }
};

/**
 * 检查是否有有效的邀请人信息
 * @returns boolean
 */
export const hasValidReferralData = (): boolean => {
  return getReferralData() !== null;
};

/**
 * 获取邀请人信息的详细状态（用于调试）
 * @returns 详细的邀请人信息状态
 */
export const getReferralStatus = (): {
  hasReferral: boolean;
  recommenderId: string | null;
  isExpired: boolean;
  remainingDays?: number;
} => {
  try {
    const stored = localStorage.getItem(REFERRAL_KEY);
    if (!stored) {
      return {
        hasReferral: false,
        recommenderId: null,
        isExpired: false
      };
    }
    
    const referralData: ReferralData = JSON.parse(stored);
    const now = Date.now();
    const isExpired = now > referralData.expiresAt;
    const remainingTime = referralData.expiresAt - now;
    const remainingDays = Math.ceil(remainingTime / (24 * 60 * 60 * 1000));
    
    return {
      hasReferral: true,
      recommenderId: referralData.recommenderId,
      isExpired,
      remainingDays: isExpired ? 0 : Math.max(0, remainingDays)
    };
  } catch (error) {
    console.error('Failed to get referral status:', error);
    return {
      hasReferral: false,
      recommenderId: null,
      isExpired: false
    };
  }
}; 
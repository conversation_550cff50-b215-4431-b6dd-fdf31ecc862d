/*
 * @Date: 2025-06-13 12:34:09
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-13 12:36:18
 * @FilePath: /tutoro-ai-front/lib/utils/theme.ts
 */
/**
 * 判断当前是否处于深色模式
 * @param theme 当前主题值 ('light' | 'dark' | 'system')
 * @returns boolean 是否处于深色模式
 */
export const isDarkMode = (theme: string | undefined): boolean => {
  if (theme === 'dark') return true;
  if (theme === 'light') return false;
  // 当主题为 system 或 undefined 时，检查系统主题
  // 检查是否在浏览器环境中
  if (typeof window === 'undefined') {
    // 在服务端渲染时，默认返回 false（浅色模式）
    return false;
  }
  return window.matchMedia('(prefers-color-scheme: dark)').matches;
}; 
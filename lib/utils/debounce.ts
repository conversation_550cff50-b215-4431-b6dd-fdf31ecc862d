/**
 * 防止异步函数重复执行的工具
 */
export class AsyncExecutionGuard {
  private static executingFunctions = new Map<string, boolean>();

  /**
   * 防止异步函数重复执行
   * @param key 唯一标识符
   * @param fn 要执行的异步函数
   * @returns Promise<T> | null 如果正在执行则返回null，否则返回函数执行结果
   */
  static async guard<T>(key: string, fn: () => Promise<T>): Promise<T | null> {
    if (this.executingFunctions.get(key)) {
      console.log(`Function with key "${key}" is already executing, skipping...`);
      return null;
    }

    this.executingFunctions.set(key, true);
    
    try {
      const result = await fn();
      return result;
    } finally {
      this.executingFunctions.delete(key);
    }
  }

  /**
   * 检查函数是否正在执行
   * @param key 唯一标识符
   * @returns boolean
   */
  static isExecuting(key: string): boolean {
    return this.executingFunctions.get(key) || false;
  }

  /**
   * 手动清除执行状态（谨慎使用）
   * @param key 唯一标识符
   */
  static clear(key: string): void {
    this.executingFunctions.delete(key);
  }
}

/**
 * 简单的防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

import { useRef, useCallback } from 'react';

/**
 * 防止按钮重复点击的 Hook
 */
export function useDebounceCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  
  return useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]) as T;
} 
/*
 * @Date: 2025-06-05 15:42:46
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-24 15:31:48
 * @FilePath: /tutoro-ai-front/lib/stores/hybrid-auth-store.ts
 */
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { useState, useEffect, useCallback } from 'react';
import { TokenManager } from '@/lib/api/client';
import { toast } from 'sonner';
import { type Locale, locales, defaultLocale } from '@/i18n.config';
import { useAppDataStore } from './app-data-store';
import { useArticleCacheStore } from './article-cache-store';

// 导入拆分的模块
import { CookieManager } from './auth/cookie-manager';
import { createUserData, type User } from './auth/user-utils';
import { createSSRStorage } from './auth/ssr-storage';

// 导入认证API函数
import {
  loginWithPassword,
  registerUser,
  loginWithCaptcha,
  loginWithGoogle,
  sendCaptcha,
  fetchUserProfile,
  refreshAuthToken,
  type LoginCredentials,
  type RegisterData,
  type CaptchaLoginData,
  type GoogleLoginData
} from './auth/auth-api';

// 混合认证状态接口（简化版）
interface HybridAuthState {
  // 核心状态
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  lastLoginTime: number | null;
  preferredLocale: Locale;
  lastRefreshTime: number | null;
  isInitializingData: boolean;

  // 核心认证方法
  setUser: (user: User | null, shouldInitializeData?: boolean) => void;
  setLoading: (loading: boolean) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setPreferredLocale: (locale: Locale) => void;

  // 简化的认证方法（使用统一的API函数）
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  loginWithCaptcha: (data: CaptchaLoginData) => Promise<void>;
  loginWithGoogle: (data: GoogleLoginData) => Promise<void>;
  sendCaptcha: (email: string) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  clearAuth: () => void;

  // 数据初始化方法
  initializeUserData: () => Promise<void>;
  initializeUserPreferences: () => Promise<void>;

  // 同步方法
  syncToCookies: () => void;
  syncFromCookies: () => void;

  // 工具方法
  isTokenExpiring: () => boolean;
  isTokenExpired: () => boolean;
  isTokenValid: () => boolean;
  getAuthInfo: () => { hasToken: boolean; isExpiring: boolean; user: User | null };
  refreshToken: () => Promise<void>;
  initializeAuth: () => Promise<void>;
}





// 创建混合认证 store
export const useHybridAuthStore = create<HybridAuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      isLoading: false,
      isAuthenticated: false,
      lastLoginTime: null,
      preferredLocale: defaultLocale,
      lastRefreshTime: null,
      isInitializingData: false,

      // 设置用户信息
      setUser: (user, shouldInitializeData = true) => {
        set({
          user,
          isAuthenticated: !!user,
          lastLoginTime: user ? Date.now() : null
        });

        // 自动同步到cookie
        get().syncToCookies();

        // 如果是新登录的用户，异步初始化数据（避免阻塞用户体验）
        if (user && shouldInitializeData) {
          // 异步执行初始化，不阻塞UI
          setTimeout(async () => {
            try {
              await Promise.all([get().initializeUserData(), get().initializeUserPreferences()]);
            } catch (error) {
              console.error('用户数据初始化出错:', error);
            }
          }, 100);
        }
      },

      // 设置加载状态
      setLoading: (isLoading) => set({ isLoading }),

      // 设置认证状态
      setAuthenticated: (isAuthenticated) => {
        set({ isAuthenticated });
        get().syncToCookies();
      },

      // 设置语言偏好
      setPreferredLocale: (locale) => {
        set({ preferredLocale: locale });

        // 同步到 TokenManager 和 cookie
        TokenManager.setPreferredLocale(locale);
        CookieManager.set('preferred_locale', locale, 365 * 24 * 60 * 60); // 1年

        toast.success(`Language changed to ${locale}`);
      },

      // 简化的登录方法
      login: async (credentials) => {
        const { setLoading, setUser } = get();

        try {
          setLoading(true);

          // 使用统一的API函数
          const userData = await loginWithPassword(credentials);
          setUser(userData);

          // 登录成功后刷新用户信息，确保数据最新
          try {
            await get().refreshUser();
          } catch (error) {
            console.warn('登录后刷新用户信息失败:', error);
            // 不抛出错误，因为登录已经成功
          }
        } finally {
          setLoading(false);
        }
      },

      // 简化的注册方法
      register: async (data) => {
        const { setLoading, setUser } = get();

        try {
          setLoading(true);

          // 使用统一的API函数
          const userData = await registerUser(data);
          setUser(userData);

          // 注册成功后刷新用户信息，确保数据最新
          try {
            await get().refreshUser();
          } catch (error) {
            console.warn('注册后刷新用户信息失败:', error);
            // 不抛出错误，因为注册已经成功
          }

          toast.success('注册成功');
        } finally {
          setLoading(false);
        }
      },

      // 简化的验证码登录方法
      loginWithCaptcha: async (data) => {
        const { setLoading, setUser } = get();

        try {
          setLoading(true);

          // 使用统一的API函数
          const userData = await loginWithCaptcha(data);
          setUser(userData);

          // 验证码登录成功后刷新用户信息，确保数据最新
          try {
            await get().refreshUser();
          } catch (error) {
            console.warn('验证码登录后刷新用户信息失败:', error);
            // 不抛出错误，因为登录已经成功
          }
        } finally {
          setLoading(false);
        }
      },

      // 简化的Google登录方法
      loginWithGoogle: async (data) => {
        const { setLoading, setUser } = get();

        try {
          setLoading(true);

          // 使用统一的API函数
          const userData = await loginWithGoogle(data);
          setUser(userData);

          // Google登录成功后刷新用户信息，确保数据最新
          try {
            await get().refreshUser();
          } catch (error) {
            console.warn('Google登录后刷新用户信息失败:', error);
            // 不抛出错误，因为登录已经成功
          }
        } finally {
          setLoading(false);
        }
      },

      // 简化的发送验证码方法
      sendCaptcha: async (email) => {
        // 使用统一的API函数
        await sendCaptcha(email);
      },

      // 登出方法
      logout: async () => {
        try {
          const { clearAuth } = get();
          // 清除所有认证数据
          TokenManager.clearTokens();
          clearAuth();
        } catch (error) {
          console.error('Logout error:', error);
          toast.error('Error during logout');
        }
      },

      // 简化的刷新用户信息方法
      refreshUser: async () => {
        const { setUser, setLoading, lastRefreshTime } = get();

        // 防重复调用: 如果距离上次刷新不到5秒，跳过
        const now = Date.now();
        if (lastRefreshTime && now - lastRefreshTime < 5000) {
          console.log('Skipping refreshUser - called too recently');
          return;
        }

        // 检查是否有token
        const token = TokenManager.getToken();
        if (!token) {
          console.log('Skipping refreshUser - no token available');
          return;
        }

        try {
          setLoading(true);
          set({ lastRefreshTime: now });

          // 使用统一的API函数
          const userData = await fetchUserProfile();
          setUser(userData, false); // 不触发数据初始化，因为这是刷新操作
          console.log('User info refreshed:', userData.username);
        } catch (error: any) {
          console.error('Refresh user error:', error);

          // 如果是认证相关错误，清除认证状态
          if (error?.response?.status === 401 ||
              error?.status === 401 ||
              error?.data?.code === 'UNAUTHORIZED' ||
              error?.message?.includes('登录已过期')) {
            console.warn('Authentication expired, clearing auth state');
            get().clearAuth();
          }

          throw error; // 重新抛出错误供调用者处理
        } finally {
          setLoading(false);
        }
      },

      // 清除认证状态
      clearAuth: () => {
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          lastLoginTime: null,
          lastRefreshTime: null,
          isInitializingData: false
        });

        // 清除应用数据
        const appDataStore = useAppDataStore.getState();
        appDataStore.clearAppData();
        // 清除文章缓存数据
        const articleCacheStore = useArticleCacheStore.getState();
        articleCacheStore.clearAllCache();
        // 清除认证相关的cookie（保留语言偏好）
        CookieManager.remove('isAuthenticated');
      },

      // 同步状态到cookie（供中间件使用）- 只传递登录状态，不传递token
      syncToCookies: () => {
        const { isAuthenticated, preferredLocale } = get();

        // 只同步登录状态（不传递token）
        CookieManager.set('isAuthenticated', isAuthenticated ? 'true' : 'false', 7 * 24 * 60 * 60); // 7天

        // 同步语言偏好
        CookieManager.set('preferred_locale', preferredLocale, 365 * 24 * 60 * 60); // 1年
      },

      // 从cookie同步状态（页面初始化时）
      syncFromCookies: () => {
        const cookieAuth = CookieManager.get('isAuthenticated');
        const cookieLocale = CookieManager.get('preferred_locale');
        const localToken = TokenManager.getToken();

        // 检查认证状态一致性（优先验证token）
        if (cookieAuth === 'true' && !localToken) {
          // Cookie显示已登录但localStorage没有token，可能token过期了
          console.warn('Cookie shows authenticated but no local token found - clearing cookie auth state');
          set({ isAuthenticated: false, user: null });
          CookieManager.set('isAuthenticated', 'false');
        } else if (cookieAuth === 'false' && localToken) {
          // Cookie显示未登录但localStorage有token，检查token是否有效
          console.warn('Cookie shows unauthenticated but local token exists - validating token');

          // 检查token是否有效
          if (!TokenManager.isTokenValid() || TokenManager.isTokenExpired()) {
            console.warn('Local token is invalid or expired, clearing token');
            TokenManager.clearTokens();
            set({ isAuthenticated: false, user: null });
          } else if (TokenManager.isTokenExpiring()) {
            console.warn('Local token is expiring but still valid, updating cookie auth state');
            set({ isAuthenticated: true });
            CookieManager.set('isAuthenticated', 'true');
            // 可以选择在这里触发token刷新
          } else {
            // token仍然有效，信任localStorage的token，更新cookie状态
            console.log('Local token is valid, updating cookie auth state');
            set({ isAuthenticated: true });
            CookieManager.set('isAuthenticated', 'true');
          }
        } else if (cookieAuth === 'true' && localToken) {
          // 状态一致，都显示已登录
          set({ isAuthenticated: true });
        } else {
          // 都显示未登录，确保状态清除
          set({ isAuthenticated: false, user: null });
        }

        // 同步语言偏好
        if (cookieLocale && locales.includes(cookieLocale as Locale)) {
          const currentLocale = get().preferredLocale;
          if (currentLocale !== cookieLocale) {
            set({ preferredLocale: cookieLocale as Locale });
            TokenManager.setPreferredLocale(cookieLocale);
          }
        }
      },

      // 检查token是否即将过期
      isTokenExpiring: () => {
        return TokenManager.isTokenExpiring();
      },

      // 检查token是否已过期
      isTokenExpired: () => {
        return TokenManager.isTokenExpired();
      },

      // 检查token是否有效
      isTokenValid: () => {
        return TokenManager.isTokenValid();
      },

      // 获取认证信息
      getAuthInfo: () => {
        const { user } = get();
        const tokenInfo = TokenManager.getTokenInfo();

        return {
          hasToken: tokenInfo.hasToken,
          isExpiring: tokenInfo.isExpiring || false,
          user
        };
      },

      // 简化的刷新Token方法
      refreshToken: async () => {
        try {
          // 使用统一的API函数
          const userData = await refreshAuthToken();

          if (userData) {
            get().setUser(userData, false); // 不触发数据初始化
          }

          console.log('Token refreshed successfully');
        } catch (error: any) {
          console.error('Refresh token error:', error);

          // 如果刷新失败，清除认证状态
          get().clearAuth();
          throw error;
        }
      },

      // 初始化认证状态
      initializeAuth: async () => {
        try {
          // 同步cookie状态
          get().syncFromCookies();

          // 检查是否有token
          const tokenInfo = TokenManager.getTokenInfo();
          let shouldInitializeData = false;

          if (tokenInfo.hasToken && get().isAuthenticated) {
            // 如果有token且状态显示已认证，但没有用户信息，则获取用户信息
            if (!get().user) {
              try {
                await get().refreshUser();
                shouldInitializeData = true; // 用户信息刷新成功，需要初始化数据
              } catch (error) {
                console.warn('Failed to fetch user info during initialization:', error);
                // 如果获取用户信息失败，清除认证状态
                get().clearAuth();
                return;
              }
            } else {
              // 用户已登录且有用户信息，检查是否需要初始化数据
              shouldInitializeData = true;
            }

            // 如果token即将过期，尝试刷新
            if (tokenInfo.isExpiring) {
              try {
                await get().refreshToken();
              } catch (error) {
                console.warn('Failed to refresh token during initialization:', error);
              }
            }

            // 如果需要初始化数据，异步执行
            if (shouldInitializeData) {
              setTimeout(async () => {
                try {
                  await Promise.all([get().initializeUserData(), get().initializeUserPreferences()]);
                } catch (error) {
                  console.error('认证初始化后数据初始化失败:', error);
                }
              }, 100);
            }
          } else if (!tokenInfo.hasToken && get().isAuthenticated) {
            // 如果没有token但状态显示已认证，清除状态
            get().clearAuth();
          }
        } catch (error) {
          console.error('Auth initialization error:', error);
          // 初始化失败，清除认证状态
          get().clearAuth();
        }
      },

      // 新增：数据初始化方法
      initializeUserData: async () => {
        const { user } = get();
        if (!user) return;

        try {
          set({ isInitializingData: true });
          console.log('🚀 开始初始化用户数据...');

          // 1. 初始化应用数据 (models, spaces, history)
          const appDataStore = useAppDataStore.getState();
          if (!appDataStore.isInitialized) {
            console.log('📊 初始化应用数据...');
            await appDataStore.initializeAppData();
          }

          // 2. 同步用户语言偏好到 TokenManager
          const { preferredLocale } = get();
          TokenManager.setPreferredLocale(preferredLocale);

          // 3. 设置用户特定的偏好（如有需要）
          // 这里可以调用用户设置相关的API

          console.log('✅ 用户数据初始化完成');
        } catch (error) {
          console.error('❌ 用户数据初始化失败:', error);
          toast.error('数据初始化失败，请刷新页面重试');
        } finally {
          set({ isInitializingData: false });
        }
      },

      initializeUserPreferences: async () => {
        const { user, preferredLocale } = get();
        if (!user) return;

        try {
          console.log('⚙️ 初始化用户偏好设置...');

          // 1. 确保语言偏好同步
          TokenManager.setPreferredLocale(preferredLocale);
          CookieManager.set('preferred_locale', preferredLocale, 365 * 24 * 60 * 60);

          // 2. 从用户资料中恢复保存的偏好设置
          // 如果 API 返回了用户的偏好设置，在这里应用它们

          // 3. 初始化主题偏好（如果用户有保存的主题设置）
          // 这里可以根据用户资料设置主题

          // 4. 预加载用户常用的数据
          // 比如最近访问的空间、常用的AI模型等

          console.log('✅ 用户偏好设置初始化完成');
        } catch (error) {
          console.error('❌ 用户偏好设置初始化失败:', error);
          // 偏好设置初始化失败不应该阻止用户继续使用
        }
      }
    }),
    {
      name: 'hybrid-auth-storage',
      storage: createJSONStorage(() => createSSRStorage()),

      // 配置持久化字段 (lastRefreshTime 不持久化，每次启动重置)
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        lastLoginTime: state.lastLoginTime,
        preferredLocale: state.preferredLocale
      }),

      // 水合完成后的处理
      onRehydrateStorage: () => (state) => {
        if (state && typeof window !== 'undefined') {
          // 只在客户端水合后处理
          setTimeout(() => {
            state.syncFromCookies();
            state.syncToCookies();
          }, 0);
        }
      },

      // 跳过水合检查，防止 SSR 不匹配
      skipHydration: typeof window === 'undefined'
    }
  )
);

// SSR 安全的 hook 包装器
const useSSRSafeStore = <T>(selector: (state: HybridAuthState) => T, defaultValue: T): T => {
  const [mounted, setMounted] = useState(false);
  const storeValue = useHybridAuthStore(selector);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return defaultValue;
  }

  return storeValue;
};

// 优化的认证hooks
export const useHybridAuth = () => {
  const user = useSSRSafeStore((state) => state.user, null);
  const isLoading = useSSRSafeStore((state) => state.isLoading, false);
  const isAuthenticated = useSSRSafeStore((state) => state.isAuthenticated, false);
  const isInitializingData = useSSRSafeStore((state) => state.isInitializingData, false);
  const preferredLocale = useSSRSafeStore((state) => state.preferredLocale, defaultLocale);
  const login = useHybridAuthStore((state) => state.login);
  const register = useHybridAuthStore((state) => state.register);
  const loginWithCaptcha = useHybridAuthStore((state) => state.loginWithCaptcha);
  const loginWithGoogle = useHybridAuthStore((state) => state.loginWithGoogle);
  const sendCaptcha = useHybridAuthStore((state) => state.sendCaptcha);
  const logout = useHybridAuthStore((state) => state.logout);
  const _refreshUser = useHybridAuthStore((state) => state.refreshUser);
  const refreshToken = useHybridAuthStore((state) => state.refreshToken);
  const initializeAuth = useHybridAuthStore((state) => state.initializeAuth);
  const initializeUserData = useHybridAuthStore((state) => state.initializeUserData);
  const initializeUserPreferences = useHybridAuthStore((state) => state.initializeUserPreferences);
  const setPreferredLocale = useHybridAuthStore((state) => state.setPreferredLocale);

  // 使用 useCallback 稳定 refreshUser 函数引用
  const refreshUser = useCallback(async () => {
    return await _refreshUser();
  }, [_refreshUser]);

  return {
    user,
    isLoading,
    isAuthenticated,
    isInitializingData,
    preferredLocale,
    login,
    register,
    loginWithCaptcha,
    loginWithGoogle,
    sendCaptcha,
    logout,
    refreshUser,
    refreshToken,
    initializeAuth,
    initializeUserData,
    initializeUserPreferences,
    setPreferredLocale
  };
};

// 性能优化的选择性hooks
export const useHybridUser = () => useSSRSafeStore((state) => state.user, null);
export const useHybridIsAuthenticated = () => useSSRSafeStore((state) => state.isAuthenticated, false);
export const useHybridAuthLoading = () => useSSRSafeStore((state) => state.isLoading, false);
export const useHybridPreferredLocale = () => useSSRSafeStore((state) => state.preferredLocale, defaultLocale);
export const useHybridIsInitializingData = () => useSSRSafeStore((state) => state.isInitializingData, false);

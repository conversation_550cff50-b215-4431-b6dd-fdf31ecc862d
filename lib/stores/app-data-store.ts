/*
 * @Date: 2025-06-16 14:48:14
 * @LastEditors: yosan
 * @LastEditTime: 2025-07-08 00:02:18
 * @FilePath: /tutoro-ai-front/lib/stores/app-data-store.ts
 */
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { toast } from 'sonner';

// API导入
import { getInfraModelListModel } from '@/servers/api/damoxing';
import {
  getSpacePageSpace,
  postSpaceCreateSpace,
  postSpaceDeleteSpaceId,
  postSpaceUpdateSpaceId,
  getSpaceFindSpaceId
} from '@/servers/api/kongjian';
import {
  getContentPageRecentContent,
  postContentDeleteContentId,
  postContentUpdateContentId,
  postContentCreateContent
} from '@/servers/api/wendangneirong';
import appConfig from '@/config/app';

// 大模型类型
interface Model {
  id?: number;
  name?: string;
  description?: string;
  modelId?: string;
  cached?: boolean;
  createAt?: string;
  updateAt?: string;
}

// 空间类型
interface Space {
  id?: number | string;
  title?: string;
  description?: string;
  memberId?: number;
  createAt?: string;
  updateAt?: string;
}

// 历史记录类型
interface HistoryRecord {
  id: string;
  title: string;
  type: 'study' | 'exam' | 'chat' | 'other';
  content: string;
  createdAt: Date;
  spaceId: string;
  spaceName: string;
}

// 分页数据接口
interface PaginatedHistoryData {
  records: HistoryRecord[];
  total: number;
  page: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

// 应用数据状态接口
interface AppDataState {
  // 核心数据
  models: Model[];
  spaces: Space[];
  historyRecords: HistoryRecord[];

  // 分页相关状态
  historyPagination: {
    currentPage: number;
    pageSize: number;
    total: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };

  // 加载状态
  isLoadingModels: boolean;
  isLoadingSpaces: boolean;
  isLoadingHistoryRecords: boolean;
  isInitialized: boolean;

  // 核心方法
  initializeAppData: () => Promise<void>;

  // 刷新数据
  refreshModels: () => Promise<void>;
  refreshSpaces: () => Promise<void>;
  refreshHistoryRecords: (page?: number, pageSize?: number) => Promise<PaginatedHistoryData>;

  // 分页方法
  loadHistoryPage: (page: number, pageSize?: number) => Promise<PaginatedHistoryData>;
  resetHistoryPagination: () => void;

  // 空间管理
  removeSpace: (spaceId: string | number) => Promise<void>;
  updateSpace: (spaceId: string | number, updates: Partial<Space>) => Promise<void>;
  getSpaceById: (id: string | number) => Promise<Space | undefined>;
  addSpaceToStore: (space: Space) => void;

  // 历史记录管理
  removeHistoryRecord: (id: string) => Promise<void>;
  updateHistoryRecord: (id: string, title: string) => Promise<void>;
  createContent: (params: { spaceId?: number; uploadId: number; title?: string }) => Promise<any>;

  // 工具方法
  clearAppData: () => void;
}

// 全局初始化锁，防止重复初始化
let globalInitializationPromise: Promise<void> | null = null;

// 创建应用数据 store
export const useAppDataStore = create<AppDataState>()(
  persist(
    (set, get) => ({
      // 大模型数据
      models: [],
      // 空间数据
      spaces: [],
      // 历史记录数据
      historyRecords: [],

      // 分页相关状态
      historyPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        hasNextPage: false,
        hasPrevPage: false
      },

      // 模型加载状态
      isLoadingModels: false,
      // 空间加载状态
      isLoadingSpaces: false,
      // 历史记录加载状态
      isLoadingHistoryRecords: false,
      // 初始化状态
      isInitialized: false,

      // 初始化应用数据
      initializeAppData: async () => {
        const state = get();

        console.log('🚀 initializeAppData called', {
          isInitialized: state.isInitialized,
          hasModels: state.models.length > 0,
          hasSpaces: state.spaces.length > 0,
          hasHistoryRecords: state.historyRecords.length > 0
        });

        // 创建初始化 Promise
        globalInitializationPromise = (async () => {
          try {
            const promises = [];

            // 并行加载所有数据
            console.log('📊 Loading models and spaces...');
            promises.push(get().refreshModels());
            promises.push(get().refreshSpaces());
            promises.push(get().refreshHistoryRecords());
            // 历史记录现在通过页面级别的API hook直接获取，不需要在初始化时加载

            await Promise.all(promises);

            // 标记初始化完成
            set({
              isInitialized: true
            });

            console.log('✅ App data initialization completed successfully');
          } catch (error) {
            console.error('❌ App data initialization failed:', error);
            toast.error('Failed to initialize app data');
            throw error;
          }
        })();

        await globalInitializationPromise;
        globalInitializationPromise = null;
      },

      // 刷新大模型列表
      refreshModels: async () => {
        set({ isLoadingModels: true });
        try {
          const response = await getInfraModelListModel();

          if (response?.data) {
            const models: Model[] = response.data.map((item) => ({
              id: item.id || 0,
              name: item.id === 1 ? appConfig.aiName : item.name || '',
              description: item.description,
              modelId: item.modelId || '',
              cached: item.cached,
              createAt: item.createAt,
              updateAt: item.updateAt
            }));

            set({ models, isLoadingModels: false });
            console.log(`✅ Loaded ${models.length} models from API`);
          } else {
            console.log('⚠️ No models data in API response');
            set({ isLoadingModels: false });
          }
        } catch (error: any) {
          console.error('❌ Models loading error:', error);
          set({ isLoadingModels: false });
          toast.error('Failed to load models');
          throw error;
        }
      },

      // 刷新空间列表
      refreshSpaces: async () => {
        set({ isLoadingSpaces: true });
        try {
          const response = await getSpacePageSpace({ page: 1, pageSize: 100 });

          if (response?.data) {
            const spaces: Space[] =
              response.data.record?.map((item) => ({
                id: item.id || 0,
                title: item.title || '',
                description: item.description,
                memberId: item.memberId || 0,
                createAt: item.createAt,
                updateAt: item.updateAt
              })) || [];

            set({ spaces, isLoadingSpaces: false });
            console.log(`✅ Loaded ${spaces.length} spaces from API`);
          }
        } catch (error: any) {
          console.error('❌ Spaces loading error:', error);
          set({ isLoadingSpaces: false });
          toast.error('Failed to load spaces');
          throw error;
        }
      },

      // 刷新历史记录
      refreshHistoryRecords: async (page?: number, pageSize?: number) => {
        set({ isLoadingHistoryRecords: true });

        try {
          console.log('🔄 Fetching history records from API...');
          const currentPage = page || 1;
          const currentPageSize = pageSize || 10;
          const response = await getContentPageRecentContent({ page: currentPage, pageSize: currentPageSize });

          if (response?.data) {
            const historyRecords: HistoryRecord[] =
              response.data.record?.map((item) => ({
                id: String(item.id || ''),
                title: item.title || '',
                type: 'study' as const, // API 返回的是内容记录，默认设为 study 类型
                content: item.fileUrl || '', // 使用 fileUrl 作为内容标识
                createdAt: new Date(item.createAt || Date.now()),
                spaceId: String(item.space?.id || ''),
                spaceName: item.space?.title || ''
              })) || [];

            const paginationData = {
              records: historyRecords,
              total: response.data.totalCount || 0,
              page: currentPage,
              pageSize: currentPageSize,
              hasNextPage: currentPage < (response.data.pageCount || 1),
              hasPrevPage: currentPage > 1
            };

            // 更新分页状态
            set({
              historyRecords,
              historyPagination: {
                currentPage,
                pageSize: currentPageSize,
                total: response.data.totalCount || 0,
                hasNextPage: currentPage < (response.data.pageCount || 1),
                hasPrevPage: currentPage > 1
              },
              isLoadingHistoryRecords: false
            });

            console.log(`✅ Loaded ${historyRecords.length} history records from API (page ${currentPage})`);
            return paginationData;
          }

          const emptyPaginationData = {
            records: [],
            total: 0,
            page: currentPage,
            pageSize: currentPageSize,
            hasNextPage: false,
            hasPrevPage: false
          };

          set({
            historyRecords: [],
            historyPagination: {
              currentPage,
              pageSize: currentPageSize,
              total: 0,
              hasNextPage: false,
              hasPrevPage: false
            },
            isLoadingHistoryRecords: false
          });

          return emptyPaginationData;
        } catch (error: any) {
          console.error('❌ History records loading error:', error);
          toast.error('Failed to load history records');
          const currentPage = page || 1;
          const currentPageSize = pageSize || 10;

          const errorPaginationData = {
            records: [],
            total: 0,
            page: currentPage,
            pageSize: currentPageSize,
            hasNextPage: false,
            hasPrevPage: false
          };

          set({
            historyRecords: [],
            historyPagination: {
              currentPage,
              pageSize: currentPageSize,
              total: 0,
              hasNextPage: false,
              hasPrevPage: false
            },
            isLoadingHistoryRecords: false
          });

          return errorPaginationData;
        }
      },

      // 加载特定页的历史记录
      loadHistoryPage: async (page: number, pageSize?: number) => {
        const currentPageSize = pageSize || get().historyPagination.pageSize;
        return await get().refreshHistoryRecords(page, currentPageSize);
      },

      // 重置历史记录分页状态
      resetHistoryPagination: () => {
        set((state) => ({
          historyPagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0,
            hasNextPage: false,
            hasPrevPage: false
          }
        }));
      },

      // 删除空间
      removeSpace: async (spaceId: string | number) => {
        try {
          // 先删除空间的API调用
          await postSpaceDeleteSpaceId({ id: Number(spaceId) });

          // 获取当前状态
          const state = get();

          // 查找属于该空间的所有历史记录
          const recordsToDelete = state.historyRecords.filter((record) => String(record.spaceId) === String(spaceId));

          console.log(`🗑️ Found ${recordsToDelete.length} history records to delete for space ${spaceId}`);

          // 删除每个历史记录（调用API）
          const deletePromises = recordsToDelete.map(async (record) => {
            try {
              await postContentDeleteContentId({ id: Number(record.id) });
              console.log(`✅ Deleted content record: ${record.id}`);
            } catch (error) {
              console.error(`❌ Failed to delete content record ${record.id}:`, error);
              // 即使单个记录删除失败，也继续删除其他记录
            }
          });

          // 等待所有删除操作完成（但不阻塞整个流程）
          await Promise.allSettled(deletePromises);

          // 更新本地状态：删除空间和相关历史记录
          set((state) => ({
            spaces: state.spaces.filter((space) => String(space.id) !== String(spaceId)),
            historyRecords: state.historyRecords.filter((record) => String(record.spaceId) !== String(spaceId))
          }));

          console.log(`✅ Space ${spaceId} and ${recordsToDelete.length} related records deleted successfully`);
        } catch (error: any) {
          console.error('❌ Delete space error:', error);
          toast.error('Failed to delete space');
          throw error;
        }
      },

      // 更新空间
      updateSpace: async (spaceId: string | number, updates: Partial<Space>) => {
        try {
          const response = await postSpaceUpdateSpaceId(
            { id: Number(spaceId) },
            {
              title: updates.title,
              description: updates.description
            }
          );

          if (response?.data) {
            set((state) => ({
              spaces: state.spaces.map((space) =>
                String(space.id) === String(spaceId) ? { ...space, ...response.data } : space
              )
            }));
          }
        } catch (error: any) {
          console.error('❌ Update space error:', error);
          throw error;
        }
      },

      // 根据ID获取空间
      getSpaceById: async (id: string | number) => {
        try {
          const response = await getSpaceFindSpaceId({ id: Number(id) });

          if (response?.data) {
            return {
              id: response.data.id,
              title: response.data.title,
              description: response.data.description,
              memberId: response.data.memberId,
              createAt: response.data.createAt,
              updateAt: response.data.updateAt
            };
          }

          return undefined;
        } catch (error: any) {
          console.error('❌ Get space by id error:', error);
          return undefined;
        }
      },

      // 删除历史记录
      removeHistoryRecord: async (id: string) => {
        try {
          await postContentDeleteContentId({ id: Number(id) });
          set((state) => ({
            historyRecords: state.historyRecords.filter((record) => record.id !== id)
          }));
        } catch (error: any) {
          console.error('❌ Remove history record error:', error);
          throw error;
        }
      },

      // 更新历史记录
      updateHistoryRecord: async (id: string, title: string) => {
        try {
          const response = await postContentUpdateContentId({ id: Number(id) }, { title });

          if (response?.data) {
            set((state) => ({
              historyRecords: state.historyRecords.map((record) => (record.id === id ? { ...record, title } : record))
            }));
          }
        } catch (error: any) {
          console.error('❌ Update history record error:', error);
          throw error;
        }
      },

      // 创建内容
      createContent: async (params: { spaceId?: number; uploadId: number; title?: string }) => {
        try {
          const response = await postContentCreateContent({
            spaceId: params.spaceId,
            uploadId: params.uploadId,
            title: params.title
          });

          if (response?.data) {
            // 创建新的历史记录条目
            const newHistoryRecord: HistoryRecord = {
              id: String(response.data.id),
              title: params.title || response.data.title || 'Untitled',
              type: params.uploadId === 0 ? 'chat' : 'study', // AI查询为chat类型，文件上传为study类型
              content: response.data.fileUrl || '',
              createdAt: new Date(response.data.createAt || Date.now()),
              spaceId: String(params.spaceId || ''),
              spaceName: '' // 这里可以从spaces中查找对应的spaceName
            };

            // 找到对应的space名称
            const state = get();
            const space = state.spaces.find((s) => String(s.id) === String(params.spaceId));
            if (space) {
              newHistoryRecord.spaceName = space.title || '';
            }

            // 添加到历史记录开头
            set((state) => ({
              historyRecords: [newHistoryRecord, ...state.historyRecords]
            }));

            console.log(`✅ Created content with ID: ${response.data.id}, type: ${newHistoryRecord.type}`);
            return response;
          }

          throw new Error(response?.msg || 'Failed to create content');
        } catch (error: any) {
          console.error('❌ Create content error:', error);
          throw error;
        }
      },

      // 清除所有应用数据
      clearAppData: () => {
        console.log('🧹 Clearing all app data');
        set({
          models: [],
          spaces: [],
          historyRecords: [],
          isLoadingModels: false,
          isLoadingSpaces: false,
          isLoadingHistoryRecords: false,
          isInitialized: false
        });
        globalInitializationPromise = null;
      },

      // 新增：直接添加到本地store，不调用API
      addSpaceToStore: (space: Space) => {
        set((state) => ({
          spaces: [...state.spaces, space]
        }));
      }
    }),
    {
      name: 'app-data-storage',
      version: 2,
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        models: state.models,
        spaces: state.spaces,
        historyRecords: state.historyRecords,
        isInitialized: state.isInitialized
      }),
      migrate: (persistedState: any, version: number) => {
        if (version !== 2) {
          return {
            models: [],
            spaces: [],
            historyRecords: [],
            isInitialized: false
          };
        }
        return persistedState;
      }
    }
  )
);

// 导出类型
export type { Space, HistoryRecord, Model, AppDataState };

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { Flashcard } from '@/types'

interface ChapterSection {
  id: string
  pageNumber: number
  title: string
  content: string
}

interface NewArticleChat {
  modelId: string
  aiQuery: string
  type?: 'video' | 'audio' | 'doc' // 新增类型字段
}

// 自动发送聊天数据接口
interface AutoSendChatData {
  initialInputValue: string
  initialSelectedModelId: string
  autoSend: boolean
  type?: 'video' | 'audio' | 'doc'
  endpoint?: string | null // 不同类型使用不同的 endpoint
  includeUserMessage?: boolean // 是否在消息中包含用户发送的内容
}

// ContentData 接口
interface ContentData {
  id?: number;
  title?: string;
  fileUrl?: string;
  spaceId?: number;
  createAt?: string;
  updateAt?: string;
  type?: 'video' | 'audio' | 'doc';
}

// 请求状态类型
type RequestType = 'flashcards' | 'chapters' | 'summary' | 'quizGroups';

interface ArticleCacheState {
  // Content data 缓存
  contentData: Record<number, ContentData>

  // 按 contentId 存储各种数据
  flashcards: Record<number, Flashcard[]>
  chapters: Record<number, ChapterSection[]>
  summaries: Record<number, string>
  quizGroups: Record<number, any[]>

  // 按 contentId 存储新文章聊天信息
  newArticleChat: Record<number, NewArticleChat>

  // 按 contentId 存储自动发送聊天数据
  autoSendChatData: Record<number, AutoSendChatData>

  // 请求状态跟踪 - 优化版本，支持防重复请求
  requestingStates: Record<number, Record<RequestType, boolean>>
  
  // Content data 操作方法
  setContentData: (contentId: number, contentData: ContentData) => void
  hasContentData: (contentId: number) => boolean
  getContentData: (contentId: number) => ContentData | undefined
  
  // 操作方法
  setFlashcards: (contentId: number, flashcards: Flashcard[]) => void
  setChapters: (contentId: number, chapters: ChapterSection[]) => void
  setSummary: (contentId: number, summary: string) => void
  setQuizGroups: (contentId: number, quizGroups: any[]) => void
  
  // 新文章聊天的操作方法
  setNewArticleChat: (contentId: number, newArticleChat: NewArticleChat) => void
  getNewArticleChat: (contentId: number) => NewArticleChat | undefined

  // 自动发送聊天数据的操作方法
  setAutoSendChatData: (contentId: number, data: AutoSendChatData) => void
  getAutoSendChatData: (contentId: number) => AutoSendChatData | undefined
  clearAutoSendChatData: (contentId: number) => void

  // 统一的自动发送数据设置方法 - 根据内容类型自动配置
  setNewArticleChatData: (contentId: number, contentData: ContentData, options?: {
    initialInputValue?: string
    initialSelectedModelId?: string
    autoSend?: boolean
  }) => void
  
  // 检查是否已有数据
  hasFlashcards: (contentId: number) => boolean
  hasChapters: (contentId: number) => boolean
  hasSummary: (contentId: number) => boolean
  hasQuizGroups: (contentId: number) => boolean
  
  // 请求状态管理 - 优化版本
  setRequestingState: (contentId: number, type: RequestType, requesting: boolean) => void
  isRequesting: (contentId: number, type: RequestType) => boolean
  // 新增：检查是否应该发起请求（避免重复请求）
  shouldStartRequest: (contentId: number, type: RequestType) => boolean
  
  // 清除特定内容的缓存
  clearContentCache: (contentId: number) => void
  
  // 清除所有缓存
  clearAllCache: () => void
}

export const useArticleCacheStore = create<ArticleCacheState>()(
  persist(
    (set, get) => ({
      // Content data 缓存
      contentData: {},
      
      // 缓存数据
      flashcards: {},
      // 章节
      chapters: {},
      // 摘要
      summaries: {},
      // 测验分组
      quizGroups: {},
      // 新文章聊天信息
      newArticleChat: {},

      // 自动发送聊天数据
      autoSendChatData: {},

      // 请求状态跟踪
      requestingStates: {},
      
      setContentData: (contentId, contentData) =>
        set((state) => ({
          contentData: { ...state.contentData, [contentId]: contentData }
        })),
      
      hasContentData: (contentId) => {
        return Boolean(get().contentData[contentId])
      },
      
      getContentData: (contentId) => {
        return get().contentData[contentId]
      },
      
      setFlashcards: (contentId, flashcards) =>
        set((state) => ({
          flashcards: { ...state.flashcards, [contentId]: flashcards }
        })),
      
      setChapters: (contentId, chapters) =>
        set((state) => ({
          chapters: { ...state.chapters, [contentId]: chapters }
        })),
      
      setSummary: (contentId, summary) =>
        set((state) => ({
          summaries: { ...state.summaries, [contentId]: summary }
        })),
      
      setQuizGroups: (contentId, quizGroups) =>
        set((state) => ({
          quizGroups: { ...state.quizGroups, [contentId]: quizGroups }
        })),
      
      setNewArticleChat: (contentId, newArticleChat) =>
        set((state) => ({
          newArticleChat: { ...state.newArticleChat, [contentId]: newArticleChat }
        })),
      
      getNewArticleChat: (contentId) => {
        return get().newArticleChat[contentId]
      },

      // 自动发送聊天数据的操作方法
      setAutoSendChatData: (contentId, data) =>
        set((state) => ({
          autoSendChatData: { ...state.autoSendChatData, [contentId]: data }
        })),

      getAutoSendChatData: (contentId) => {
        return get().autoSendChatData[contentId]
      },

      clearAutoSendChatData: (contentId) =>
        set((state) => {
          const newAutoSendChatData = { ...state.autoSendChatData }
          delete newAutoSendChatData[contentId]
          return { autoSendChatData: newAutoSendChatData }
        }),

      // 统一的自动发送数据设置方法 - 根据内容类型自动配置
      setNewArticleChatData: (contentId, contentData, options = {}) => {
        const {
          initialInputValue = '',
          initialSelectedModelId = '',
          autoSend = true
        } = options

        // 根据内容类型确定配置
        let endpoint: string | null = null
        let includeUserMessage = true
        let type: 'video' | 'audio' | 'doc' = 'doc'

        if (contentData.type === 'video' && !contentData.fileUrl) {
          endpoint = '/llm/video/describe'
          includeUserMessage = false
          type = 'video'
        } else if (contentData.type === 'audio' && !contentData.fileUrl) {
          endpoint = '/llm/audio/describe'
          includeUserMessage = false
          type = 'audio'
        } else if (contentData.type === 'doc') {
          endpoint = null // doc 类型使用默认 endpoint
          includeUserMessage = true
          type = 'doc'
        }

        const autoSendData: AutoSendChatData = {
          initialInputValue,
          initialSelectedModelId,
          autoSend,
          type,
          endpoint,
          includeUserMessage
        }

        // 设置到 store 中
        set((state) => ({
          autoSendChatData: { ...state.autoSendChatData, [contentId]: autoSendData }
        }))
      },
      
      hasFlashcards: (contentId) => {
        const flashcards = get().flashcards[contentId]
        return flashcards && flashcards.length > 0
      },
      
      hasChapters: (contentId) => {
        const chapters = get().chapters[contentId]
        return chapters && chapters.length > 0
      },
      
      hasSummary: (contentId) => {
        const summary = get().summaries[contentId]
        return Boolean(summary && summary.length > 0)
      },
      
      hasQuizGroups: (contentId) => {
        const quizGroups = get().quizGroups[contentId]
        return quizGroups && quizGroups.length > 0
      },
      
      setRequestingState: (contentId, type, requesting) =>
        set((state) => ({
          requestingStates: {
            ...state.requestingStates,
            [contentId]: {
              ...(state.requestingStates[contentId] || {
                flashcards: false,
                chapters: false,
                summary: false,
                quizGroups: false
              }),
              [type]: requesting
            }
          }
        })),
      
      isRequesting: (contentId, type) => {
        const contentRequestingState = get().requestingStates[contentId]
        return contentRequestingState ? contentRequestingState[type] : false
      },
      
      shouldStartRequest: (contentId, type) => {
        const state = get()
        const isCurrentlyRequesting = state.requestingStates[contentId]?.[type] || false
        
        // 检查是否已有数据
        let hasData = false
        switch (type) {
          case 'flashcards':
            hasData = state.hasFlashcards(contentId)
            break
          case 'chapters':
            hasData = state.hasChapters(contentId)
            break
          case 'summary':
            hasData = state.hasSummary(contentId)
            break
          case 'quizGroups':
            hasData = state.hasQuizGroups(contentId)
            break
        }
        
        // 只有在没有数据且没有正在请求时才应该发起请求
        return !hasData && !isCurrentlyRequesting
      },
      
      clearContentCache: (contentId) =>
        set((state) => {
          const newContentData = { ...state.contentData }
          const newFlashcards = { ...state.flashcards }
          const newChapters = { ...state.chapters }
          const newSummaries = { ...state.summaries }
          const newQuizGroups = { ...state.quizGroups }
          const newNewArticleChat = { ...state.newArticleChat }
          const newAutoSendChatData = { ...state.autoSendChatData }
          const newRequestingStates = { ...state.requestingStates }

          delete newContentData[contentId]
          delete newFlashcards[contentId]
          delete newChapters[contentId]
          delete newSummaries[contentId]
          delete newQuizGroups[contentId]
          delete newNewArticleChat[contentId]
          delete newAutoSendChatData[contentId]
          delete newRequestingStates[contentId]

          return {
            contentData: newContentData,
            flashcards: newFlashcards,
            chapters: newChapters,
            summaries: newSummaries,
            quizGroups: newQuizGroups,
            newArticleChat: newNewArticleChat,
            autoSendChatData: newAutoSendChatData,
            requestingStates: newRequestingStates
          }
        }),
      
      clearAllCache: () =>
        set(() => ({
          contentData: {},
          flashcards: {},
          chapters: {},
          summaries: {},
          quizGroups: {},
          newArticleChat: {},
          autoSendChatData: {},
          requestingStates: {}
        }))
    }),
    {
      name: 'article-cache-storage',
      // 设置持久化存储，但有过期时间
      version: 3, // 增加版本号，因为添加了新字段
      // 可以添加过期时间逻辑
      migrate: (persistedState: any, version: number) => {
        // 处理版本迁移逻辑
        if (version < 2) {
          // 从版本1迁移到版本2，添加contentData字段
          return {
            ...persistedState,
            contentData: {}
          }
        }
        if (version < 3) {
          // 从版本2迁移到版本3，添加autoSendChatData字段
          return {
            ...persistedState,
            autoSendChatData: {}
          }
        }
        return persistedState
      }
    }
  )
) 
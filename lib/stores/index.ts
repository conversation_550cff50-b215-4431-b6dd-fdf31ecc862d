// 导出认证相关
export {
  useHybridAuthStore,
  useHybridAuth,
  useHybridUser,
  useHybridIsAuthenticated,
  useHybridAuthLoading,
  useHybridPreferredLocale
} from './hybrid-auth-store';

// 导出应用数据相关（包含原UserState功能）
export { useAppDataStore } from './app-data-store';
export { useAppStore } from './app-store';

// 导出类型
export type { Space, HistoryRecord, Model, AppDataState } from './app-data-store';

// 导入 useAppDataStore 以便在便捷函数中使用
import { useAppDataStore } from './app-data-store';

// 便捷的 hook 函数（兼容旧的使用方式）
export const useMySpaces = () => useAppDataStore((state) => state.spaces);
export const useHistoryRecords = () => useAppDataStore((state) => state.historyRecords);
export const useHistoryPagination = () => useAppDataStore((state) => state.historyPagination);

// 数据相关hooks
export const useModels = () => useAppDataStore((state) => state.models);
export const useSpaces = () => {
  const spaces = useAppDataStore((state) => state.spaces);
  return {
    spaces,
    total: spaces.length,
    currentPage: 1,
    hasNext: false
  };
};

// 加载状态相关hooks
export const useIsLoadingModels = () => useAppDataStore((state) => state.isLoadingModels);
export const useIsLoadingSpaces = () => useAppDataStore((state) => state.isLoadingSpaces);
export const useIsLoadingHistoryRecords = () => useAppDataStore((state) => state.isLoadingHistoryRecords);
export const useIsInitialized = () => useAppDataStore((state) => state.isInitialized);

// 错误状态相关hooks
export const useAppDataErrors = () => {
  return {
    modelsError: null, // 目前没有错误状态，返回null
    spacesError: null
  };
};

// 保持向后兼容性的组合hook - 使用个别selectors来避免创建新对象
export const useAppDataLoading = () => {
  const isLoadingModels = useIsLoadingModels();
  const isLoadingSpaces = useIsLoadingSpaces();
  const isLoadingHistoryRecords = useIsLoadingHistoryRecords();
  const isInitialized = useIsInitialized();
  
  return { isLoadingModels, isLoadingSpaces, isLoadingHistoryRecords, isInitialized };
};

export const useAppDataInit = () => {
  const initializeAppData = useAppDataStore((state) => state.initializeAppData);
  const refreshModels = useAppDataStore((state) => state.refreshModels);
  const refreshSpaces = useAppDataStore((state) => state.refreshSpaces);

  return { initializeAppData, refreshModels, refreshSpaces };
};

export const useSpaceActions = () => {
  const removeSpace = useAppDataStore((state) => state.removeSpace);
  const updateSpace = useAppDataStore((state) => state.updateSpace);
  const getSpaceById = useAppDataStore((state) => state.getSpaceById);

  return {
    removeSpace,
    updateSpace,
    getSpaceById
  };
};

export const useHistoryActions = () => {
  const refreshHistoryRecords = useAppDataStore((state) => state.refreshHistoryRecords);
  const loadHistoryPage = useAppDataStore((state) => state.loadHistoryPage);
  const resetHistoryPagination = useAppDataStore((state) => state.resetHistoryPagination);
  const removeHistoryRecord = useAppDataStore((state) => state.removeHistoryRecord);
  
  return {
    refreshHistoryRecords,
    loadHistoryPage,
    resetHistoryPagination,
    removeHistoryRecord
  };
};

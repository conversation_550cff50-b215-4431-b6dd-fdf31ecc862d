/*
 * @Date: 2025-06-02 22:25:06
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-10 18:00:45
 * @FilePath: /tutoro-ai-front/lib/stores/app-store.ts
 */
import { create } from 'zustand';
import { AppState } from '@/types/app';

export const useAppStore = create<AppState>((set) => ({
  // 初始状态 - 所有模态框都关闭
  isLanguageModalOpen: false,
  isFeedbackModalOpen: false,
  isUpgradeModalOpen: false,

  // 聊天窗口状态
  isChatOpen: false,
  isMobile: false,

  // 文章标题初始状态
  articleTitle: '',

  // 答题状态初始值
  examProgress: {
    // 总题目数
    totalQuestions: 0,
    // 已回答题目数
    answeredQuestions: 0,
    // 当前进度
    currentProgress: 0,
    // 是否加载中
    isLoading: false,
  },

  // 语言模态框操作
  openLanguageModal: () => set({ isLanguageModalOpen: true }),
  closeLanguageModal: () => set({ isLanguageModalOpen: false }),
  toggleLanguageModal: () => set((state) => ({ 
    isLanguageModalOpen: !state.isLanguageModalOpen 
  })),

  // 反馈模态框操作
  openFeedbackModal: () => set({ isFeedbackModalOpen: true }),
  closeFeedbackModal: () => set({ isFeedbackModalOpen: false }),
  toggleFeedbackModal: () => set((state) => ({ 
    isFeedbackModalOpen: !state.isFeedbackModalOpen 
  })),

  // 升级模态框操作
  openUpgradeModal: () => set({ isUpgradeModalOpen: true }),
  closeUpgradeModal: () => set({ isUpgradeModalOpen: false }),
  toggleUpgradeModal: () => set((state) => ({ 
    isUpgradeModalOpen: !state.isUpgradeModalOpen 
  })),

  // 聊天窗口操作
  openChat: () => set({ isChatOpen: true }),
  closeChat: () => set({ isChatOpen: false }),
  toggleChat: () => set((state) => ({ 
    isChatOpen: !state.isChatOpen 
  })),
  setMobile: (isMobile: boolean) => set({ isMobile }),

  // 文章标题操作
  setArticleTitle: (title: string) => set({ articleTitle: title }),
  clearArticleTitle: () => set({ articleTitle: '' }),

  // 答题状态操作
  setExamProgress: (totalQuestions: number, answeredQuestions: number) => 
    set((state) => ({
      examProgress: {
        ...state.examProgress,
        totalQuestions,
        answeredQuestions,
        currentProgress: totalQuestions > 0 ? (answeredQuestions / totalQuestions) * 100 : 0,
      }
    })),

  setExamLoading: (isLoading: boolean) => 
    set((state) => ({
      examProgress: {
        ...state.examProgress,
        isLoading,
      }
    })),

  resetExamProgress: () => 
    set({
      examProgress: {
        totalQuestions: 0,
        answeredQuestions: 0,
        currentProgress: 0,
        isLoading: false,
      }
    }),

  // 关闭所有模态框
  closeAllModals: () => set({
    isLanguageModalOpen: false,
    isFeedbackModalOpen: false,
    isUpgradeModalOpen: false,
    isChatOpen: false,
  }),
})); 
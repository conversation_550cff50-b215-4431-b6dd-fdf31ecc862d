/**
 * 认证相关的工具函数
 */
import { TokenManager } from '@/lib/api/client';

/**
 * 检查用户是否已认证且token有效
 * @returns 是否已认证
 */
export function isUserAuthenticated(): boolean {
  const token = TokenManager.getToken();
  if (!token) return false;
  
  // 检查token是否有效且未过期
  return TokenManager.isTokenValid() && !TokenManager.isTokenExpired();
}

/**
 * 检查token是否需要刷新
 * @returns 是否需要刷新
 */
export function shouldRefreshToken(): boolean {
  const token = TokenManager.getToken();
  if (!token) return false;
  
  return TokenManager.isTokenExpiring();
}

/**
 * 清理所有认证相关数据
 */
export function clearAllAuthData(): void {
  TokenManager.clearTokens();
  
  // 清除认证相关的cookie
  if (typeof window !== 'undefined') {
    document.cookie = 'isAuthenticated=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
  }
}

/**
 * 验证认证状态的一致性
 * @param cookieAuth Cookie中的认证状态
 * @param localToken 本地存储的token
 * @returns 修正后的认证状态
 */
export function validateAuthConsistency(cookieAuth: string | null, localToken: string | null): {
  isAuthenticated: boolean;
  shouldClearCookie: boolean;
  shouldClearToken: boolean;
} {
  const cookieAuthBool = cookieAuth === 'true';
  const hasValidToken = localToken && TokenManager.isTokenValid() && !TokenManager.isTokenExpired();

  // Cookie显示已登录但没有有效token
  if (cookieAuthBool && !hasValidToken) {
    return {
      isAuthenticated: false,
      shouldClearCookie: true,
      shouldClearToken: true
    };
  }

  // Cookie显示未登录但有有效token
  if (!cookieAuthBool && hasValidToken) {
    return {
      isAuthenticated: true,
      shouldClearCookie: false,
      shouldClearToken: false
    };
  }

  // 状态一致
  return {
    isAuthenticated: Boolean(cookieAuthBool && hasValidToken),
    shouldClearCookie: false,
    shouldClearToken: false
  };
}

/**
 * 防抖函数，用于防止频繁调用
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * 节流函数，用于限制函数调用频率
 * @param func 要节流的函数
 * @param limit 时间限制（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

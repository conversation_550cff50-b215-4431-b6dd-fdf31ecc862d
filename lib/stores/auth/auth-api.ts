/**
 * 认证相关API调用的统一封装
 */
import { TokenManager } from '@/lib/api/client';
import { createUserData, type User } from './user-utils';
import { toast } from 'sonner';

// 登录凭据类型
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  password: string;
  confirmPassword: string;
  profile?: any;
}

export interface CaptchaLoginData {
  email: string;
  captcha: string;
  recommender?: string;
}

export interface GoogleLoginData {
  idToken: string;
  recommender?: string;
}

/**
 * 处理登录响应的通用函数
 * @param response API响应
 * @param fallbackData 回退数据
 * @returns 用户数据
 */
function processAuthResponse(response: any, fallbackData?: any): User {
  if (!response?.data?.token?.accessToken) {
    throw new Error(response?.msg || '认证响应格式错误');
  }

  // 保存token
  TokenManager.setToken(response.data.token.accessToken);

  // 创建用户数据
  return createUserData(response.data.profile, fallbackData);
}

/**
 * 处理API错误的通用函数
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 */
function handleAuthError(error: any, defaultMessage: string): never {
  console.error('Auth API error:', error);
  
  const errorMessage = error?.data?.msg || 
                      error?.response?.data?.msg || 
                      error?.message || 
                      defaultMessage;
  
  toast.error(errorMessage);
  throw error;
}

/**
 * 用户名密码登录
 */
export async function loginWithPassword(credentials: LoginCredentials): Promise<User> {
  try {
    const { postMemberAuthSignIn } = await import('@/servers/api/huiyuanzhuceyudenglu');
    const response = await postMemberAuthSignIn(credentials);
    return processAuthResponse(response, { username: credentials.username });
  } catch (error) {
    handleAuthError(error, '登录失败');
  }
}

/**
 * 用户注册
 */
export async function registerUser(data: RegisterData): Promise<User> {
  try {
    const { postMemberAuthSignUp } = await import('@/servers/api/huiyuanzhuceyudenglu');
    const response = await postMemberAuthSignUp(data);
    return processAuthResponse(response, { username: data.username });
  } catch (error) {
    handleAuthError(error, '注册失败');
  }
}

/**
 * 验证码登录
 */
export async function loginWithCaptcha(data: CaptchaLoginData): Promise<User> {
  try {
    const { postMemberAuthCaptchaSignIn } = await import('@/servers/api/huiyuanzhuceyudenglu');
    const response = await postMemberAuthCaptchaSignIn(data);
    return processAuthResponse(response, { email: data.email });
  } catch (error) {
    handleAuthError(error, '验证码登录失败');
  }
}

/**
 * Google登录
 */
export async function loginWithGoogle(data: GoogleLoginData): Promise<User> {
  try {
    const { postMemberAuthGoogleSignIn } = await import('@/servers/api/huiyuanzhuceyudenglu');
    const response = await postMemberAuthGoogleSignIn(data);
    return processAuthResponse(response);
  } catch (error) {
    handleAuthError(error, 'Google登录失败');
  }
}

/**
 * 发送验证码
 */
export async function sendCaptcha(email: string): Promise<void> {
  try {
    const { postMemberAuthSendCaptcha } = await import('@/servers/api/huiyuanzhuceyudenglu');
    await postMemberAuthSendCaptcha({ email });
  } catch (error) {
    handleAuthError(error, '发送验证码失败');
  }
}

/**
 * 获取用户信息
 */
export async function fetchUserProfile(): Promise<User> {
  try {
    const { getMemberAuthProfile } = await import('@/servers/api/huiyuanxinxi');
    const response = await getMemberAuthProfile();

    if (response?.data && response.data.id) {
      return createUserData(response.data);
    } else if (response?.code === '40000') {
      TokenManager.clearTokens();
      toast.error(response?.msg);
      throw new Error(response?.msg);
    } else {
      throw new Error('用户信息响应格式错误');
    }
  } catch (error: any) {
    // 如果是认证相关错误，清除token
    if (error?.response?.status === 401 ||
        error?.status === 401 ||
        error?.data?.code === 'UNAUTHORIZED') {
      console.warn('Authentication expired, clearing auth state');
      TokenManager.clearTokens();
      toast.error('登录已过期，请重新登录');
    }
    throw error;
  }
}

/**
 * 刷新Token
 */
export async function refreshAuthToken(): Promise<User | null> {
  try {
    const { postMemberAuthRenewToken } = await import('@/servers/api/huiyuanzhuceyudenglu');
    const response = await postMemberAuthRenewToken();

    if (response?.data?.token?.accessToken) {
      // 更新token
      TokenManager.setToken(response.data.token.accessToken);

      // 如果有用户信息，返回用户数据
      if (response.data.profile) {
        return createUserData(response.data.profile);
      }
      
      return null;
    } else {
      throw new Error('刷新Token响应格式错误');
    }
  } catch (error) {
    // 如果刷新失败，清除认证状态
    TokenManager.clearTokens();
    handleAuthError(error, 'Token刷新失败');
  }
}

#!/bin/bash

# 简化版 TutoroAI Frontend Docker 构建脚本
echo "🚀 开始构建 TutoroAI Frontend Docker 镜像..."

# 设置变量
IMAGE_NAME="tutoro-ai-front"
IMAGE_TAG="latest"
TAR_FILE="tutoro-ai-front.tar"

# 构建 Docker 镜像（使用Dockerfile中的默认值）
echo "🔨 构建 Docker 镜像..."
docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .

# 检查构建是否成功
if [ $? -eq 0 ]; then
  echo "✅ Docker 镜像构建成功！"
  
  # 显示镜像信息
  echo "📋 镜像信息："
  docker images ${IMAGE_NAME}:${IMAGE_TAG}
  
  # 保存镜像为 tar 文件
  echo "💾 保存镜像为 tar 文件..."
  docker save -o ${TAR_FILE} ${IMAGE_NAME}:${IMAGE_TAG}
  
  if [ $? -eq 0 ]; then
    echo "✅ 镜像已保存为: ${TAR_FILE}"
    echo "📏 文件大小:"
    ls -lh ${TAR_FILE}
    echo ""
    echo "🎉 构建完成！"
    echo "📦 Docker 镜像: ${IMAGE_NAME}:${IMAGE_TAG}"
    echo "📁 镜像文件: ${TAR_FILE}"
  else
    echo "❌ 保存镜像文件失败"
    exit 1
  fi
else
  echo "❌ Docker 镜像构建失败"
  exit 1
fi 
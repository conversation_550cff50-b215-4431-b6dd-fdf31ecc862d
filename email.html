<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Verify your email to sign in to TutoroAI</title>
    <style>
      /* Reset and base styles */
      * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
      }

      body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #333333;
          background-color: #f8f9fa;
          padding: 20px;
      }

      .email-container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border: 1px solid #e1e5e9;
          border-radius: 12px;
          padding: 40px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .logo-container {
          text-align: center;
          margin-bottom: 32px;
      }

      .logo {
          width: 137.75px;
          height: 106px;
          /* background: linear-gradient(135deg, #1f2937 0%, #374151 100%); */
          border-radius: 12px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: 20px;
          /* box-shadow: 0 4px 14px 0 rgba(31, 41, 55, 0.25); */
      }
      .logo img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .brand-name {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
          margin-top: 8px;
      }

      .title {
          font-size: 24px;
          font-weight: 600;
          text-align: center;
          margin-bottom: 32px;
          color: #1a1a1a;
      }

      .greeting {
          font-size: 16px;
          margin-bottom: 20px;
          color: #333333;
      }

      .message {
          font-size: 16px;
          margin-bottom: 24px;
          color: #333333;
          line-height: 1.6;
      }

      .instructions {
          font-size: 16px;
          margin-bottom: 32px;
          color: #333333;
          line-height: 1.6;
      }

      .verification-code {
          background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
          border: 2px solid #d1d5db;
          border-radius: 12px;
          padding: 24px;
          text-align: center;
          margin: 32px 0;
      }

      .code-label {
          font-size: 14px;
          color: #64748b;
          margin-bottom: 12px;
          font-weight: 500;
      }

      .code {
          font-size: 36px;
          font-weight: 700;
          font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Courier New', monospace;
          letter-spacing: 6px;
          color: #1f2937;
          text-shadow: 0 2px 4px rgba(31, 41, 55, 0.1);
      }

      .code-note {
          font-size: 12px;
          color: #64748b;
          margin-top: 12px;
      }

      .verification-link {
          background-color: #f9fafb;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          padding: 16px;
          margin: 24px 0;
          word-break: break-all;
      }

      .verification-link a {
          color: #1f2937;
          text-decoration: none;
          font-size: 14px;
      }

      .verification-link a:hover {
          text-decoration: underline;
      }

      .cta-button {
          display: inline-block;
          background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
          color: white;
          padding: 14px 28px;
          border-radius: 8px;
          text-decoration: none;
          font-weight: 500;
          margin: 24px 0;
          transition: all 0.2s;
          box-shadow: 0 4px 14px 0 rgba(31, 41, 55, 0.25);
      }

      .cta-button:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 20px 0 rgba(31, 41, 55, 0.35);
      }

      .security-notice {
          margin-top: 40px;
          padding-top: 24px;
          border-top: 1px solid #e1e5e9;
          font-size: 14px;
          color: #666666;
          line-height: 1.6;
          background-color: #fefefe;
          padding: 20px;
          border-radius: 8px;
          border: 1px solid #f1f5f9;
      }

      .security-notice h3 {
          color: #374151;
          font-size: 16px;
          margin-bottom: 12px;
          font-weight: 600;
      }

      .security-notice a {
          color: #1f2937;
          text-decoration: none;
      }

      .security-notice a:hover {
          text-decoration: underline;
      }

      .footer {
          text-align: center;
          margin-top: 32px;
          padding-top: 24px;
          border-top: 1px solid #e1e5e9;
          color: #64748b;
          font-size: 14px;
      }

      /* Responsive design */
      @media (max-width: 480px) {
          .email-container {
              padding: 24px;
              margin: 10px;
          }

          .title {
              font-size: 20px;
          }

          .code {
              font-size: 28px;
              letter-spacing: 4px;
          }

          .verification-link {
              padding: 12px;
          }

          .verification-link a {
              font-size: 12px;
          }
      }

      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
          body {
              background-color: #0f172a;
              color: #e2e8f0;
          }

          .email-container {
              background-color: #1e293b;
              border-color: #334155;
          }

          .title, .greeting, .message, .instructions {
              color: #e2e8f0;
          }

          .verification-code {
              background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
              border-color: #475569;
          }

                     .code {
               color: #e5e7eb;
           }

          .security-notice {
              background-color: #1e293b;
              border-color: #334155;
          }

          .security-notice h3 {
              color: #e2e8f0;
          }
      }

      .sr-only {
          position: absolute;
          width: 1px;
          height: 1px;
          padding: 0;
          margin: -1px;
          overflow: hidden;
          clip: rect(0, 0, 0, 0);
          white-space: nowrap;
          border: 0;
      }
    </style>
  </head>
  <body>
    <div class="email-container" role="main">
      <header class="logo-container">
        <div class="logo" aria-label="TutoroAI Logo">
          <img src="https://tutoroai.com/images/logo2.png" alt="TutoroAI" />
        </div>
        <!-- <div class="brand-name">TutoroAI</div> -->
      </header>

      <h1 class="title">Verify your email to sign in to TutoroAI</h1>

      <div class="greeting">Hello ,</div>

      <div class="message">
        Welcome to TutoroAI! We're excited to have you join our AI-powered learning platform. To complete your sign-in
        process, please verify your email address.
      </div>

      <div class="instructions">
        Enter the 6-digit verification code below in the sign-in window to access your TutoroAI account:
      </div>

      <div class="verification-code" role="region" aria-labelledby="code-label">
        <div class="code-label" id="code-label">Your verification code:</div>
        <div class="code" aria-label="Verification code: {{code}}">{{code}}</div>
        <div class="code-note">This code will expire in 5 minutes</div>
      </div>

      <div class="security-notice">
        <h3>🔒 Security Notice</h3>
        <p>
          <strong>Keep your code secure:</strong>
          Never share this verification code with anyone. TutoroAI support will never ask for your verification code via
          email, phone, or chat.
        </p>
        <br />
        <p>
          <strong>Didn't request this?</strong>
          If you didn't attempt to sign in to TutoroAI but received this email, you can safely ignore it. Your account
          remains secure.
        </p>
        <br />
        <p>
          <strong>Need help?</strong>
          If you're having trouble signing in or have questions about your account, please contact our support team at
          <a href="mailto:<EMAIL>"><EMAIL></a>
          .
        </p>
      </div>
    </div>
  </body>
</html>

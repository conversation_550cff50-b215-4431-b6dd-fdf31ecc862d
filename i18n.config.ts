/*
 * @Date: 2025-06-02 19:00:23
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-16 18:38:03
 * @FilePath: /tutoro-ai-front/i18n.config.ts
 */
export const defaultLocale = 'en' as const;
export const locales = ['en', 'zh', 'zh-HK'] as const;

export type Locale = (typeof locales)[number];

export const localeNames = {
  en: 'English',
  zh: '简体中文',
  'zh-HK': '繁體中文'
} as const;

export const getLocaleFlag = (locale: Locale): string => {
  switch (locale) {
    case 'zh':
      return '🇨🇳';
    case 'zh-HK':
      return '🇭🇰';
    case 'en':
      return '🇺🇸';
    default:
      return '🇺🇸';
  }
}; 
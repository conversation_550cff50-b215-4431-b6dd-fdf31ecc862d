/*
 * @Date: 2025-06-11 17:50:51
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-11 20:31:55
 * @FilePath: /tutoro-ai-front/public/serviceWorker.js
 */
// Basic Service Worker for Tutoro AI
const CACHE_NAME = 'tutoro-ai-v1';
const urlsToCache = ['/manifest.json'];

// Install event
self.addEventListener('install', (event) => {
  event.waitUntil(caches.open(CACHE_NAME).then((cache) => cache.addAll(urlsToCache)));
  // Force the waiting service worker to become the active service worker
  self.skipWaiting();
});

// Fetch event
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // Only handle GET requests for caching
  if (event.request.method !== 'GET') {
    return;
  }
  
  // Skip service worker for navigation requests to avoid redirect issues
  if (event.request.mode === 'navigate') {
    return;
  }
  
  // Skip service worker for API routes and middleware-handled routes
  if (
    url.pathname.startsWith('/api/') ||
    url.pathname.startsWith('/_next/') ||
    url.pathname.includes('.') ||
    url.pathname === '/' ||
    url.pathname.startsWith('/servers/') ||
    url.host !== location.host // Skip external requests
  ) {
    return;
  }

  event.respondWith(
    caches.match(event.request).then((response) => {
      if (response) {
        return response;
      }
      
      // For network requests, properly handle redirects
      return fetch(event.request.clone(), {
        redirect: 'follow' // Explicitly allow following redirects
      }).then((fetchResponse) => {
        // Don't cache redirected responses
        if (fetchResponse.type === 'opaqueredirect' || fetchResponse.redirected) {
          return fetchResponse;
        }
        
        // Cache successful responses
        if (fetchResponse.status === 200) {
          const responseToCache = fetchResponse.clone();
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseToCache);
          });
        }
        
        return fetchResponse;
      }).catch(() => {
        // Return cached version if network fails
        return caches.match(event.request);
      });
    })
  );
});

// Activate event
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  // Take control of all open clients immediately
  self.clients.claim();
});

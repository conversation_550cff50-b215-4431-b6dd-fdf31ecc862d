// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 获取可用大模型列表 GET /infra/model/list-model */
export async function getInfraModelListModel(options?: { [key: string]: any }) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      name?: string;
      description?: string;
      modelId?: string;
      cached?: boolean;
      baseURL?: string;
      apiKey?: string;
      tag?: string;
    }[];
  }>('/infra/model/list-model', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取可用大模型列表 GET /infra/model/list-model */
export async function getInfraModelListModel2(options?: { [key: string]: any }) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      name?: string;
      description?: string;
      modelId?: string;
      cached?: boolean;
      baseURL?: string;
      apiKey?: string;
      tag?: string;
    }[];
  }>('/infra/model/list-model', {
    method: 'GET',
    ...(options || {}),
  });
}

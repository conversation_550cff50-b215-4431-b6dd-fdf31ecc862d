// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 获取当前会员信息 GET /member/auth/profile */
export async function getMemberAuthProfile(options?: { [key: string]: any }) {
  return request<{
    code: string;
    msg: string;
    data?: {
      gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      username?: string;
      phone?: string;
      email?: string;
      googleId?: string;
      nickname?: string;
      avatar?: string;
      lastAccessTime?: string;
      memberFlag?: number;
      freeze?: boolean;
      freezeReason?: string;
      recommender?: string;
      stripeCustomerId?: string;
      subscription: {
        product?: 'PRO' | 'UNKNOWN';
        price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
        status?: 'ACTIVE' | 'CANCELED';
      };
      stripeSubscription: {
        product?: string;
        price?: string;
        status?: string;
        cancelAt?: number;
        canceledAt?: number;
      };
    };
  }>('/member/auth/profile', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取当前会员信息 GET /member/auth/profile */
export async function getMemberAuthProfile2(options?: { [key: string]: any }) {
  return request<{
    code: string;
    msg: string;
    data?: {
      gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      username?: string;
      phone?: string;
      email?: string;
      googleId?: string;
      nickname?: string;
      avatar?: string;
      lastAccessTime?: string;
      memberFlag?: number;
      freeze?: boolean;
      freezeReason?: string;
      recommender?: string;
      stripeCustomerId?: string;
      subscription: {
        product?: 'PRO' | 'UNKNOWN';
        price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
        status?: 'ACTIVE' | 'CANCELED';
      };
      stripeSubscription: {
        product?: string;
        price?: string;
        status?: string;
        cancelAt?: number;
        canceledAt?: number;
      };
    };
  }>('/member/auth/profile', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取指定会员信息 GET /member/auth/profile/${param0} */
export async function getMemberAuthProfileId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMemberAuthProfileIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      username?: string;
      phone?: string;
      email?: string;
      googleId?: string;
      nickname?: string;
      avatar?: string;
      lastAccessTime?: string;
      memberFlag?: number;
      freeze?: boolean;
      freezeReason?: string;
      recommender?: string;
      stripeCustomerId?: string;
      subscription: {
        product?: 'PRO' | 'UNKNOWN';
        price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
        status?: 'ACTIVE' | 'CANCELED';
      };
      stripeSubscription: {
        product?: string;
        price?: string;
        status?: string;
        cancelAt?: number;
        canceledAt?: number;
      };
    };
  }>(`/member/auth/profile/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定会员信息 GET /member/auth/profile/${param0} */
export async function getMemberAuthProfileId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMemberAuthProfileIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      username?: string;
      phone?: string;
      email?: string;
      googleId?: string;
      nickname?: string;
      avatar?: string;
      lastAccessTime?: string;
      memberFlag?: number;
      freeze?: boolean;
      freezeReason?: string;
      recommender?: string;
      stripeCustomerId?: string;
      subscription: {
        product?: 'PRO' | 'UNKNOWN';
        price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
        status?: 'ACTIVE' | 'CANCELED';
      };
      stripeSubscription: {
        product?: string;
        price?: string;
        status?: string;
        cancelAt?: number;
        canceledAt?: number;
      };
    };
  }>(`/member/auth/profile/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新当前会员密码 POST /member/auth/update-password */
export async function postMemberAuthUpdatePassword(
  body: {
    /** 旧密码 */
    oldPassword?: string;
    /** 密码 */
    password: string;
    /** 确认密码 */
    confirmPassword: string;
  },
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string }>('/member/auth/update-password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新当前会员密码 POST /member/auth/update-password */
export async function postMemberAuthUpdatePassword2(
  body: {
    /** 旧密码 */
    oldPassword?: string;
    /** 密码 */
    password: string;
    /** 确认密码 */
    confirmPassword: string;
  },
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string }>('/member/auth/update-password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新当前会员信息 POST /member/auth/update-profile */
export async function postMemberAuthUpdateProfile(
  body: {
    /** 昵称 */
    nickname: string;
    /** 头像URL地址 */
    avatar: string;
    /** 性别枚举：MALE 男性；FEMALE 女性；UNKNOWN 未知； */
    gender: 'MALE' | 'FEMALE' | 'UNKNOWN';
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      username?: string;
      phone?: string;
      email?: string;
      googleId?: string;
      nickname?: string;
      avatar?: string;
      lastAccessTime?: string;
      memberFlag?: number;
      freeze?: boolean;
      freezeReason?: string;
      recommender?: string;
      stripeCustomerId?: string;
      subscription: {
        product?: 'PRO' | 'UNKNOWN';
        price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
        status?: 'ACTIVE' | 'CANCELED';
      };
      stripeSubscription: {
        product?: string;
        price?: string;
        status?: string;
        cancelAt?: number;
        canceledAt?: number;
      };
    };
  }>('/member/auth/update-profile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新当前会员信息 POST /member/auth/update-profile */
export async function postMemberAuthUpdateProfile2(
  body: {
    /** 昵称 */
    nickname: string;
    /** 头像URL地址 */
    avatar: string;
    /** 性别枚举：MALE 男性；FEMALE 女性；UNKNOWN 未知； */
    gender: 'MALE' | 'FEMALE' | 'UNKNOWN';
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      username?: string;
      phone?: string;
      email?: string;
      googleId?: string;
      nickname?: string;
      avatar?: string;
      lastAccessTime?: string;
      memberFlag?: number;
      freeze?: boolean;
      freezeReason?: string;
      recommender?: string;
      stripeCustomerId?: string;
      subscription: {
        product?: 'PRO' | 'UNKNOWN';
        price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
        status?: 'ACTIVE' | 'CANCELED';
      };
      stripeSubscription: {
        product?: string;
        price?: string;
        status?: string;
        cancelAt?: number;
        canceledAt?: number;
      };
    };
  }>('/member/auth/update-profile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 创建空间 POST /space/create-space */
export async function postSpaceCreateSpace(
  body: {
    title?: string;
    description?: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      memberId?: number;
      index?: number;
      title?: string;
      description?: string;
      sessionId?: string;
    };
  }>('/space/create-space', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建空间 POST /space/create-space */
export async function postSpaceCreateSpace2(
  body: {
    title?: string;
    description?: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      memberId?: number;
      index?: number;
      title?: string;
      description?: string;
      sessionId?: string;
    };
  }>('/space/create-space', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除空间 POST /space/delete-space/${param0} */
export async function postSpaceDeleteSpaceId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postSpaceDeleteSpaceIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      memberId?: number;
      index?: number;
      title?: string;
      description?: string;
      sessionId?: string;
    };
  }>(`/space/delete-space/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除空间 POST /space/delete-space/${param0} */
export async function postSpaceDeleteSpaceId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postSpaceDeleteSpaceIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      memberId?: number;
      index?: number;
      title?: string;
      description?: string;
      sessionId?: string;
    };
  }>(`/space/delete-space/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定空间详情 GET /space/find-space/${param0} */
export async function getSpaceFindSpaceId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpaceFindSpaceIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      memberId?: number;
      index?: number;
      title?: string;
      description?: string;
      sessionId?: string;
    };
  }>(`/space/find-space/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定空间详情 GET /space/find-space/${param0} */
export async function getSpaceFindSpaceId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpaceFindSpaceIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      memberId?: number;
      index?: number;
      title?: string;
      description?: string;
      sessionId?: string;
    };
  }>(`/space/find-space/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 新建会话 GET /space/new-session/${param0} */
export async function getSpaceNewSessionId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpaceNewSessionIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/space/new-session/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 新建会话 GET /space/new-session/${param0} */
export async function getSpaceNewSessionId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpaceNewSessionIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/space/new-session/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 分页查询空间列表 GET /space/page-space */
export async function getSpacePageSpace(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpacePageSpaceParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      page?: number;
      pageSize?: number;
      pageCount?: number;
      totalCount?: number;
      record?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      }[];
    };
  }>('/space/page-space', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // pageSize has a default value: 30
      pageSize: '30',
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询空间列表 GET /space/page-space */
export async function getSpacePageSpace2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpacePageSpaceParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      page?: number;
      pageSize?: number;
      pageCount?: number;
      totalCount?: number;
      record?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      }[];
    };
  }>('/space/page-space', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // pageSize has a default value: 30
      pageSize: '30',
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新空间 POST /space/update-space/${param0} */
export async function postSpaceUpdateSpaceId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postSpaceUpdateSpaceIdParams,
  body: {
    title?: string;
    description?: string;
  },
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      memberId?: number;
      index?: number;
      title?: string;
      description?: string;
      sessionId?: string;
    };
  }>(`/space/update-space/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新空间 POST /space/update-space/${param0} */
export async function postSpaceUpdateSpaceId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postSpaceUpdateSpaceIdParams,
  body: {
    title?: string;
    description?: string;
  },
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      memberId?: number;
      index?: number;
      title?: string;
      description?: string;
      sessionId?: string;
    };
  }>(`/space/update-space/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

declare namespace API {
  type getContentFindContentIdParams = {
    /** 内容ID */
    id: number;
  };

  type getContentNewSessionIdParams = {
    /** 内容ID */
    id: number;
  };

  type getContentPageContentParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页记录数，默认30 */
    pageSize?: number;
    spaceId?: number;
  };

  type getContentPageRecentContentParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页记录数，默认30 */
    pageSize?: number;
    spaceId?: number;
    /** 检索词 */
    search?: string;
  };

  type getLlmQuizAnswerListAnswerParams = {
    /** 测验组ID */
    quizGroupId: number;
  };

  type getLlmQuizGroupListGroupContentIdParams = {
    /** 内容ID */
    contentId: number;
  };

  type getLlmQuizQuestionListQuestionGroupIdParams = {
    /** 分组ID */
    groupId: number;
  };

  type getMemberAuthProfileIdParams = {
    /** 会员ID */
    id: number;
  };

  type getNoteFindNoteByContentContentIdParams = {
    /** 内容ID */
    contentId: number;
  };

  type getNoteFindNoteContentIdParams = {
    /** 内容ID */
    contentId: number;
  };

  type getNoteFindNoteIdParams = {
    /** 笔记ID */
    id: number;
  };

  type getNoteListTagParams = {
    /** 标签名称 */
    name?: string;
  };

  type getNotePageNoteParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页记录数，默认30 */
    pageSize?: number;
    /** 空间ID */
    spaceId?: number;
    /** 内容ID */
    contentId?: number;
    /** 标签ID（多个用逗号分隔） */
    tagId?: string;
    /** 笔记标题 */
    title?: string;
  };

  type getSpaceFindSpaceIdParams = {
    id: number;
  };

  type getSpaceNewSessionIdParams = {
    /** 空间ID */
    id: number;
  };

  type getSpacePageSpaceParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页记录数，默认30 */
    pageSize?: number;
    title?: string;
  };

  type postContentDeleteContentIdParams = {
    /** 内容ID */
    id: number;
  };

  type postContentUpdateContentIdParams = {
    /** 内容ID */
    id: number;
  };

  type postContentUpdateQuizSettingsIdParams = {
    /** 内容ID */
    id: number;
  };

  type postFeedbackFindFeedbackIdParams = {
    id: number;
  };

  type postLlmChapterGenerateChapterContentIdParams = {
    /** 内容ID */
    contentId: number;
    /** 模型id */
    modelId?: number;
    /** 是否重置 */
    reset?: 'true' | 'false';
  };

  type postLlmChatContentIdParams = {
    contentId: number;
  };

  type postLlmFreshcardContentIdParams = {
    /** 内容ID */
    contentId: number;
    /** 模型id */
    modelId?: number;
    /** 抽认卡数量 */
    quantity?: number;
    /** 是否重置 */
    reset?: 'true' | 'false';
  };

  type postLlmQuizAnswerCreateAnswerParams = {
    /** 模型id */
    modelId?: number;
  };

  type postLlmQuizGroupContentIdParams = {
    /** 内容ID */
    contentId: number;
    /** 模型id */
    modelId?: number;
    /** 是否重置 */
    reset?: 'true' | 'false';
  };

  type postLlmQuizGroupResetAnswerGroupIdParams = {
    /** 分组ID */
    groupId: number;
  };

  type postLlmQuizQuestionDeleteQuestionIdParams = {
    /** 问题ID */
    id: number;
  };

  type postLlmQuizQuestionGroupIdParams = {
    /** 分组ID */
    groupId: number;
    /** 模型id */
    modelId?: number;
    /** 是否重置 */
    reset?: 'true' | 'false';
  };

  type postLlmQuizQuestionResetAnswerIdParams = {
    /** 问题ID */
    id: number;
  };

  type postLlmSummaryGenerateSummaryContentIdParams = {
    /** 内容ID */
    contentId: number;
    /** 模型id */
    modelId?: number;
    /** 是否重置 */
    reset?: 'true' | 'false';
  };

  type postNoteDeleteNoteIdParams = {
    id: number;
  };

  type postNoteUpdateNoteIdParams = {
    id: number;
  };

  type postSpaceDeleteSpaceIdParams = {
    id: number;
  };

  type postSpaceUpdateSpaceIdParams = {
    id: number;
  };
}

// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 创建问题的回答 POST /llm/quiz/answer/create-answer */
export async function postLlmQuizAnswerCreateAnswer(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizAnswerCreateAnswerParams,
  body: {
    /** 问题ID */
    quizQuestionId: number;
    /** 答案 */
    answer: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      quizType?: 'multiple_choice' | 'free_responses';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      quizGroupId?: number;
      quizQuestionId?: number;
      memberId?: number;
      correct?: boolean;
      answer?: string;
      score?: string;
      comment?: string;
    };
  }>('/llm/quiz/answer/create-answer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      // modelId has a default value: 1
      modelId: '1',
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建问题的回答 POST /llm/quiz/answer/create-answer */
export async function postLlmQuizAnswerCreateAnswer2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizAnswerCreateAnswerParams,
  body: {
    /** 问题ID */
    quizQuestionId: number;
    /** 答案 */
    answer: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      quizType?: 'multiple_choice' | 'free_responses';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      quizGroupId?: number;
      quizQuestionId?: number;
      memberId?: number;
      correct?: boolean;
      answer?: string;
      score?: string;
      comment?: string;
    };
  }>('/llm/quiz/answer/create-answer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      // modelId has a default value: 1
      modelId: '1',
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取分组下问题的回答列表 GET /llm/quiz/answer/list-answer */
export async function getLlmQuizAnswerListAnswer(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getLlmQuizAnswerListAnswerParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      quizType?: 'multiple_choice' | 'free_responses';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      quizGroupId?: number;
      quizQuestionId?: number;
      memberId?: number;
      correct?: boolean;
      answer?: string;
      score?: string;
      comment?: string;
    }[];
  }>('/llm/quiz/answer/list-answer', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取分组下问题的回答列表 GET /llm/quiz/answer/list-answer */
export async function getLlmQuizAnswerListAnswer2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getLlmQuizAnswerListAnswerParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      quizType?: 'multiple_choice' | 'free_responses';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      quizGroupId?: number;
      quizQuestionId?: number;
      memberId?: number;
      correct?: boolean;
      answer?: string;
      score?: string;
      comment?: string;
    }[];
  }>('/llm/quiz/answer/list-answer', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 生成测验分组 POST /llm/quiz/group/${param0} */
export async function postLlmQuizGroupContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizGroupContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      memberId?: number;
      title?: string;
      progress?: string;
    }[];
  }>(`/llm/quiz/group/${param0}`, {
    method: 'POST',
    params: {
      // modelId has a default value: 1
      modelId: '1',
      // reset has a default value: false
      reset: 'false',
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 生成测验分组 POST /llm/quiz/group/${param0} */
export async function postLlmQuizGroupContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizGroupContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      memberId?: number;
      title?: string;
      progress?: string;
    }[];
  }>(`/llm/quiz/group/${param0}`, {
    method: 'POST',
    params: {
      // modelId has a default value: 1
      modelId: '1',
      // reset has a default value: false
      reset: 'false',
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取测验分组列表 GET /llm/quiz/group/list-group/${param0} */
export async function getLlmQuizGroupListGroupContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getLlmQuizGroupListGroupContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      memberId?: number;
      title?: string;
      progress?: string;
    }[];
  }>(`/llm/quiz/group/list-group/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取测验分组列表 GET /llm/quiz/group/list-group/${param0} */
export async function getLlmQuizGroupListGroupContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getLlmQuizGroupListGroupContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      memberId?: number;
      title?: string;
      progress?: string;
    }[];
  }>(`/llm/quiz/group/list-group/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 重置测验分组回答 POST /llm/quiz/group/reset-answer/${param0} */
export async function postLlmQuizGroupResetAnswerGroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizGroupResetAnswerGroupIdParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/llm/quiz/group/reset-answer/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 重置测验分组回答 POST /llm/quiz/group/reset-answer/${param0} */
export async function postLlmQuizGroupResetAnswerGroupId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizGroupResetAnswerGroupIdParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/llm/quiz/group/reset-answer/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 生成测验题目 POST /llm/quiz/question/${param0} */
export async function postLlmQuizQuestionGroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizQuestionGroupIdParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      quizType?: 'multiple_choice' | 'free_responses';
      quizDifficulty?: 'easy' | 'medium' | 'hard';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      memberId?: number;
      quizGroupId?: number;
      answer?: string;
      explanation?: string;
      question?: string;
      options?: { index?: number; option?: string; correct?: boolean }[];
    }[];
  }>(`/llm/quiz/question/${param0}`, {
    method: 'POST',
    params: {
      // modelId has a default value: 1
      modelId: '1',
      // reset has a default value: false
      reset: 'false',
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 生成测验题目 POST /llm/quiz/question/${param0} */
export async function postLlmQuizQuestionGroupId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizQuestionGroupIdParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      quizType?: 'multiple_choice' | 'free_responses';
      quizDifficulty?: 'easy' | 'medium' | 'hard';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      memberId?: number;
      quizGroupId?: number;
      answer?: string;
      explanation?: string;
      question?: string;
      options?: { index?: number; option?: string; correct?: boolean }[];
    }[];
  }>(`/llm/quiz/question/${param0}`, {
    method: 'POST',
    params: {
      // modelId has a default value: 1
      modelId: '1',
      // reset has a default value: false
      reset: 'false',
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除测验题目 POST /llm/quiz/question/delete-question/${param0} */
export async function postLlmQuizQuestionDeleteQuestionId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizQuestionDeleteQuestionIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/llm/quiz/question/delete-question/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除测验题目 POST /llm/quiz/question/delete-question/${param0} */
export async function postLlmQuizQuestionDeleteQuestionId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizQuestionDeleteQuestionIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/llm/quiz/question/delete-question/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取测验题目列表 GET /llm/quiz/question/list-question/${param0} */
export async function getLlmQuizQuestionListQuestionGroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getLlmQuizQuestionListQuestionGroupIdParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      quizType?: 'multiple_choice' | 'free_responses';
      quizDifficulty?: 'easy' | 'medium' | 'hard';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      memberId?: number;
      quizGroupId?: number;
      answer?: string;
      explanation?: string;
      question?: string;
      options?: { index?: number; option?: string; correct?: boolean }[];
    }[];
  }>(`/llm/quiz/question/list-question/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取测验题目列表 GET /llm/quiz/question/list-question/${param0} */
export async function getLlmQuizQuestionListQuestionGroupId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getLlmQuizQuestionListQuestionGroupIdParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      quizType?: 'multiple_choice' | 'free_responses';
      quizDifficulty?: 'easy' | 'medium' | 'hard';
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      contentId?: number;
      memberId?: number;
      quizGroupId?: number;
      answer?: string;
      explanation?: string;
      question?: string;
      options?: { index?: number; option?: string; correct?: boolean }[];
    }[];
  }>(`/llm/quiz/question/list-question/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除测验题回答 POST /llm/quiz/question/reset-answer/${param0} */
export async function postLlmQuizQuestionResetAnswerId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizQuestionResetAnswerIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/llm/quiz/question/reset-answer/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除测验题回答 POST /llm/quiz/question/reset-answer/${param0} */
export async function postLlmQuizQuestionResetAnswerId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmQuizQuestionResetAnswerIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/llm/quiz/question/reset-answer/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

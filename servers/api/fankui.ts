// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 创建反馈 POST /feedback/create-feedback */
export async function postFeedbackCreateFeedback(
  body: {
    /** 姓名 */
    name: string;
    /** 邮箱 */
    email?: string;
    /** 反馈类别 */
    category: string;
    /** 留言 */
    message: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      name?: string;
      email?: string;
      category?: string;
      message?: string;
    };
  }>('/feedback/create-feedback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建反馈 POST /feedback/create-feedback */
export async function postFeedbackCreateFeedback2(
  body: {
    /** 姓名 */
    name: string;
    /** 邮箱 */
    email?: string;
    /** 反馈类别 */
    category: string;
    /** 留言 */
    message: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      name?: string;
      email?: string;
      category?: string;
      message?: string;
    };
  }>('/feedback/create-feedback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取反馈详情 POST /feedback/find-feedback/${param0} */
export async function postFeedbackFindFeedbackId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postFeedbackFindFeedbackIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      name?: string;
      email?: string;
      category?: string;
      message?: string;
    };
  }>(`/feedback/find-feedback/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取反馈详情 POST /feedback/find-feedback/${param0} */
export async function postFeedbackFindFeedbackId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postFeedbackFindFeedbackIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      name?: string;
      email?: string;
      category?: string;
      message?: string;
    };
  }>(`/feedback/find-feedback/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

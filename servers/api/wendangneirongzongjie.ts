// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 生成内容总结 POST /llm/summary/generate-summary/${param0} */
export async function postLlmSummaryGenerateSummaryContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmSummaryGenerateSummaryContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      content?: string;
    };
  }>(`/llm/summary/generate-summary/${param0}`, {
    method: 'POST',
    params: {
      // modelId has a default value: 1
      modelId: '1',
      // reset has a default value: false
      reset: 'false',
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 生成内容总结 POST /llm/summary/generate-summary/${param0} */
export async function postLlmSummaryGenerateSummaryContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmSummaryGenerateSummaryContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      content?: string;
    };
  }>(`/llm/summary/generate-summary/${param0}`, {
    method: 'POST',
    params: {
      // modelId has a default value: 1
      modelId: '1',
      // reset has a default value: false
      reset: 'false',
      ...queryParams,
    },
    ...(options || {}),
  });
}

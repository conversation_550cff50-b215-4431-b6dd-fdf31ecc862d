// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 文件上传 POST /infra/files/upload */
export async function postInfraFilesUpload(options?: { [key: string]: any }) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      originalFileName?: string;
      mimeType?: string;
      fileSize?: number;
      fileExt?: string;
      fileName?: string;
      fileUrl?: string;
    };
  }>('/infra/files/upload', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 文件上传 POST /infra/files/upload */
export async function postInfraFilesUpload2(options?: { [key: string]: any }) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      originalFileName?: string;
      mimeType?: string;
      fileSize?: number;
      fileExt?: string;
      fileName?: string;
      fileUrl?: string;
    };
  }>('/infra/files/upload', {
    method: 'POST',
    ...(options || {}),
  });
}

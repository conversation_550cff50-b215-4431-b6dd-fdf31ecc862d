// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 与大模型问答 POST /llm/chat/ */
export async function postLlmChat(
  body: {
    /** 空间ID */
    spaceId?: number;
    /** 内容ID */
    contentId?: number;
    /** 模型ID */
    modelId?: number;
    /** 问题 */
    question: string;
    uploadId?: number[];
  },
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/llm/chat/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 与大模型问答 POST /llm/chat/ */
export async function postLlmChat2(
  body: {
    /** 空间ID */
    spaceId?: number;
    /** 内容ID */
    contentId?: number;
    /** 模型ID */
    modelId?: number;
    /** 问题 */
    question: string;
    uploadId?: number[];
  },
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/llm/chat/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 与大模型问答 POST /llm/chat/${param0} */
export async function postLlmChatContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmChatContentIdParams,
  body: {
    /** 模型ID */
    modelId?: number;
    /** 问题 */
    question: string;
    uploadId?: number[];
  },
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/llm/chat/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 与大模型问答 POST /llm/chat/${param0} */
export async function postLlmChatContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmChatContentIdParams,
  body: {
    /** 模型ID */
    modelId?: number;
    /** 问题 */
    question: string;
    uploadId?: number[];
  },
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/llm/chat/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

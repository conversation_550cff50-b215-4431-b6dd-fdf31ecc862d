// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 创建内容 POST /content/create-content */
export async function postContentCreateContent(
  body: {
    spaceId?: number;
    uploadId: number;
    /** 标题 */
    title?: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>('/content/create-content', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建内容 POST /content/create-content */
export async function postContentCreateContent2(
  body: {
    spaceId?: number;
    uploadId: number;
    /** 标题 */
    title?: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>('/content/create-content', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除内容 POST /content/delete-content/${param0} */
export async function postContentDeleteContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postContentDeleteContentIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>(`/content/delete-content/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除内容 POST /content/delete-content/${param0} */
export async function postContentDeleteContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postContentDeleteContentIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>(`/content/delete-content/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定内容详情 GET /content/find-content/${param0} */
export async function getContentFindContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getContentFindContentIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>(`/content/find-content/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定内容详情 GET /content/find-content/${param0} */
export async function getContentFindContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getContentFindContentIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>(`/content/find-content/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 新建会话 GET /content/new-session/${param0} */
export async function getContentNewSessionId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getContentNewSessionIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/content/new-session/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 新建会话 GET /content/new-session/${param0} */
export async function getContentNewSessionId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getContentNewSessionIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{ code: string; msg: string }>(`/content/new-session/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 分页查询内容列表 GET /content/page-content */
export async function getContentPageContent(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getContentPageContentParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      page?: number;
      pageSize?: number;
      pageCount?: number;
      totalCount?: number;
      record?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
        space: {
          id?: number;
          createBy?: number;
          updateBy?: number;
          createAt?: string;
          updateAt?: string;
          memberId?: number;
          index?: number;
          title?: string;
          description?: string;
          sessionId?: string;
        };
      }[];
    };
  }>('/content/page-content', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // pageSize has a default value: 30
      pageSize: '30',
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询内容列表 GET /content/page-content */
export async function getContentPageContent2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getContentPageContentParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      page?: number;
      pageSize?: number;
      pageCount?: number;
      totalCount?: number;
      record?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
        space: {
          id?: number;
          createBy?: number;
          updateBy?: number;
          createAt?: string;
          updateAt?: string;
          memberId?: number;
          index?: number;
          title?: string;
          description?: string;
          sessionId?: string;
        };
      }[];
    };
  }>('/content/page-content', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // pageSize has a default value: 30
      pageSize: '30',
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询最近访问过的内容列表 GET /content/page-recent-content */
export async function getContentPageRecentContent(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getContentPageRecentContentParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      page?: number;
      pageSize?: number;
      pageCount?: number;
      totalCount?: number;
      record?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
        space: {
          id?: number;
          createBy?: number;
          updateBy?: number;
          createAt?: string;
          updateAt?: string;
          memberId?: number;
          index?: number;
          title?: string;
          description?: string;
          sessionId?: string;
        };
      }[];
    };
  }>('/content/page-recent-content', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // pageSize has a default value: 30
      pageSize: '30',

      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询最近访问过的内容列表 GET /content/page-recent-content */
export async function getContentPageRecentContent2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getContentPageRecentContentParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      page?: number;
      pageSize?: number;
      pageCount?: number;
      totalCount?: number;
      record?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
        space: {
          id?: number;
          createBy?: number;
          updateBy?: number;
          createAt?: string;
          updateAt?: string;
          memberId?: number;
          index?: number;
          title?: string;
          description?: string;
          sessionId?: string;
        };
      }[];
    };
  }>('/content/page-recent-content', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // pageSize has a default value: 30
      pageSize: '30',

      ...params,
    },
    ...(options || {}),
  });
}

/** 更新内容 POST /content/update-content/${param0} */
export async function postContentUpdateContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postContentUpdateContentIdParams,
  body: {
    /** 标题 */
    title: string;
  },
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>(`/content/update-content/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新内容 POST /content/update-content/${param0} */
export async function postContentUpdateContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postContentUpdateContentIdParams,
  body: {
    /** 标题 */
    title: string;
  },
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>(`/content/update-content/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新测验题型配置 POST /content/update-quiz-settings/${param0} */
export async function postContentUpdateQuizSettingsId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postContentUpdateQuizSettingsIdParams,
  body: {
    quizType: ('multiple_choice' | 'free_responses')[];
    QuizDifficulty: ('easy' | 'medium' | 'hard')[];
  },
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>(`/content/update-quiz-settings/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新测验题型配置 POST /content/update-quiz-settings/${param0} */
export async function postContentUpdateQuizSettingsId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postContentUpdateQuizSettingsIdParams,
  body: {
    quizType: ('multiple_choice' | 'free_responses')[];
    QuizDifficulty: ('easy' | 'medium' | 'hard')[];
  },
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      memberId?: number;
      uploadId?: number;
      fileUrl?: string;
      title?: string;
      accessTime?: string;
      vectorStored?: boolean;
      sessionId?: string;
      quizType?: { quizType?: 'multiple_choice' | 'free_responses' }[];
      quizDifficulty?: { quizDifficulty?: 'easy' | 'medium' | 'hard' }[];
      attachments?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        uploadId?: number;
      }[];
    };
  }>(`/content/update-quiz-settings/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

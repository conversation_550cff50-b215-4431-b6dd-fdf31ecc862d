// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 生成抽认卡 POST /llm/freshcard/${param0} */
export async function postLlmFreshcardContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmFreshcardContentIdParams,
  body: { answer?: string; explanation?: string; hint?: string; question?: string }[],
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/llm/freshcard/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      // modelId has a default value: 1
      modelId: '1',
      // quantity has a default value: 10
      quantity: '10',
      // reset has a default value: false
      reset: 'false',
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}

/** 生成抽认卡 POST /llm/freshcard/${param0} */
export async function postLlmFreshcardContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postLlmFreshcardContentIdParams,
  body: { answer?: string; explanation?: string; hint?: string; question?: string }[],
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/llm/freshcard/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      // modelId has a default value: 1
      modelId: '1',
      // quantity has a default value: 10
      quantity: '10',
      // reset has a default value: false
      reset: 'false',
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}

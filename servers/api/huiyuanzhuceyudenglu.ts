// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 验证码注册/登录 POST /member/auth/captcha/sign-in */
export async function postMemberAuthCaptchaSignIn(
  body: {
    /** 推荐人username */
    recommender?: string;
    /** 邮箱 */
    email: string;
    /** 验证码 */
    captcha: string;
    /** 会员信息 */
    profile?: { nickname?: string; avatar?: string; gender?: 'MALE' | 'FEMALE' | 'UNKNOWN' };
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/captcha/sign-in', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 验证码注册/登录 POST /member/auth/captcha/sign-in */
export async function postMemberAuthCaptchaSignIn2(
  body: {
    /** 推荐人username */
    recommender?: string;
    /** 邮箱 */
    email: string;
    /** 验证码 */
    captcha: string;
    /** 会员信息 */
    profile?: { nickname?: string; avatar?: string; gender?: 'MALE' | 'FEMALE' | 'UNKNOWN' };
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/captcha/sign-in', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 验证码注册/登录 POST /member/auth/google/sign-in */
export async function postMemberAuthGoogleSignIn(
  body: {
    /** 推荐人username */
    recommender?: string;
    /** 谷歌登录凭证 */
    idToken: string;
    /** 会员信息 */
    profile?: { nickname?: string; avatar?: string; gender?: 'MALE' | 'FEMALE' | 'UNKNOWN' };
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/google/sign-in', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 验证码注册/登录 POST /member/auth/google/sign-in */
export async function postMemberAuthGoogleSignIn2(
  body: {
    /** 推荐人username */
    recommender?: string;
    /** 谷歌登录凭证 */
    idToken: string;
    /** 会员信息 */
    profile?: { nickname?: string; avatar?: string; gender?: 'MALE' | 'FEMALE' | 'UNKNOWN' };
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/google/sign-in', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 刷新登录凭证 POST /member/auth/renew-token */
export async function postMemberAuthRenewToken(options?: { [key: string]: any }) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/renew-token', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 刷新登录凭证 POST /member/auth/renew-token */
export async function postMemberAuthRenewToken2(options?: { [key: string]: any }) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/renew-token', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 发送验证码 POST /member/auth/send-captcha */
export async function postMemberAuthSendCaptcha(
  body: {
    /** 邮箱 */
    email: string;
  },
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string; data?: { email?: string; expireAt?: number } }>(
    '/member/auth/send-captcha',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 发送验证码 POST /member/auth/send-captcha */
export async function postMemberAuthSendCaptcha2(
  body: {
    /** 邮箱 */
    email: string;
  },
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string; data?: { email?: string; expireAt?: number } }>(
    '/member/auth/send-captcha',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 账号密码登录 POST /member/auth/sign-in */
export async function postMemberAuthSignIn(
  body: {
    /** 用户名 */
    username: string;
    /** 密码 */
    password: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/sign-in', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 账号密码登录 POST /member/auth/sign-in */
export async function postMemberAuthSignIn2(
  body: {
    /** 用户名 */
    username: string;
    /** 密码 */
    password: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/sign-in', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 账号密码注册 POST /member/auth/sign-up */
export async function postMemberAuthSignUp(
  body: {
    /** 推荐人username */
    recommender?: string;
    /** 用户名 */
    username: string;
    /** 密码 */
    password: string;
    /** 确认密码 */
    confirmPassword: string;
    /** 会员信息 */
    profile?: { nickname?: string; avatar?: string; gender?: 'MALE' | 'FEMALE' | 'UNKNOWN' };
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/sign-up', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 账号密码注册 POST /member/auth/sign-up */
export async function postMemberAuthSignUp2(
  body: {
    /** 推荐人username */
    recommender?: string;
    /** 用户名 */
    username: string;
    /** 密码 */
    password: string;
    /** 确认密码 */
    confirmPassword: string;
    /** 会员信息 */
    profile?: { nickname?: string; avatar?: string; gender?: 'MALE' | 'FEMALE' | 'UNKNOWN' };
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      token: { accessToken?: string; expireAt?: string };
      profile: {
        gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        username?: string;
        phone?: string;
        email?: string;
        googleId?: string;
        nickname?: string;
        avatar?: string;
        lastAccessTime?: string;
        memberFlag?: number;
        freeze?: boolean;
        freezeReason?: string;
        recommender?: string;
        stripeCustomerId?: string;
        subscription: {
          product?: 'PRO' | 'UNKNOWN';
          price?: 'MONTHLY' | 'ANNUALLY' | 'UNKNOWN';
          status?: 'ACTIVE' | 'CANCELED';
        };
        stripeSubscription: {
          product?: string;
          price?: string;
          status?: string;
          cancelAt?: number;
          canceledAt?: number;
        };
      };
    };
  }>('/member/auth/sign-up', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 创建checkout POST /stripe/create-checkout-session */
export async function postStripeCreateCheckoutSession(
  body: {
    lookupKey: string;
  },
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string; data?: string }>('/stripe/create-checkout-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建checkout POST /stripe/create-checkout-session */
export async function postStripeCreateCheckoutSession2(
  body: {
    lookupKey: string;
  },
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string; data?: string }>('/stripe/create-checkout-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建portal POST /stripe/create-portal-session */
export async function postStripeCreatePortalSession(
  body: {
    sessionId?: string;
  },
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string; data?: string }>('/stripe/create-portal-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建portal POST /stripe/create-portal-session */
export async function postStripeCreatePortalSession2(
  body: {
    sessionId?: string;
  },
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string; data?: string }>('/stripe/create-portal-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** stripe webhook 回调处理端点 POST /stripe/webhook */
export async function postStripeWebhook(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/stripe/webhook', {
    method: 'POST',
    ...(options || {}),
  });
}

/** stripe webhook 回调处理端点 POST /stripe/webhook */
export async function postStripeWebhook2(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/stripe/webhook', {
    method: 'POST',
    ...(options || {}),
  });
}

/*
 * @Date: 2025-07-07 13:59:37
 * @LastEditors: yosan
 * @LastEditTime: 2025-07-07 14:45:17
 * @FilePath: /tutoro-ai-front/servers/api/wenben.ts
 */
// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 创建粘贴链接/文本 POST /content/paste/create-paste */
export async function postContentPasteCreatePaste(
  body: {
    /** 链接地址 */
    url?: string;
    /** 文本 */
    text?: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      originalFileName?: string;
      mimeType?: string;
      fileSize?: number;
      fileExt?: string;
      fileName?: string;
      fileUrl?: string;
    };
  }>('/content/paste/create-paste', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建粘贴链接/文本 POST /content/paste/create-paste */
export async function postContentPasteCreatePaste2(
  body: {
    /** 链接地址 */
    url?: string;
    /** 文本 */
    text?: string;
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      originalFileName?: string;
      mimeType?: string;
      fileSize?: number;
      fileExt?: string;
      fileName?: string;
      fileUrl?: string;
    };
  }>('/content/paste/create-paste', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

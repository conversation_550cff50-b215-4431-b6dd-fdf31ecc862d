// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 大模型解析音频内容 POST /llm/audio/describe */
export async function postLlmAudioDescribe(
  body: {
    /** 内容ID */
    contentId: number;
  },
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/llm/audio/describe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 大模型解析音频内容 POST /llm/audio/describe */
export async function postLlmAudioDescribe2(
  body: {
    /** 内容ID */
    contentId: number;
  },
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/llm/audio/describe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 大模型解析视频内容 POST /llm/video/describe */
export async function postLlmVideoDescribe(
  body: {
    /** 内容ID */
    contentId: number;
  },
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/llm/video/describe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 大模型解析视频内容 POST /llm/video/describe */
export async function postLlmVideoDescribe2(
  body: {
    /** 内容ID */
    contentId: number;
  },
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/llm/video/describe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

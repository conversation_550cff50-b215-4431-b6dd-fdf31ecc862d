// @ts-ignore
/* eslint-disable */
import request from '@/lib/api/client';

/** 创建笔记 POST /note/create-note */
export async function postNoteCreateNote(
  body: {
    /** 空间ID */
    spaceId?: number;
    /** 内容ID */
    contentId?: number;
    /** 笔记标题 */
    title?: string;
    /** 笔记内容 */
    context: string;
    tags?: string[];
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>('/note/create-note', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建笔记 POST /note/create-note */
export async function postNoteCreateNote2(
  body: {
    /** 空间ID */
    spaceId?: number;
    /** 内容ID */
    contentId?: number;
    /** 笔记标题 */
    title?: string;
    /** 笔记内容 */
    context: string;
    tags?: string[];
  },
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>('/note/create-note', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除笔记 POST /note/delete-note/${param0} */
export async function postNoteDeleteNoteId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postNoteDeleteNoteIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>(`/note/delete-note/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除笔记 POST /note/delete-note/${param0} */
export async function postNoteDeleteNoteId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postNoteDeleteNoteIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>(`/note/delete-note/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取内容最新笔记 GET /note/find-note-by-content/${param0} */
export async function getNoteFindNoteByContentContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNoteFindNoteByContentContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>(`/note/find-note-by-content/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取内容最新笔记 GET /note/find-note-by-content/${param0} */
export async function getNoteFindNoteByContentContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNoteFindNoteByContentContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>(`/note/find-note-by-content/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定空间详情 GET /note/find-note/${param0} */
export async function getNoteFindNoteContentId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNoteFindNoteContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      content?: string;
    };
  }>(`/note/find-note/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定空间详情 GET /note/find-note/${param0} */
export async function getNoteFindNoteContentId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNoteFindNoteContentIdParams,
  options?: { [key: string]: any },
) {
  const { contentId: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      content?: string;
    };
  }>(`/note/find-note/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定笔记详情 GET /note/find-note/${param0} */
export async function getNoteFindNoteId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNoteFindNoteIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>(`/note/find-note/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定笔记详情 GET /note/find-note/${param0} */
export async function getNoteFindNoteId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNoteFindNoteIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>(`/note/find-note/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取笔记标签列表 GET /note/list-tag */
export async function getNoteListTag(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNoteListTagParams,
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string; data?: { id?: number; name?: string }[] }>(
    '/note/list-tag',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 获取笔记标签列表 GET /note/list-tag */
export async function getNoteListTag2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNoteListTagParams,
  options?: { [key: string]: any },
) {
  return request<{ code: string; msg: string; data?: { id?: number; name?: string }[] }>(
    '/note/list-tag',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 分页查询笔记列表 GET /note/page-note */
export async function getNotePageNote(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNotePageNoteParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      page?: number;
      pageSize?: number;
      pageCount?: number;
      totalCount?: number;
      record?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        title?: string;
        space: {
          id?: number;
          createBy?: number;
          updateBy?: number;
          createAt?: string;
          updateAt?: string;
          memberId?: number;
          index?: number;
          title?: string;
          description?: string;
          sessionId?: string;
        };
        content: {
          id?: number;
          createBy?: number;
          updateBy?: number;
          createAt?: string;
          updateAt?: string;
          spaceId?: number;
          memberId?: number;
          uploadId?: number;
          filePath?: string;
          fileUrl?: string;
          title?: string;
          accessTime?: string;
          vectorStored?: boolean;
          sessionId?: string;
        };
        tags?: { tagId?: number; tagName?: string }[];
      }[];
    };
  }>('/note/page-note', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // pageSize has a default value: 30
      pageSize: '30',

      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询笔记列表 GET /note/page-note */
export async function getNotePageNote2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNotePageNoteParams,
  options?: { [key: string]: any },
) {
  return request<{
    code: string;
    msg: string;
    data?: {
      page?: number;
      pageSize?: number;
      pageCount?: number;
      totalCount?: number;
      record?: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        contentId?: number;
        memberId?: number;
        title?: string;
        space: {
          id?: number;
          createBy?: number;
          updateBy?: number;
          createAt?: string;
          updateAt?: string;
          memberId?: number;
          index?: number;
          title?: string;
          description?: string;
          sessionId?: string;
        };
        content: {
          id?: number;
          createBy?: number;
          updateBy?: number;
          createAt?: string;
          updateAt?: string;
          spaceId?: number;
          memberId?: number;
          uploadId?: number;
          filePath?: string;
          fileUrl?: string;
          title?: string;
          accessTime?: string;
          vectorStored?: boolean;
          sessionId?: string;
        };
        tags?: { tagId?: number; tagName?: string }[];
      }[];
    };
  }>('/note/page-note', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // pageSize has a default value: 30
      pageSize: '30',

      ...params,
    },
    ...(options || {}),
  });
}

/** 更新笔记 POST /note/update-note/${param0} */
export async function postNoteUpdateNoteId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postNoteUpdateNoteIdParams,
  body: {
    /** 空间ID */
    spaceId?: number;
    /** 内容ID */
    contentId?: number;
    /** 笔记标题 */
    title?: string;
    /** 笔记内容 */
    context: string;
    tags?: string[];
  },
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>(`/note/update-note/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新笔记 POST /note/update-note/${param0} */
export async function postNoteUpdateNoteId2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postNoteUpdateNoteIdParams,
  body: {
    /** 空间ID */
    spaceId?: number;
    /** 内容ID */
    contentId?: number;
    /** 笔记标题 */
    title?: string;
    /** 笔记内容 */
    context: string;
    tags?: string[];
  },
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<{
    code: string;
    msg: string;
    data?: {
      id?: number;
      createBy?: number;
      updateBy?: number;
      createAt?: string;
      updateAt?: string;
      spaceId?: number;
      contentId?: number;
      memberId?: number;
      title?: string;
      context?: string;
      space: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        memberId?: number;
        index?: number;
        title?: string;
        description?: string;
        sessionId?: string;
      };
      content: {
        id?: number;
        createBy?: number;
        updateBy?: number;
        createAt?: string;
        updateAt?: string;
        spaceId?: number;
        memberId?: number;
        uploadId?: number;
        filePath?: string;
        fileUrl?: string;
        title?: string;
        accessTime?: string;
        vectorStored?: boolean;
        sessionId?: string;
      };
      tags?: { tagId?: number; tagName?: string }[];
    };
  }>(`/note/update-note/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

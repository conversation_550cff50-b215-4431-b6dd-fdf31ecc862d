# 使用官方 Node.js 18 镜像
FROM node:18-alpine AS deps
# 检查最新版本请查看 https://github.com/nodejs/docker-node

# 安装依赖
WORKDIR /app
COPY package*.json ./
COPY yarn.lock ./
RUN yarn install --frozen-lockfile

# 构建阶段
FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 接收构建参数
ARG NEXT_PUBLIC_API_BASE_URL=https://api.tutoroai.com
ARG NODE_ENV=production
ARG NEXT_TELEMETRY_DISABLED=1

# 设置构建时环境变量
ENV NEXT_TELEMETRY_DISABLED=${NEXT_TELEMETRY_DISABLED}
ENV NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL}
ENV NODE_ENV=${NODE_ENV}

# 构建应用
RUN yarn build

# 运行阶段
FROM node:18-alpine AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# 创建用户组和用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 复制必要文件
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 更改权限
USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"] 
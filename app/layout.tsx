/*
 * @Date: 2025-06-03 22:11:12
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-11 17:49:25
 * @FilePath: /tutoro-ai-front/app/layout.tsx
 */
import type React from 'react';
import type { Metadata, Viewport } from 'next';
import appConfig from '@/config/app';

import './globals.css';

export const metadata: Metadata = {
  title: appConfig.name,
  description: appConfig.description,
  keywords: appConfig.keywords,
  // generator: 'v0.dev',
  applicationName: appConfig.name,
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: appConfig.name,
  },
  formatDetection: {
    telephone: false,
  },
  manifest: '/manifest.json',
  icons: {
    icon: '/favicon/favicon.ico',
    apple: '/favicon/android-chrome-192x192.png',
  },
  other: {
    'mobile-web-app-capable': 'yes',
  },
};

export const viewport: Viewport = {
  themeColor: '#000000',
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return children;
}

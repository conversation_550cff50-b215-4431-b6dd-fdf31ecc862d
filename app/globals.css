@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Responsive Font Size Variables */
    --font-scale: 1;
    --font-size-xs: calc(0.75rem * var(--font-scale));
    --font-size-sm: calc(0.875rem * var(--font-scale));
    --font-size-base: calc(1rem * var(--font-scale));
    --font-size-lg: calc(1.125rem * var(--font-scale));
    --font-size-xl: calc(1.25rem * var(--font-scale));
    --font-size-2xl: calc(1.5rem * var(--font-scale));
    --font-size-3xl: calc(1.875rem * var(--font-scale));
    --font-size-4xl: calc(2.25rem * var(--font-scale));
    --font-size-5xl: calc(3rem * var(--font-scale));
    --font-size-6xl: calc(3.75rem * var(--font-scale));

    /* Line Height Variables */
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;
  }

  /* Mobile First - Small screens */
  @media (max-width: 640px) {
    :root {
      --font-scale: 0.9;
    }
  }

  /* Tablet screens */
  @media (min-width: 641px) and (max-width: 1024px) {
    :root {
      --font-scale: 0.95;
    }
  }

  /* Desktop screens */
  @media (min-width: 1025px) and (max-width: 1440px) {
    :root {
      --font-scale: 1;
    }
  }

  /* Large desktop screens */
  @media (min-width: 1441px) {
    :root {
      --font-scale: 1.05;
    }
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
    transition: font-size 0.3s ease-in-out;
  }

  body {
    @apply bg-background text-foreground;
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
  }

  /* Typography Scale */
  h1 {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
    font-weight: 700;
  }

  h2 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
    font-weight: 600;
  }

  h3 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-snug);
    font-weight: 600;
  }

  h4 {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-snug);
    font-weight: 500;
  }

  h5 {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-normal);
    font-weight: 500;
  }

  h6 {
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    font-weight: 500;
  }

  p {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
  }

  small {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
  }

  .text-xs {
    font-size: var(--font-size-xs) !important;
  }
  .text-sm {
    font-size: var(--font-size-sm) !important;
  }
  .text-base {
    font-size: var(--font-size-base) !important;
  }
  .text-lg {
    font-size: var(--font-size-lg) !important;
  }
  .text-xl {
    font-size: var(--font-size-xl) !important;
  }
  .text-2xl {
    font-size: var(--font-size-2xl) !important;
  }
  .text-3xl {
    font-size: var(--font-size-3xl) !important;
  }
  .text-4xl {
    font-size: var(--font-size-4xl) !important;
  }
  .text-5xl {
    font-size: var(--font-size-5xl) !important;
  }
  .text-6xl {
    font-size: var(--font-size-6xl) !important;
  }
}

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Flashcard flip animation */
  .preserve-3d {
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  .rotate-x-180 {
    transform: rotateX(180deg);
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Ensure smooth transitions for flip animations */
  .flip-transition {
    transition: transform 0.5s ease-in-out;
  }
}

/* 隐藏产生阴影的伪元素 */
.rpv-core__page-layer::after {
  display: none !important;
  content: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
}

.wmde-markdown pre > code {
  margin: 10px 0 !important;
}

/* Chat message content overflow handling */
.chat-content-wrapper {
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
}

.markdown-content {
  max-width: 100%;
  overflow-x: hidden;
  width: 100%;
}

.markdown-content * {
  max-width: 100%;
  word-break: break-word;
  overflow-wrap: break-word;
}

/* Table styles for chat content */
.markdown-content table {

  display: table !important;
}

.markdown-content table th,
.markdown-content table td {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  padding: 8px 12px;
  border: 1px solid hsl(var(--border));
  text-align: left;
  vertical-align: top;
  min-width: 80px; /* Minimum width for readability */
  max-width: 200px; /* Maximum width to prevent overflow */
}

.markdown-content table th {
  background-color: hsl(var(--muted));
  font-weight: 600;
}

.markdown-content table tbody tr:hover {
  background-color: hsl(var(--muted) / 0.5);
}

/* Responsive table behavior */
@media (max-width: 768px) {
  .markdown-content {
    overflow-x: auto;
  }
  
  .markdown-content table {
    font-size: 0.875rem;
    min-width: 100%;
  }
  
  .markdown-content table th,
  .markdown-content table td {
    padding: 6px 8px;
    min-width: 60px;
    max-width: 120px;
  }
}

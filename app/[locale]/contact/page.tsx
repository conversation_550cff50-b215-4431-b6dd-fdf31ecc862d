'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Send, Phone, Mail, MapPin, Clock } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import { postFeedbackCreateFeedback } from '@/servers/api/fankui';
import { useHybridUser } from '@/lib/stores/hybrid-auth-store';
import appConfig from '@/config/app';

export default function ContactPage() {
  const { t } = useTranslation();
  const user = useHybridUser();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    feedbackType: 'partnership',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{
    name?: string;
    email?: string;
    feedbackType?: string;
    message?: string;
    general?: string;
  }>({});

  // 预填用户信息
  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        name: user.nickname || user.username || '',
        email: user.email || ''
      }));
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: typeof errors = {};

    if (!formData.name.trim()) {
      newErrors.name = String(t('contact.errors.nameRequired')) || 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = String(t('contact.errors.emailRequired')) || 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = String(t('contact.errors.emailInvalid')) || 'Invalid email format';
    }

    // if (!formData.feedbackType) {
    //   newErrors.feedbackType = String(t('contact.errors.feedbackTypeRequired')) || 'Feedback type is required';
    // }

    if (!formData.message.trim()) {
      newErrors.message = String(t('contact.errors.messageRequired')) || 'Message is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const response = await postFeedbackCreateFeedback({
        name: formData.name.trim(),
        email: formData.email.trim(),
        category: formData.feedbackType,
        message: formData.message.trim()
      });

      if (response.code === '0') {
        toast.success(String(t('contact.submitSuccess')) || 'Feedback submitted successfully!');
        setFormData(prev => ({ 
          ...prev,
          feedbackType: 'partnership', 
          message: '' 
        }));
      } else {
        throw new Error(response.msg || 'Failed to submit feedback');
      }
    } catch (error: any) {
      console.error('Submit feedback error:', error);
      setErrors({
        general:
          error?.response?.data?.msg ||
          error?.message ||
          String(t('contact.submitError')) ||
          'Failed to submit feedback. Please try again.'
      });
      toast.error(String(t('contact.submitError')) || 'Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">{String(t('contact.title'))}</h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">{String(t('contact.subtitle'))}</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl">{String(t('contact.formTitle'))}</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* General Error Message */}
                    {errors.general && (
                      <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <p className="text-sm text-red-700 dark:text-red-300">{errors.general}</p>
                      </div>
                    )}

                    {/* Name and Email Row */}
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-base font-medium">
                          {String(t('contact.name'))}
                        </Label>
                        <Input
                          id="name"
                          type="text"
                          placeholder={String(t('contact.nameHelper'))}
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          required
                          className={`h-12 ${errors.name ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                        />
                        {errors.name && <p className="text-sm text-red-600 dark:text-red-400">{errors.name}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-base font-medium">
                          {String(t('contact.email'))}
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder={String(t('contact.emailHelper'))}
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          required
                          className={`h-12 ${errors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                        />
                        {errors.email && <p className="text-sm text-red-600 dark:text-red-400">{errors.email}</p>}
                      </div>
                    </div>



                    {/* Message */}
                    <div className="space-y-2">
                      <Label htmlFor="message" className="text-base font-medium">
                        {String(t('contact.message'))}
                      </Label>
                      <Textarea
                        id="message"
                        placeholder={String(t('contact.messagePlaceholder'))}
                        value={formData.message}
                        onChange={(e) => handleInputChange('message', e.target.value)}
                        required
                        className={`min-h-[150px] resize-none ${
                          errors.message ? 'border-red-500 focus-visible:ring-red-500' : ''
                        }`}
                      />
                      {errors.message && <p className="text-sm text-red-600 dark:text-red-400">{errors.message}</p>}
                    </div>

                    {/* Submit Button */}
                    <Button type="submit" disabled={isSubmitting} className="w-full h-12 text-base font-medium">
                      {isSubmitting ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          {String(t('contact.submitting'))}
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Send className="w-4 h-4" />
                          {String(t('contact.submit'))}
                        </div>
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Contact Information */}
            <div className="space-y-6">
              {/* Contact Details */}
              <Card>
                <CardHeader>
                  <CardTitle>{String(t('contact.contactInfo'))}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start gap-3" onClick={() => {
                    navigator.clipboard.writeText(appConfig.email);
                    toast.success(String(t('contact.emailCopied')) || 'Email copied to clipboard');
                  }}>
                    <Mail className="w-5 h-5 text-primary mt-0.5" />
                    <div className="flex flex-row gap-2">
                      <p className="font-medium">{String(t('contact.emailLabel'))}</p>
                      <p className="text-muted-foreground">
                        {appConfig.email}
                      </p>
                    </div>
                  </div>
                  {/* <div className="flex items-start gap-3">
                    <Phone className="w-5 h-5 text-primary mt-0.5" />
                    <div>
                      <p className="font-medium">{String(t('contact.phoneLabel'))}</p>
                      <p className="text-muted-foreground">+****************</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-primary mt-0.5" />
                    <div>
                      <p className="font-medium">{String(t('contact.addressLabel'))}</p>
                      <p className="text-muted-foreground">
                        123 Learning Street
                        <br />
                        Education City, EC 12345
                        <br />
                        United States
                      </p>
                    </div>
                  </div> */}
                </CardContent>
              </Card>

              {/* Business Hours */}
              {/* <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    {String(t('contact.businessHours'))}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>
                      {String(t('contact.monday'))} - {String(t('contact.friday'))}
                    </span>
                    <span>9:00 AM - 6:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{String(t('contact.saturday'))}</span>
                    <span>10:00 AM - 4:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{String(t('contact.sunday'))}</span>
                    <span>{String(t('contact.closed'))}</span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-3">{String(t('contact.timezoneNote'))}</p>
                </CardContent>
              </Card> */}

              {/* Response Time */}
              <Card>
                <CardHeader>
                  <CardTitle>{String(t('contact.responseTime'))}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{String(t('contact.responseTimeDesc'))}</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

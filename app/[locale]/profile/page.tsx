'use client';

import { useState, useEffect, useRef } from 'react';
import { ChevronDown, ChevronUp, Flame, FileText, Calendar, Copy, Sun, Moon, ArrowLeft, Upload } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useRouter } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import { useHybridAuth, useHybridUser, useHybridIsAuthenticated } from '@/lib/stores/hybrid-auth-store';
import { isDarkMode } from '@/lib/utils/theme';
import { postInfraFilesUpload } from '@/servers/api/wendangshangchuan';
import { postMemberAuthUpdateProfile } from '@/servers/api/huiyuanxinxi';
import appConfig from '@/config/app';

export default function ProfilePage() {
  const { t } = useTranslation();
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const { refreshUser } = useHybridAuth();
  const user = useHybridUser();

  const [isPersonalInfoOpen, setIsPersonalInfoOpen] = useState(true);
  const [isSettingsOpen, setIsSettingsOpen] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [nickname, setNickname] = useState(user?.nickname || '');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 页面加载时刷新用户信息
  useEffect(() => {
    refreshUser();
  }, []);

  // 当用户信息更新时，更新nickname状态
  useEffect(() => {
    if (user) {
      setNickname(user.nickname || '');
    }
  }, [user]);

  const handleCopyReferralLink = () => {
    navigator.clipboard.writeText(`${appConfig.appSite}/?recommender=${user?.username}`);
    toast.success(t('profile.referral.copyLink'));
  };

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? 'dark' : 'light');
  };

  const handleDeleteAccount = () => {
    if (window.confirm(t('profile.dangerZone.description'))) {
      toast.error('账户删除功能暂未实现');
    }
  };

  const handleEditAvatar = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);

    try {
      // Step 1: Upload file to get file ID
      const formData = new FormData();
      formData.append('file', file);

      const uploadResponse = await postInfraFilesUpload({
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (uploadResponse?.code !== '0' || !uploadResponse?.data?.fileUrl) {
        throw new Error(uploadResponse?.msg || 'File upload failed');
      }

      const fileUrl = uploadResponse.data.fileUrl;

      // Step 2: Update profile with new avatar URL
      const updateResponse = await postMemberAuthUpdateProfile({
        nickname: nickname,
        avatar: fileUrl,
        gender: user?.gender || 'UNKNOWN'
      });

      if (updateResponse?.code !== '0') {
        throw new Error(updateResponse?.msg || 'Profile update failed');
      }

      // Step 3: Refresh user data
      await refreshUser();
      toast.success(t('profile.userInfo.avatarUpdateSuccess'));
    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error instanceof Error ? error.message : t('profile.userInfo.avatarUpdateError'));
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleSaveChanges = async () => {
    try {
      const updateResponse = await postMemberAuthUpdateProfile({
        nickname: nickname,
        avatar: user?.avatar || '',
        gender: user?.gender || 'UNKNOWN'
      });

      if (updateResponse?.code !== '0') {
        throw new Error(updateResponse?.msg || 'Profile update failed');
      }

      await refreshUser();
      toast.success(t('profile.personalInfo.saveChanges'));
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error(error instanceof Error ? error.message : t('profile.personalInfo.saveError'));
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <div className="w-full py-8 ">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>{t('profile.userInfo.title')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex flex-col items-center text-center">
                  <div className="relative group">
                    <Avatar className="w-24 h-24 mb-4">
                      <AvatarImage
                        src={user?.avatar || '/images/placeholder.svg?height=96&width=96&text=Mountain'}
                        alt="Profile"
                      />
                      <AvatarFallback className="text-2xl">{user?.nickname?.[0] || 'U'}</AvatarFallback>
                    </Avatar>
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept="image/*"
                      onChange={handleFileChange}
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-white dark:bg-neutral-800 shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-neutral-700"
                      onClick={handleEditAvatar}
                      disabled={isUploading}
                    >
                      {isUploading ? <Upload className="w-4 h-4 animate-spin" /> : <Upload className="w-4 h-4" />}
                    </Button>
                  </div>
                  <h3 className="font-semibold text-xl mb-1">{user?.nickname || user?.email || '-'}</h3>
                  <p className="text-sm text-muted-foreground mb-2">{user?.email || t('profile.userInfo.noEmail')}</p>
                  <p className="text-xs text-muted-foreground">
                    {t('profile.userInfo.createdAt')}{' '}
                    {user?.createAt ? new Date(user.createAt).toLocaleDateString() : t('profile.userInfo.unknown')}
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  <Card className="text-center p-4">
                    <CardContent className="p-0">
                      <Flame className="w-12 h-12 mx-auto mb-3 text-orange-500" />
                      <div className="text-3xl font-bold mb-1">1</div>
                      <div className="text-sm text-muted-foreground">{t('profile.userInfo.stats.streak')}</div>
                    </CardContent>
                  </Card>
                  <Card className="text-center p-4">
                    <CardContent className="p-0">
                      <FileText className="w-12 h-12 mx-auto mb-3 text-blue-500" />
                      <div className="text-3xl font-bold mb-1">6</div>
                      <div className="text-sm text-muted-foreground">{t('profile.userInfo.stats.content')}</div>
                    </CardContent>
                  </Card>
                  <Card className="text-center p-4">
                    <CardContent className="p-0">
                      <Calendar className="w-12 h-12 mx-auto mb-3 text-green-500" />
                      <div className="text-3xl font-bold mb-1">1</div>
                      <div className="text-sm text-muted-foreground">{t('profile.userInfo.stats.activeDays')}</div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="md:col-span-2 space-y-6">
            <Card>
              <Collapsible open={isPersonalInfoOpen} onOpenChange={setIsPersonalInfoOpen}>
                <CardHeader>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                      <CardTitle>{t('profile.personalInfo.title')}</CardTitle>
                      {isPersonalInfoOpen ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                    </Button>
                  </CollapsibleTrigger>
                </CardHeader>

                {/* 个人资料 */}
                <CollapsibleContent>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">{t('profile.personalInfo.name')}</label>
                        <input
                          type="text"
                          value={nickname}
                          onChange={(e) => setNickname(e.target.value)}
                          className="flex h-12 p-4 w-full rounded-lg border bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-md transition-all duration-200 border-primary/10"
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block text-muted-foreground">
                          {t('profile.personalInfo.email')}
                        </label>
                        <input
                          type="email"
                          defaultValue={user?.email || ''}
                          className="flex h-12 p-4 w-full rounded-lg border bg-muted/50 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none text-md transition-all duration-200 border-muted text-muted-foreground cursor-not-allowed"
                          disabled
                          readOnly
                        />
                      </div>
                    </div>

                    {/* <div>
                      <label className="text-sm font-medium mb-2 block">{t('profile.personalInfo.language')}</label>
                      <Select defaultValue="en">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">🇺🇸 🇬🇧 英语</SelectItem>
                          <SelectItem value="zh-CN">🇨🇳 简体中文</SelectItem>
                          <SelectItem value="zh-HK">🇹🇼 繁体中文</SelectItem>
                        </SelectContent>
                      </Select>
                    </div> */}

                    {/* <div>
                      <label className="text-sm font-medium mb-2 block">{t('profile.personalInfo.aiModel')}</label>
                      <Select defaultValue="default">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="default">Default</SelectItem>
                          <SelectItem value="gpt-4">GPT-4</SelectItem>
                          <SelectItem value="claude">Claude</SelectItem>
                        </SelectContent>
                      </Select>
                    </div> */}

                    <Button className="w-full md:w-auto" onClick={handleSaveChanges}>
                      {t('profile.personalInfo.saveChanges')}
                    </Button>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>

            <Card>
              <Collapsible open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
                <CardHeader>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                      <CardTitle>{t('profile.appSettings.title')}</CardTitle>
                      {isSettingsOpen ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                    </Button>
                  </CollapsibleTrigger>
                </CardHeader>
                <CollapsibleContent>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between p-4 border border-border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <Sun className="w-5 h-5" />
                          <Moon className="w-5 h-5" />
                        </div>
                        <div>
                          <div className="font-medium">{t('profile.appSettings.darkMode.title')}</div>
                          <div className="text-sm text-muted-foreground">
                            {t('profile.appSettings.darkMode.description')}
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={isDarkMode(theme)}
                        onCheckedChange={handleThemeToggle}
                        aria-label={t('profile.appSettings.darkMode.title')}
                      />
                    </div>

                    {/* 通知 */}
                    {/* <div className="space-y-4">
                      <h4 className="font-medium">{t('profile.appSettings.notifications.title')}</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{t('profile.appSettings.notifications.email.title')}</div>
                            <div className="text-sm text-muted-foreground">
                              {t('profile.appSettings.notifications.email.description')}
                            </div>
                          </div>
                          <Switch defaultChecked />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{t('profile.appSettings.notifications.push.title')}</div>
                            <div className="text-sm text-muted-foreground">
                              {t('profile.appSettings.notifications.push.description')}
                            </div>
                          </div>
                          <Switch />
                        </div>
                      </div>
                    </div> */}
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('profile.referral.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">{t('profile.referral.description')}</p>
                  <div className="flex gap-3">
                    <input
                      type="text"
                      value={`${appConfig.appSite}/?recommender=${user?.username}`}
                      readOnly
                      className="flex-1 h-12 p-4 rounded-lg border bg-muted/50 ring-offset-background text-md transition-all duration-200 border-muted text-muted-foreground cursor-not-allowed"
                    />
                    <Button onClick={handleCopyReferralLink}>
                      <Copy className="w-4 h-4 mr-2" />
                      {t('profile.referral.copyLink')}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="text-red-600">{t('profile.dangerZone.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">{t('profile.dangerZone.description')}</p>
                  <Button variant="destructive" onClick={handleDeleteAccount} className="w-full md:w-auto">
                    {t('profile.dangerZone.deleteAccount')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

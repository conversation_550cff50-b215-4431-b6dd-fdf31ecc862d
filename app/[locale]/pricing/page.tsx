'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, X, Loader2, <PERSON>ting<PERSON>, CreditCard, Copy } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useTranslation } from '@/lib/translation-context';
import { postStripeCreateCheckoutSession, postStripeCreatePortalSession } from '@/servers/api/zhifu';
import { toast } from 'sonner';
import { useHybridIsAuthenticated, useHybridUser } from '@/lib/stores';
import { useRouter } from 'next/navigation';

// 定义计划生成函数
const createPlans = (t: any) => {
  // 基础计划模板
  const basePlans = [
    {
      id: 'free',
      name: t('upgradeModal.plans.free.name'),
      price: '$0',
      billingCycle: t('upgradeModal.plans.free.billingCycle'),
      lookupKey: '',
      features: [
        { text: t('upgradeModal.plans.free.features.uploads'), included: true },
        { text: t('upgradeModal.plans.free.features.aiChat'), included: true },
        { text: t('upgradeModal.plans.free.features.quizAnswers'), included: true },
        { text: t('upgradeModal.plans.free.features.exams'), included: true },
        { text: t('upgradeModal.plans.free.features.voiceChat'), included: true },
        { text: t('upgradeModal.plans.free.features.fileSize'), included: true }
      ],
      buttonText: t('upgradeModal.startButton'),
      variant: 'outline' as const
    },
    {
      id: 'pro',
      name: t('upgradeModal.plans.pro.name'),
      features: [
        { text: t('upgradeModal.plans.pro.features.uploads'), included: true },
        { text: t('upgradeModal.plans.pro.features.aiChat'), included: true },
        { text: t('upgradeModal.plans.pro.features.quizzes'), included: true },
        { text: t('upgradeModal.plans.pro.features.exams'), included: true },
        { text: t('upgradeModal.plans.pro.features.voiceMode'), included: true },
        { text: t('upgradeModal.plans.pro.features.fileSize'), included: true },
        { text: t('upgradeModal.plans.pro.features.support'), included: true }
      ],
      buttonText: t('upgradeModal.selectPlan'),
      variant: 'default' as const,
      popular: true
    },
    {
      id: 'team',
      name: t('upgradeModal.plans.team.name'),
      price: t('upgradeModal.plans.team.price'),
      billingCycle: '',
      lookupKey: '',
      features: [
        { text: t('upgradeModal.plans.team.features.everything'), included: true },
        { text: t('upgradeModal.plans.team.features.billing'), included: true },
        { text: t('upgradeModal.plans.team.features.members'), included: true },
        { text: t('upgradeModal.plans.team.features.storage'), included: true },
        { text: t('upgradeModal.plans.team.features.collaboration'), included: true }
      ],
      buttonText: t('upgradeModal.contactButton'),
      variant: 'outline' as const
    }
  ];

  // Pro 计划的差异配置
  const proPlanConfig = {
    monthly: {
      price: '$9.99',
      billingCycle: t('upgradeModal.plans.pro.billingCycleMonthly'),
      lookupKey: 'tutoro-ai-monthly'
    },
    annually: {
      price: '$5.99',
      billingCycle: t('upgradeModal.plans.pro.billingCycleAnnually'),
      lookupKey: 'tutoro-ai-annually'
    }
  };

  // 生成具体计划
  const generatePlans = (isAnnual: boolean) => {
    const configKey = isAnnual ? 'annually' : 'monthly';
    return basePlans.map(plan => {
      if (plan.id === 'pro') {
        return {
          ...plan,
          ...proPlanConfig[configKey]
        };
      }
      return plan;
    });
  };

  return {
    monthly: generatePlans(false),
    annually: generatePlans(true)
  };
};

const universityLogos = [
  {
    name: 'Stanford University',
    src: '/images/college_01.png',
    alt: 'Stanford University Logo'
  },
  {
    name: 'University of Michigan',
    src: '/images/college_02.png',
    alt: 'University of Michigan Logo'
  },
  {
    name: 'Harvard University',
    src: '/images/college_03.png',
    alt: 'Harvard University Logo'
  },
  {
    name: 'Michigan State University',
    src: '/images/college_04.png',
    alt: 'Michigan State University Logo'
  },
  {
    name: 'MIT',
    src: '/images/college_05.png',
    alt: 'MIT Logo'
  },
  {
    name: 'Princeton University',
    src: '/images/college_06.png',
    alt: 'Princeton University Logo'
  },
  {
    name: 'University College 7',
    src: '/images/college_07.png',
    alt: 'University College 7 Logo'
  },
  {
    name: 'University College 8',
    src: '/images/college_08.png',
    alt: 'University College 8 Logo'
  }
];

// 成功显示组件
const SuccessDisplay = ({ sessionId, t }: { sessionId: string; t: any }) => {
  const [loading, setLoading] = useState(false);

  const handleManageBilling = async () => {
    setLoading(true);
    try {
      const response = await postStripeCreatePortalSession({ sessionId });
      if (response.data) {
        window.location.href = response.data;
      }
    } catch (error) {
      console.error('Error creating portal session:', error);
      toast.error(t('upgradeModal.errors.portalFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto max-w-3xl py-8 px-4">
      <Card className="text-center">
        <CardHeader>
          <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-4">
            <Check className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          <CardTitle className="text-2xl text-green-600 dark:text-green-400">
            {t('upgradeModal.success.title')}
          </CardTitle>
          <CardDescription>{t('upgradeModal.success.description')}</CardDescription>
        </CardHeader>
        <CardFooter className="flex justify-center">
          <Button onClick={handleManageBilling} disabled={loading} className="w-full max-w-sm">
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t('upgradeModal.success.manageBilling')}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

// 消息显示组件
const MessageDisplay = ({ message, t }: { message: string; t: any }) => {
  return (
    <div className="container mx-auto max-w-3xl py-8 px-4">
      <Card className="text-center">
        <CardHeader>
          <div className="mx-auto w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mb-4">
            <X className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
          </div>
          <CardTitle className="text-xl text-yellow-600 dark:text-yellow-400">
            {t('upgradeModal.canceled.title')}
          </CardTitle>
          <CardDescription>{message}</CardDescription>
        </CardHeader>
        <CardFooter className="flex justify-center">
          <Button variant="outline" onClick={() => window.location.reload()} className="w-full max-w-sm">
            {t('upgradeModal.canceled.button')}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

// 订阅管理显示组件
const SubscriptionManagement = ({ user, t }: { user: any; t: any }) => {
  const [loading, setLoading] = useState(false);

  const handleManageBilling = async () => {
    if (!user?.subscription) {
      toast.error(t('upgradeModal.errors.noSubscription'));
      return;
    }

    setLoading(true);
    try {
      // 创建portal session来管理订阅
      const response = await postStripeCreatePortalSession({ 
        sessionId: user.stripeCustomerId || '' 
      });
      if (response.data) {
        window.location.href = response.data;
      } else {
        throw new Error('No portal URL returned');
      }
    } catch (error) {
      console.error('Error creating portal session:', error);
      toast.error(t('upgradeModal.errors.portalFailed'));
    } finally {
      setLoading(false);
    }
  };

  const getPlanDisplayName = () => {
    if (user?.subscription?.product === 'PRO') {
      const isMonthly = user?.subscription?.price === 'MONTHLY';
      return isMonthly ? t('upgradeModal.plans.pro.billingCycleMonthly') : t('upgradeModal.plans.pro.billingCycleAnnually');
    }
    return t('upgradeModal.plans.free.name');
  };

  const getPlanPrice = () => {
    if (user?.subscription?.product === 'PRO') {
      const isMonthly = user?.subscription?.price === 'MONTHLY';
      return isMonthly ? '$9.99/month' : '$5.99/month';
    }
    return '$0';
  };

  const getSubscriptionStatus = () => {
    if (user?.subscription?.status === 'ACTIVE') {
      return { text: t('upgradeModal.subscription.active'), color: 'text-green-600 bg-green-100' };
    } else if (user?.subscription?.status === 'CANCELED') {
      return { text: t('upgradeModal.subscription.canceled'), color: 'text-red-600 bg-red-100' };
    }
    return { text: t('upgradeModal.subscription.unknown'), color: 'text-gray-600 bg-gray-100' };
  };

  const subscriptionStatus = getSubscriptionStatus();

  return (
    <div className="container mx-auto max-w-4xl py-8 px-4">
      <section className="text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
          {t('upgradeModal.subscription.title')}
        </h1>
        <p className="mt-4 text-xl text-gray-600 dark:text-gray-300">
          {t('upgradeModal.subscription.description')}
        </p>
      </section>

      {/* 当前订阅状态 */}
      <Card className="mb-8 border-primary/20 shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
            <CreditCard className="w-8 h-8 text-primary" />
          </div>
          <CardTitle className="text-2xl">
            {user?.subscription?.product === 'PRO' 
              ? t('upgradeModal.subscription.currentPlan') 
              : t('upgradeModal.subscription.noPlan')}
          </CardTitle>
          <div className="flex items-center justify-center gap-2 mt-2">
            <Badge className={`${subscriptionStatus.color} border-0`}>
              {subscriptionStatus.text}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="text-center space-y-4">
          {user?.subscription?.product === 'PRO' ? (
            <>
              <div className="flex items-center justify-center gap-2">
                <span className="text-3xl font-bold">{getPlanPrice()}</span>
                <span className="text-lg text-gray-500">
                  {user?.subscription?.price === 'MONTHLY' ? '/month' : '/month (billed annually)'}
                </span>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                {t('upgradeModal.subscription.proDescription')}
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div className="flex items-center gap-2">
                  <Check className="h-5 w-5 text-green-500" />
                  <span className="text-sm">{t('upgradeModal.plans.pro.features.uploads')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-5 w-5 text-green-500" />
                  <span className="text-sm">{t('upgradeModal.plans.pro.features.aiChat')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-5 w-5 text-green-500" />
                  <span className="text-sm">{t('upgradeModal.plans.pro.features.quizzes')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-5 w-5 text-green-500" />
                  <span className="text-sm">{t('upgradeModal.plans.pro.features.exams')}</span>
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center justify-center gap-2">
                <span className="text-3xl font-bold">$0</span>
                <span className="text-lg text-gray-500">/month</span>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                {t('upgradeModal.subscription.freeDescription')}
              </p>
            </>
          )}
        </CardContent>
        
        <CardFooter className="flex flex-col gap-3">
          {user?.subscription?.product === 'PRO' ? (
            <>
              <Button 
                onClick={handleManageBilling} 
                disabled={loading} 
                className="w-full"
                size="lg"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Settings className="mr-2 h-4 w-4" />
                {t('upgradeModal.subscription.manageBilling')}
              </Button>
              <p className="text-sm text-gray-500 text-center">
                {t('upgradeModal.subscription.manageDescription')}
              </p>
            </>
          ) : (
            <Button 
              onClick={() => window.location.reload()} 
              className="w-full"
              size="lg"
            >
              {t('upgradeModal.subscription.upgradeToPro')}
            </Button>
          )}
        </CardFooter>
      </Card>

      {/* 如果是免费用户，显示升级选项 */}
      {user?.subscription?.product !== 'PRO' && (
        <section className="text-center">
          <h2 className="text-2xl font-semibold mb-4">
            {t('upgradeModal.subscription.upgradePrompt')}
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            {t('upgradeModal.subscription.upgradePromptDescription')}
          </p>
          <Button 
            onClick={() => window.location.reload()} 
            variant="outline" 
            size="lg"
          >
            {t('upgradeModal.subscription.viewPlans')}
          </Button>
        </section>
      )}
    </div>
  );
};

export default function FeesPage() {
  const [isAnnual, setIsAnnual] = useState(true);
  const [loading, setLoading] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [sessionId, setSessionId] = useState('');
  const [message, setMessage] = useState('');
  const { t } = useTranslation();
  const plans = createPlans(t);
  const currentPlans = isAnnual ? plans.annually : plans.monthly;
  const isAuthenticated = useHybridIsAuthenticated();
  const user = useHybridUser();
  const router = useRouter();

  const handleCopyCode = () => {
    navigator.clipboard.writeText("SUMMER25");
    toast.success('优惠码已复制');
  };

  useEffect(() => {
    // Check to see if this is a redirect back from Checkout
    const query = new URLSearchParams(window.location.search);

    if (query.get('success')) {
      setSuccess(true);
      setSessionId(query.get('session_id') || '');
    }

    if (query.get('canceled')) {
      setSuccess(false);
      setMessage(t('upgradeModal.canceled.message'));
    }
  }, []);

  const handleSubscribe = async (lookupKey: string, id: string) => {
    // 特殊计划的路由处理
    const specialPlanRoutes: Record<string, string> = {
      'team': '/contact',
      'free': isAuthenticated ? '/' : '/login'
    };

    const routePath = specialPlanRoutes[id];
    if (routePath) {
      router.push(routePath);
      return;
    }

    // Pro 计划需要认证
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    // 创建支付会话
    setLoading(lookupKey);
    try {
      const response = await postStripeCreateCheckoutSession({ lookupKey });
      if (response.data) {
        window.location.href = response.data;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error(t('upgradeModal.errors.checkoutFailed'));
    } finally {
      setLoading(null);
    }
  };

  // 如果是成功页面
  if (success && sessionId) {
    return <SuccessDisplay sessionId={sessionId} t={t} />;
  }

  // 如果有错误消息
  if (message) {
    return <MessageDisplay message={message} t={t} />;
  }

  // 如果用户已经是PRO用户，显示订阅管理页面
  if (isAuthenticated && user?.subscription?.product === 'PRO') {
    return <SubscriptionManagement user={user} t={t} />;
  }

  return (
    <div className="container mx-auto max-w-5xl py-8 px-4">
      {/* 促销横幅 */}
      {/* <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg mb-8">
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <span className="text-green-600 dark:text-green-400">🎉</span>
            <span className="text-sm font-semibold text-green-700 dark:text-green-300">
              {t("upgradeModal.discount")}
            </span>
          </div>
          <div className="inline-flex items-center gap-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg px-3 py-1">
            <span className="font-mono text-sm text-green-700 dark:text-green-300">SUMMER25</span>
            <Button variant="ghost" size="icon" className="h-4 w-4 p-0" onClick={handleCopyCode}>
              <Copy className="h-3 w-3" />
            </Button>
          </div>
          <p className="text-xs text-green-600 dark:text-green-400 mt-1">
            {t("upgradeModal.useCode")}
          </p>
        </div>
      </div> */}
      
      <section className="text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
          {t('upgradeModal.upgradeTitle')}
        </h1>
        <p className="mt-4 text-xl text-gray-600 dark:text-gray-300">{t('upgradeModal.choosePlan')}</p>
        <div className="mt-8 flex items-center justify-center space-x-3">
          <Label htmlFor="billing-cycle" className={!isAnnual ? 'text-primary font-semibold' : 'text-muted-foreground'}>
            {t('upgradeModal.monthly')}
          </Label>
          <Switch
            id="billing-cycle"
            checked={isAnnual}
            onCheckedChange={setIsAnnual}
            aria-label={t('upgradeModal.toggleBilling')}
          />
          <Label htmlFor="billing-cycle" className={isAnnual ? 'text-primary font-semibold' : 'text-muted-foreground'}>
            {t('upgradeModal.annual')}
          </Label>
          {isAnnual && (
            <Badge variant="secondary" className="bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100">
              {t('upgradeModal.save')}
            </Badge>
          )}
        </div>
      </section>

      <section className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-stretch">
        {currentPlans.map((plan) => (
          <Card
            key={plan.id}
            className={`flex flex-col ${
              plan.popular ? 'border-primary ring-2 ring-primary shadow-xl dark:bg-slate-800' : 'dark:bg-slate-800/50'
            }`}
          >
            {plan.popular && (
              <div className="px-4 py-1 bg-primary text-primary-foreground text-center text-sm font-semibold rounded-t-lg -mb-px">
                {t('upgradeModal.mostPopular')}
              </div>
            )}
            <CardHeader className="pb-4">
              <CardTitle className="text-2xl">{plan.name}</CardTitle>
              <div className="flex items-baseline">
                <span className="text-4xl font-extrabold tracking-tight">{plan.price}</span>
                {plan.billingCycle && (
                  <span className="ml-1 text-xl font-semibold text-gray-500 dark:text-gray-400">
                    {plan.billingCycle}
                  </span>
                )}
              </div>
              <CardDescription>
                {plan.id === 'pro'
                  ? t('upgradeModal.proDescription')
                  : plan.id === 'team'
                  ? t('upgradeModal.teamDescription')
                  : t('upgradeModal.freeDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow space-y-3">
              <ul className="space-y-2 text-sm">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    {feature.included ? (
                      <Check className="h-5 w-5 text-green-500 mr-2 shrink-0 mt-0.5" />
                    ) : (
                      <X className="h-5 w-5 text-red-500 mr-2 shrink-0 mt-0.5" />
                    )}
                    <span
                      className={
                        feature.included
                          ? 'text-gray-900 dark:text-gray-100'
                          : 'text-gray-500 dark:text-gray-400 line-through'
                      }
                    >
                      {feature.text}
                    </span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                size="lg"
                variant={plan.variant}
                disabled={loading === plan.lookupKey}
                onClick={() => handleSubscribe(plan.lookupKey || '', plan.id)}
              >
                {loading === plan.lookupKey && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {plan.buttonText}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </section>

      {/* 大学logo滚动 */}
      <section className="mt-16 text-center">
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">{t('upgradeModal.joinCommunity')}</p>
        <div className="logo-scroll-container relative overflow-hidden py-4">
          <div className="logo-scroll-track flex">
            {/* 第一组logo */}
            {universityLogos.map((logo) => (
              <div key={logo.name} className="logo-item flex-shrink-0 mx-8">
                <Image src={logo.src} alt={logo.alt} width={100} height={30} className="opacity-70" />
              </div>
            ))}
            {/* 复制一组logo用于无缝循环 */}
            {universityLogos.map((logo) => (
              <div key={`${logo.name}-duplicate`} className="logo-item flex-shrink-0 mx-8">
                <Image src={logo.src} alt={logo.alt} width={100} height={30} className="opacity-70" />
              </div>
            ))}
          </div>
        </div>

        <style jsx>{`
          .logo-scroll-container {
            mask-image: linear-gradient(
              to right,
              transparent,
              black 10%,
              black 90%,
              transparent
            );
          }
          
          .logo-scroll-track {
            animation: scroll 30s linear infinite;
            width: max-content;
          }
          
          .logo-scroll-track:hover {
            animation-play-state: paused;
          }
          
          @keyframes scroll {
            0% {
              transform: translateX(0);
            }
            100% {
              transform: translateX(-50%);
            }
          }
        `}</style>
      </section>
    </div>
  );
}

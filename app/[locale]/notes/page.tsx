'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import {
  Search,
  Tag,
  Clock,
  BookOpen,
  MessageSquare,
  Plus,
  Filter,
  ChevronLeft,
  ChevronRight,
  Edit,
  Trash2,
  X
} from 'lucide-react';
import { format } from 'date-fns';
import { useTranslation } from '@/lib/translation-context';
import { useGetNotes, useGetNoteTags, useDeleteNote } from '@/lib/api/hooks';
import { CollectNoteModal } from '@/components/notes/collect-note-modal';

// 定义 API 返回的笔记数据结构
interface ApiNote {
  id: number;
  createAt: string;
  updateAt: string;
  context: string;
  tags?: { tagId: number; tagName: string }[];
  content?: {
    id: number;
    title: string;
  };
  title: string;
  space?: {
    id: number;
    title: string;
  };
}

// 解析后的笔记结构
interface ParsedNote {
  id: number;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  source: 'chat' | 'manual' | 'article';
  contentTitle?: string;
  spaceTitle?: string;
}

interface Tag {
  id: number;
  name: string;
}

export default function NotesPage() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingNote, setEditingNote] = useState<ParsedNote | null>(null);
  const [deletingNote, setDeletingNote] = useState<ParsedNote | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const pageSize = 10;

  // 使用 API 获取笔记数据
  const {
    data: notesResponse,
    isLoading,
    error,
    refetch
  } = useGetNotes({
    page: currentPage,
    pageSize: pageSize,
    tagId: selectedTagIds.length > 0 ? selectedTagIds.join(',') : undefined
  });

  // 获取标签列表
  const { data: tagsResponse, isLoading: tagsLoading } = useGetNoteTags();

  // 删除笔记
  const deleteNote = useDeleteNote({
    onSuccess: () => {
      setIsDeleteModalOpen(false);
      setDeletingNote(null);
      refetch();
    },
    onError: (error) => {
      console.error('删除笔记失败:', error);
      // 这里可以添加错误提示
    }
  });

  // 解析笔记数据
  const parseNotes = (apiNotes: ApiNote[]): ParsedNote[] => {
    return apiNotes.map((apiNote) => {
      let parsedContent: any = {};
      try {
        parsedContent = JSON.parse(apiNote.context || '{}');
      } catch (e) {
        console.error(t('notes.parseError'), e);
        parsedContent = { title: t('notes.noTitle'), content: apiNote.context || '' };
      }

      return {
        id: apiNote.id,
        title: parsedContent.title || apiNote?.title || t('notes.noTitle'),
        content: parsedContent.content || apiNote.context || '',
        tags: apiNote.tags?.map((tag) => tag.tagName) || [],
        createdAt: new Date(apiNote.createAt),
        updatedAt: new Date(apiNote.updateAt),
        source: parsedContent.source || 'manual',
        contentTitle: apiNote.content?.title,
        spaceTitle: apiNote.space?.title
      };
    });
  };

  const notes = notesResponse?.data?.record ? parseNotes(notesResponse.data.record) : [];

  // 从 API 获取标签列表
  const allTags: Tag[] =
    tagsResponse?.data?.map((tag: { id?: number; name?: string }) => ({
      id: tag.id || 0,
      name: tag.name || ''
    })) || [];

  // 分页信息
  const totalPages = notesResponse?.data?.pageCount || 1;
  const totalCount = notesResponse?.data?.totalCount || 0;

  const filteredNotes = notes.filter((note) => {
    const matchesSearch =
      searchQuery === '' ||
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.content.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesTags = selectedTags.length === 0 || selectedTags.every((tag) => note.tags.includes(tag));

    return matchesSearch && matchesTags;
  });

  const toggleTag = (tagId: number, tagName: string) => {
    const isSelected = selectedTagIds.includes(tagId.toString());

    if (isSelected) {
      setSelectedTagIds((prev) => prev.filter((id) => id !== tagId.toString()));
      setSelectedTags((prev) => prev.filter((name) => name !== tagName));
    } else {
      setSelectedTagIds((prev) => [...prev, tagId.toString()]);
      setSelectedTags((prev) => [...prev, tagName]);
    }

    // 重置到第一页
    setCurrentPage(1);
  };

  const handleNewNote = () => {
    setEditingNote(null);
    setIsModalOpen(true);
  };

  const handleEditNote = (note: ParsedNote) => {
    setEditingNote(note);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingNote(null);
    // 刷新笔记列表
    refetch();
  };

  const handleDeleteNote = (note: ParsedNote) => {
    setDeletingNote(note);
    setIsDeleteModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (deletingNote) {
      deleteNote.mutate(deletingNote.id);
    }
  };

  const handleDeleteModalClose = () => {
    setIsDeleteModalOpen(false);
    setDeletingNote(null);
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'chat':
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'article':
        return <BookOpen className="h-4 w-4 text-green-500" />;
      default:
        return <BookOpen className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPreviewText = (note: ParsedNote) => {
    return note.content.slice(0, 150) + (note.content.length > 150 ? '...' : '');
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 max-w-6xl">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p>{t('notes.loading') || 'Loading...'}</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6 max-w-6xl">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-red-500 mb-4">{t('notes.error') || 'Error loading notes'}</p>
            <Button onClick={() => window.location.reload()}>{t('notes.retry') || 'Retry'}</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">{t('notes.title')}</h1>
        <Button onClick={handleNewNote}>
          <Plus className="h-4 w-4 mr-2" />
          {t('notes.newNote')}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="space-y-6">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={t('notes.search.placeholder')}
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Tags */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                {t('notes.tags.title')}
              </h3>
              {selectedTags.length > 0 && (
                <Button variant="ghost" size="sm" onClick={() => setSelectedTags([])}>
                  {t('notes.tags.clear')}
                </Button>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              {allTags.map((tag: Tag) => (
                <Badge
                  key={tag.id}
                  variant={selectedTags.includes(tag.name) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => toggleTag(tag.id, tag.name)}
                >
                  {tag.name}
                </Badge>
              ))}
              {allTags.length === 0 && !tagsLoading && (
                <p className="text-sm text-gray-500">{t('notes.tags.noTags')}</p>
              )}
              {tagsLoading && <p className="text-sm text-gray-500">{t('notes.loading') || 'Loading tags...'}</p>}
            </div>
          </div>

          {/* Filters */}
          {/* <div>
            <h3 className="font-medium flex items-center mb-2">
              <Filter className="h-4 w-4 mr-2" />
              {t('notes.filters.title')}
            </h3>
            <div className="space-y-2">
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <Clock className="h-4 w-4 mr-2" />
                {t('notes.filters.recentlyUpdated')}
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <MessageSquare className="h-4 w-4 mr-2" />
                {t('notes.filters.fromChat')}
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <BookOpen className="h-4 w-4 mr-2" />
                {t('notes.filters.fromArticle')}
              </Button>
            </div>
          </div> */}
        </div>

        {/* Main Content */}
        <div className="md:col-span-3">
          <Tabs defaultValue="grid">
            <div className="flex items-center justify-between mb-4">
              <p className="text-sm text-gray-500">
                {filteredNotes.length} {t('notes.view.noteCount')}
              </p>
              <TabsList>
                <TabsTrigger value="grid">{t('notes.view.grid')}</TabsTrigger>
                <TabsTrigger value="list">{t('notes.view.list')}</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="grid" className="mt-0">
              {filteredNotes.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredNotes.map((note) => (
                    <Card key={note.id} className="overflow-hidden group">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {getSourceIcon(note.source)}
                            <p className="text-xs text-gray-500 ml-2">{format(note.createdAt, 'yyyy/MM/dd')}</p>
                          </div>
                          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button variant="ghost" size="sm" onClick={() => handleEditNote(note)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteNote(note)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardTitle className="text-lg mt-2">{note.title}</CardTitle>
                        {note.contentTitle && (
                          <p className="text-sm text-gray-500">
                            {t('notes.source')}: {note.contentTitle}
                          </p>
                        )}
                      </CardHeader>
                      <CardContent className="pb-3">
                        <p className="text-sm text-gray-600 line-clamp-3">{getPreviewText(note)}</p>
                      </CardContent>
                      <CardFooter className="pt-0">
                        <div className="flex flex-wrap gap-1">
                          {note.tags.map((tag: string) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <BookOpen className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">{t('notes.empty.title')}</h3>
                  <p className="text-sm text-gray-500 mb-4">{t('notes.empty.description')}</p>
                  <Button onClick={handleNewNote}>
                    <Plus className="h-4 w-4 mr-2" />
                    {t('notes.empty.action')}
                  </Button>
                </div>
              )}
            </TabsContent>

            <TabsContent value="list" className="mt-0">
              {filteredNotes.length > 0 ? (
                <div className="space-y-2">
                  {filteredNotes.map((note) => (
                    <div
                      key={note.id}
                      className="flex items-start p-3 border rounded-lg hover:bg-gray-50 transition-colors group"
                    >
                      <div className="mr-4 mt-1">{getSourceIcon(note.source)}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium">{note.title}</h3>
                          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button variant="ghost" size="sm" onClick={() => handleEditNote(note)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteNote(note)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        {note.contentTitle && (
                          <p className="text-xs text-gray-500">
                            {t('notes.source')}: {note.contentTitle}
                          </p>
                        )}
                        <p className="text-sm text-gray-600 line-clamp-1 mt-1">{getPreviewText(note)}</p>
                        <div className="flex items-center mt-2">
                          <p className="text-xs text-gray-500 mr-4">{format(note.createdAt, 'yyyy/MM/dd')}</p>
                          <div className="flex flex-wrap gap-1">
                            {note.tags.map((tag: string) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <BookOpen className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">{t('notes.empty.title')}</h3>
                  <p className="text-sm text-gray-500 mb-4">{t('notes.empty.description')}</p>
                  <Button onClick={handleNewNote}>
                    <Plus className="h-4 w-4 mr-2" />
                    {t('notes.empty.action')}
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* 分页控件 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <p className="text-sm text-gray-500">
                {t('notes.pagination.total')
                  .replace('{{count}}', totalCount.toString())
                  .replace('{{current}}', currentPage.toString())
                  .replace('{{total}}', totalPages.toString())}
              </p>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  {t('notes.pagination.previous')}
                </Button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  {t('notes.pagination.next')}
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* CollectNoteModal */}
      <CollectNoteModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        mode={editingNote ? 'edit' : 'create-empty'}
        noteId={editingNote?.id || null}
        editingNote={editingNote}
      />

      {/* DeleteConfirmModal */}
      {isDeleteModalOpen && deletingNote && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 transition-all duration-300">
          <div className="bg-white dark:bg-neutral-900 rounded-2xl max-w-md w-full p-6 shadow-xl transform transition-all duration-300 scale-100">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                {t('notes.delete.title') || '确定删除这个笔记吗？'}
              </h2>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-400 hover:text-gray-600"
                onClick={handleDeleteModalClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="mb-8">
              <p className="text-gray-600">
                "{deletingNote.title}" {t('notes.delete.message') || '将被永久删除。'}
              </p>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={handleDeleteModalClose}
                className="px-6 py-2 text-gray-600 border-gray-300 hover:bg-gray-50"
                disabled={deleteNote.isPending}
              >
                {t('notes.delete.cancel') || '取消'}
              </Button>
              <Button
                onClick={handleConfirmDelete}
                className="px-6 py-2 bg-red-500 hover:bg-red-600 text-white"
                disabled={deleteNote.isPending}
              >
                {deleteNote.isPending ? t('notes.delete.deleting') || '删除中...' : t('notes.delete.confirm') || '删除'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';

import { ContentGrid } from '@/components/ui/content-grid';
import type { ContentBlockData, ContentBlockActions } from '@/components/ui/content-block';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Grid3X3, List, BookOpen, ChevronLeft, ChevronRight } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { useGetHistoryRecords } from '@/lib/api/hooks';
import { useAppDataStore } from '@/lib/stores';

// 定义 API 返回的历史记录数据结构
interface ApiHistoryRecord {
  id: number;
  title: string;
  fileUrl: string;
  createAt: string;
  updateAt: string;
  space?: {
    id: number;
    title: string;
  };
}

// 解析后的历史记录结构
interface ParsedHistoryRecord {
  id: string;
  title: string;
  content: string;
  type: 'study' | 'exam' | 'chat' | 'other';
  createdAt: Date;
  spaceId: string;
  spaceName: string;
}

export default function HistoryPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const { t } = useTranslation();
  const router = useRouter();
  const pageSize = 10;

  // 使用 API hook 获取历史记录数据
  const {
    data: historyResponse,
    isLoading,
    error,
    refetch
  } = useGetHistoryRecords({
    page: currentPage,
    pageSize: pageSize
  });

  // 解析历史记录数据
  const parseHistoryRecords = (apiRecords: ApiHistoryRecord[]): ParsedHistoryRecord[] => {
    return apiRecords.map((apiRecord) => ({
      id: String(apiRecord.id),
      title: apiRecord.title || t('historyPage.noTitle'),
      content: apiRecord.fileUrl || '',
      type: 'study' as const,
      createdAt: new Date(apiRecord.createAt),
      spaceId: String(apiRecord.space?.id || ''),
      spaceName: apiRecord.space?.title || ''
    }));
  };

  const historyRecords = historyResponse?.data?.record ? parseHistoryRecords(historyResponse.data.record) : [];

  // 分页信息
  const totalPages = historyResponse?.data?.pageCount || 1;
  const totalCount = historyResponse?.data?.totalCount || 0;

  // 将 historyRecords 转换为 ContentBlockData 格式
  const historyItems: ContentBlockData[] = historyRecords.map((record) => ({
    id: record.id,
    title: record.title,
    description: record.content,
    type: record.type === 'exam' ? 'exam' : 'article',
    date: format(record.createdAt, 'yyyy年MM月dd日'),
    status: record.type === 'exam' ? 'completed' : 'viewed',
    space: record.spaceId
      ? {
          name: record.spaceName,
          id: record.spaceId
        }
      : undefined
  }));

  // 从应用数据store获取空间列表，用于移动操作
  const { spaces } = useAppDataStore();

  const handleTitleEdit = (id: string, newTitle: string) => {
    // 这里可以添加更新历史记录标题的逻辑
    console.log('Title edit not implemented yet:', { id, newTitle });
  };

  const handleMove = (id: string, targetSpaceId: string) => {
    // 这里可以添加移动历史记录到其他空间的逻辑
    console.log('Move not implemented yet:', { id, targetSpaceId });
  };

  const handleDelete = (id: string) => {
    // 这里可以添加删除历史记录的逻辑
    console.log('Delete not implemented yet:', id);
    // 删除后刷新数据
    refetch();
  };

  const handleShare = (id: string) => {
    console.log('Sharing item:', id);
  };

  const handleClick = (id: string) => {
    router.push(`/article/${id}`);
  };

  const actions: ContentBlockActions = {
    onTitleEdit: handleTitleEdit,
    onMove: handleMove,
    onDelete: handleDelete,
    onShare: handleShare,
    onClick: handleClick
  };

  const filteredItems = historyItems.filter((item) => {
    const matchesSearch =
      searchQuery === '' ||
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesFilter = filterType === 'all' || item.type === filterType;

    return matchesSearch && matchesFilter;
  });

  const EmptyState = () => (
    <div className="text-center py-12 text-gray-500 dark:text-gray-400">
      <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <BookOpen className="w-12 h-12 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{t('historyPage.empty.title')}</h3>
      <p className="text-sm text-gray-500">{t('historyPage.empty.description')}</p>
    </div>
  );

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 max-w-6xl">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p>{t('historyPage.loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6 max-w-6xl">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-red-500 mb-4">{t('historyPage.error')}</p>
            <Button onClick={() => refetch()}>{t('historyPage.retry')}</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="md:mt-12 mt-8">
        <div className="flex flex-col md:flex-row justify-between">
          <div className="text-xl md:text-2xl lg:text-3xl flex flex-row group w-[80%]">
            {t('mainContent.history')} ({totalCount})
          </div>
        </div>
      </div>
      <div className="border-[.5px] my-8"></div>

      {/* Search and Filter Controls */}
      <div className="my-8 px-4 sm:px-0">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder={t('historyPage.search.placeholder')}
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {/* <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder={t('historyPage.filters.all')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('historyPage.filters.all')}</SelectItem>
              <SelectItem value="article">{t('historyPage.filters.article')}</SelectItem>
              <SelectItem value="exam">{t('historyPage.filters.exam')}</SelectItem>
              <SelectItem value="practice">{t('historyPage.filters.practice')}</SelectItem>
              <SelectItem value="other">{t('historyPage.filters.other')}</SelectItem>
            </SelectContent>
          </Select> */}
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content Grid */}
      <ContentGrid
        items={filteredItems}
        actions={actions}
        variant={viewMode}
        columns={3}
        showActions={true}
        showMetadata={false}
        emptyState={<EmptyState />}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between py-6">
          <p className="text-sm text-gray-500">
            {t('historyPage.pagination.total')
              .replace('{{total}}', totalCount.toString())
              .replace('{{current}}', currentPage.toString())
              .replace('{{pages}}', totalPages.toString())}
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              {t('historyPage.pagination.previous')}
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              {t('historyPage.pagination.next')}
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </>
  );
}

'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link, Users, DollarSign } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';

const faqItems = [
  {
    value: 'item-1',
    questionKey: 'invite.faq.questions.howToBecome',
    answerKey: 'invite.faq.questions.howToBecomeAnswer'
  },
  {
    value: 'item-2',
    questionKey: 'invite.faq.questions.commissionReceive',
    answerKey: 'invite.faq.questions.commissionReceiveAnswer'
  },
  {
    value: 'item-3',
    questionKey: 'invite.faq.questions.payoutLimit',
    answerKey: 'invite.faq.questions.payoutLimitAnswer'
  },
  {
    value: 'item-4',
    questionKey: 'invite.faq.questions.currency',
    answerKey: 'invite.faq.questions.currencyAnswer'
  },
  {
    value: 'item-5',
    questionKey: 'invite.faq.questions.checkPerformance',
    answerKey: 'invite.faq.questions.checkPerformanceAnswer'
  },
  {
    value: 'item-6',
    questionKey: 'invite.faq.questions.receivePayments',
    answerKey: 'invite.faq.questions.receivePaymentsAnswer'
  },
  {
    value: 'item-7',
    questionKey: 'invite.faq.questions.socialMediaPromotion',
    answerKey: 'invite.faq.questions.socialMediaPromotionAnswer'
  },
  {
    value: 'item-8',
    questionKey: 'invite.faq.questions.needHelp',
    answerKey: 'invite.faq.questions.needHelpAnswer'
  }
];

export default function InviteAndEarnPage() {
  const { t } = useTranslation();

  return (
    <>
      <div className="container mx-auto max-w-4xl py-8 px-4">
        <header className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
            {t('invite.title')}
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
            {t('invite.subtitle')}
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <Button size="lg">{t('invite.earnWithTutoro')}</Button>
            <Button size="lg" variant="outline">
              {t('invite.viewDashboard')}
            </Button>
          </div>
          <div className="mt-8 text-center">
            <a href="#" className="text-primary hover:underline text-sm font-medium">
              {t('invite.tiktokLink')}
            </a>
          </div>
        </header>

        <section className="grid md:grid-cols-3 gap-8 mb-16">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('invite.stats.referralLink')}</CardTitle>
              <Link className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{t('invite.stats.yourLink')}</div>
              <p className="text-xs text-muted-foreground">{t('invite.stats.shareLink')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('invite.stats.referralTracking')}</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+1,234</div>
              <p className="text-xs text-muted-foreground">{t('invite.stats.successfulReferrals')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('invite.stats.earnedCommission')}</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$5,890.00</div>
              <p className="text-xs text-muted-foreground">{t('invite.stats.pendingCommission')}</p>
            </CardContent>
          </Card>
        </section>

        <section>
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">{t('invite.faq.title')}</h2>
            <p className="mt-2 text-md text-gray-600 dark:text-gray-400">
              {t('invite.faq.contactUs')}{' '}
              <a href="#" className="font-medium text-primary hover:underline">
                {t('invite.faq.contactLink')}
              </a>
              。
            </p>
          </div>
          <Accordion type="single" collapsible className="w-full">
            {faqItems.map((item) => (
              <AccordionItem value={item.value} key={item.value}>
                <AccordionTrigger className="text-base">{t(item.questionKey)}</AccordionTrigger>
                <AccordionContent className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
                  {t(item.answerKey)}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </section>
      </div>
    </>
  );
}

/*
 * @Date: 2025-06-02 19:12:52
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-20 20:54:49
 * @FilePath: /tutoro-ai-front/app/[locale]/page.tsx
 */
'use client';

import { useState, useCallback, useRef, useEffect, Suspense } from 'react';
import { MainContent } from '@/components/main-content';
import { LearningHub } from '@/components/learning-hub';
import { SpaceGrid } from '@/components/space-grid';
import { SubscriptionSuccessModal } from '@/components/subscription-success-modal';
import { useRouter, useSearchParams } from 'next/navigation';
import { useMySpaces, useHistoryRecords, useSpaceActions } from '@/lib/stores';
import { useCreateSpace } from '@/lib/api/hooks';
import { useTranslation } from '@/lib/translation-context';
import { useHybridIsAuthenticated } from '@/lib/stores/hybrid-auth-store';
import { toast } from 'sonner';
import { AsyncExecutionGuard } from '@/lib/utils/debounce';
import { useAppDataStore } from '@/lib/stores/app-data-store';
import { saveReferralData, clearReferralData } from '@/lib/utils/referral-storage';
import { postStripeCreatePortalSession } from '@/servers/api/zhifu';
import { Loader2 } from 'lucide-react';

function IndexPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useTranslation();
  const isAuthenticated = useHybridIsAuthenticated();

  // 状态管理
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [sessionId, setSessionId] = useState('');

  // 从统一的应用数据store获取用户空间和历史记录
  const mySpaces = useMySpaces();
  const historyRecords = useHistoryRecords();
  const { removeSpace } = useSpaceActions();
  const { addSpaceToStore } = useAppDataStore();

  // 创建空间的mutation hook
  const createSpaceMutation = useCreateSpace({
    onSuccess: (response) => {
      if (response?.data?.id) {
        // 直接添加到本地store，不再调用API
        addSpaceToStore({
          id: response.data.id.toString(),
          title: response.data.title || '',
          description: response.data.description || ''
        });
      } else {
        toast.error('Failed to create space');
      }
    },
    onError: (error: any) => {
      console.error('Failed to create space:', error);
      toast.error(error?.message || 'Failed to create space');
    }
  });

  // 处理 URL 参数
  useEffect(() => {
    const recommender = searchParams.get('recommender');
    const sessionIdParam = searchParams.get('session_id');

    // 处理邀请人参数
    if (recommender) {
      console.log('Recommender found in URL:', recommender);
      saveReferralData(recommender);

      // 从 URL 中移除 recommender 参数
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('recommender');
      const newUrl = `${window.location.pathname}${newSearchParams.toString() ? '?' + newSearchParams.toString() : ''}`;
      router.replace(newUrl);
    }

    // 处理支付回调参数
    if (sessionIdParam) {
      console.log('Session ID found in URL:', sessionIdParam);
      setSessionId(sessionIdParam);
      setIsProcessingPayment(true);

      // 处理支付成功回调
      handlePaymentCallback(sessionIdParam);

      // 从 URL 中移除 session_id 参数
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('session_id');
      const newUrl = `${window.location.pathname}${newSearchParams.toString() ? '?' + newSearchParams.toString() : ''}`;
      router.replace(newUrl);
    }
  }, [searchParams, router]);

  // 处理支付成功回调
  const handlePaymentCallback = async (sessionId: string) => {
    try {
      setIsProcessingPayment(true);

      // 调用 postStripeCreatePortalSession 接口验证支付
      const response = await postStripeCreatePortalSession({ sessionId });

      if (response.code === '0' || response.data) {
        // 支付成功，显示成功弹窗
        setShowSuccessModal(true);
        // toast.success(t('upgradeModal.success.title'));
      } else {
        throw new Error(response.msg || 'Payment verification failed');
      }
    } catch (error) {
      console.error('Payment callback error:', error);
      toast.error('Payment verification failed. Please contact support.');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  // 使用 zustand store 中的空间数据
  const displaySpaces = mySpaces.map((space) => ({
    id: String(space.id || ''),
    name: space.title || '',
    createdAt: space.createAt || new Date().toISOString()
  }));

  // 空间操作处理函数
  const handleDeleteSpace = (spaceId: string) => {
    removeSpace(spaceId);
    console.log('Space deleted:', spaceId);
    toast.success(t('spacePage.deleteSpaceSuccess'));
  };

  // 通用的创建空间方法
  const createSpaceInternal = useCallback(
    async (title: string, description: string = '', shouldNavigate: boolean = false): Promise<number | null> => {
      const guardKey = shouldNavigate ? 'create-space-homepage' : `create-space-${Date.now()}`;

      const result = await AsyncExecutionGuard.guard(guardKey, async () => {
        const response = await createSpaceMutation.mutateAsync({
          title,
          description
        });

        if (response?.data?.id) {
          if (shouldNavigate) {
            // 跳转到新创建的空间页面
            router.push(`/space/${response.data.id}`);
          }
          return response.data.id;
        }
        return null;
      });

      if (result === null) {
        console.log('Create space already in progress, skipping...');
        return null;
      }

      return result;
    },
    [createSpaceMutation, router]
  );

  // 处理创建空间 - 用于SpaceGrid的添加按钮
  const handleAddSpace = useCallback(async () => {
    await createSpaceInternal(
      `${t('spacePage.newSpaceTitle')} ${mySpaces.length + 1}`,
      '',
      true // 需要跳转
    );
  }, [createSpaceInternal, t, mySpaces.length]);

  // 为LearningHub提供的创建空间方法 - 不跳转，返回空间ID
  const createSpaceWithoutNavigation = useCallback(
    async (title: string, description: string = ''): Promise<number | null> => {
      return await createSpaceInternal(title, description, false);
    },
    [createSpaceInternal]
  );

  const handleSpaceClick = (spaceId: string) => {
    router.push(`/space/${spaceId}`);
  };

  // 如果正在处理支付，显示加载状态
  if (isProcessingPayment) {
    return (
      <div className="min-h-screen bg-white dark:bg-neutral-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          {/* <p className="text-lg font-medium">Processing your subscription...</p> */}
          {/* <p className="text-sm text-muted-foreground mt-2">Please wait while we verify your payment.</p> */}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      {/* Learning Hub Section */}
      <LearningHub onCreateSpace={createSpaceWithoutNavigation} />

      <div className="sm:px-10 lg:px-24 xl:px-36 mt-8 w-full bg-white dark:bg-neutral-900">
        {/* My Spaces Section */}
        <div className="container mx-auto px-2 mb-6">
          <SpaceGrid
            spaces={displaySpaces}
            onDelete={handleDeleteSpace}
            onAdd={handleAddSpace}
            onSpaceClick={handleSpaceClick}
          />
        </div>
        {/* Main Content */}
        <MainContent />
      </div>

      {/* 订阅成功弹窗 */}
      <SubscriptionSuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        sessionId={sessionId}
      />
    </div>
  );
}

export default function IndexPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-white dark:bg-neutral-900 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      }
    >
      <IndexPageContent />
    </Suspense>
  );
}

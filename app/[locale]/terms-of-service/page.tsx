'use client';

import type React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useTranslation } from '@/lib/translation-context';
import { Shield, FileText, CreditCard, Copyright, Users, GraduationCap } from 'lucide-react';
import { Header2 } from '@/components/header2';
import appConfig from '@/config/app';

export default function TermsPage() {
  const { t } = useTranslation();

  const sections = [
    {
      id: 'compliance',
      title: String(t('terms.compliance.title')),
      icon: Shield,
      content: [
        {
          subtitle: String(t('terms.compliance.institutionalPolicies.title')),
          text: String(t('terms.compliance.institutionalPolicies.content'))
        },
        {
          subtitle: String(t('terms.compliance.copyrightLaws.title')),
          text: String(t('terms.compliance.copyrightLaws.content'))
        },
        {
          subtitle: String(t('terms.compliance.privacyRespect.title')),
          text: String(t('terms.compliance.privacyRespect.content'))
        }
      ]
    },
    {
      id: 'license',
      title: String(t('terms.license.title')),
      icon: FileText,
      content: [
        {
          text: String(t('terms.license.content'))
        },
        {
          text: String(t('terms.license.restrictions'))
        },
        {
          text: String(t('terms.license.usage'))
        }
      ]
    },
    {
      id: 'fulfillment',
      title: String(t('terms.fulfillment.title')),
      icon: CreditCard,
      content: [
        {
          subtitle: String(t('terms.fulfillment.refund.title')),
          text: String(t('terms.fulfillment.refund.content'))
        },
        {
          subtitle: String(t('terms.fulfillment.delivery.title')),
          text: String(t('terms.fulfillment.delivery.content'))
        },
        {
          subtitle: String(t('terms.fulfillment.return.title')),
          text: String(t('terms.fulfillment.return.content'))
        },
        {
          subtitle: String(t('terms.fulfillment.cancellation.title')),
          text: String(t('terms.fulfillment.cancellation.content'))
        }
      ]
    },
    {
      id: 'copyright',
      title: String(t('terms.copyright.title')),
      icon: Copyright,
      content: [
        {
          text: String(t('terms.copyright.content'))
        },
        {
          subtitle: String(t('terms.copyright.prohibited.title')),
          list: [
            String(t('terms.copyright.prohibited.item1')),
            String(t('terms.copyright.prohibited.item2')),
            String(t('terms.copyright.prohibited.item3')),
            String(t('terms.copyright.prohibited.item4'))
          ]
        },
        {
          subtitle: String(t('terms.copyright.compliance.title')),
          text: String(t('terms.copyright.compliance.content'))
        },
        {
          subtitle: String(t('terms.copyright.violations.title')),
          list: [
            String(t('terms.copyright.violations.item1')),
            String(t('terms.copyright.violations.item2')),
            String(t('terms.copyright.violations.item3')),
            String(t('terms.copyright.violations.item4'))
          ]
        }
      ]
    },
    {
      id: 'collaboration',
      title: String(t('terms.collaboration.title')),
      icon: Users,
      content: [
        {
          text: String(t('terms.collaboration.content'))
        }
      ]
    },
    {
      id: 'honor',
      title: String(t('terms.honor.title')),
      icon: GraduationCap,
      content: [
        {
          text: String(t('terms.honor.content'))
        },
        {
          subtitle: String(t('terms.honor.prohibited.title')),
          list: [
            String(t('terms.honor.prohibited.item1')),
            String(t('terms.honor.prohibited.item2')),
            String(t('terms.honor.prohibited.item3'))
          ]
        },
        {
          subtitle: String(t('terms.honor.violations.title')),
          list: [
            String(t('terms.honor.violations.item1')),
            String(t('terms.honor.violations.item2')),
            String(t('terms.honor.violations.item3'))
          ]
        },
        {
          text: String(t('terms.honor.closing'))
        }
      ]
    }
  ];

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header2 showProgress={false} isOutline={false} />
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">{String(t('terms.title'))}</h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">{String(t('terms.subtitle'))}</p>
            {/* <p className="text-sm text-muted-foreground mt-4">
            {String(t('terms.lastUpdated'))}
          </p> */}
          </div>

          {/* Introduction */}
          <Card className="mb-8">
            <CardContent className="pt-6">
              <p className="text-muted-foreground leading-relaxed">{String(t('terms.introduction'))}</p>
            </CardContent>
          </Card>

          {/* Terms Sections */}
          <div className="space-y-8">
            {sections.map((section, index) => (
              <Card key={section.id} className="overflow-hidden">
                <CardHeader className="bg-muted/50">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <section.icon className="w-6 h-6 text-primary" />
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    {section.content.map((item, itemIndex) => (
                      <div key={itemIndex}>
                        {item.subtitle && <h4 className="font-semibold text-foreground mb-3">{item.subtitle}</h4>}
                        {item.text && <p className="text-muted-foreground leading-relaxed mb-4">{item.text}</p>}
                        {item.list && (
                          <ul className="space-y-2 ml-4">
                            {item.list.map((listItem, listIndex) => (
                              <li key={listIndex} className="flex items-start gap-2 text-muted-foreground">
                                <span className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0" />
                                <span className="leading-relaxed">{listItem}</span>
                              </li>
                            ))}
                          </ul>
                        )}
                        {itemIndex < section.content.length - 1 && item.subtitle && <Separator className="mt-4" />}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Footer */}
          <Card className="mt-12">
            <CardContent className="pt-6 text-center">
              <p className="text-muted-foreground mb-4">{String(t('terms.footer.contact'))}</p>
              <p className="text-sm text-muted-foreground">{String(t('terms.footer.email'))}: {appConfig.email}</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

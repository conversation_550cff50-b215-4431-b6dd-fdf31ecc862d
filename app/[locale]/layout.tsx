/*
 * @Date: 2025-06-02 19:04:27
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-20 21:33:17
 * @FilePath: /tutoro-ai-front/app/[locale]/layout.tsx
 */
import type React from 'react';
import { notFound } from 'next/navigation';
import { getDictionary } from '@/lib/dictionaries';
import { ConditionalLayout } from '@/components/layout/conditional-layout';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { QueryProvider } from '@/lib/api/query-client';
import { HybridAuthProvider } from '@/components/providers/hybrid-auth-provider';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { Toaster } from 'sonner';
import { locales, type Locale } from '@/i18n.config';

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    locale: string;
  }>;
}

// 验证locale是否有效
function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

// Google OAuth Client ID 测试环境
// const GOOGLE_CLIENT_ID = '1052620663094-noc2akc5rai9c1op97i02v5qaqf3d7sr.apps.googleusercontent.com';
// 生产环境
const GOOGLE_CLIENT_ID = '227679091759-hk7fpa60oc42oo1o5see1k9m0fm0vbjp.apps.googleusercontent.com';

// 添加调试信息
export default async function LocaleLayout({ children, params }: LocaleLayoutProps) {
  // 等待并获取params
  const { locale } = await params;

  // 验证locale参数
  if (!isValidLocale(locale)) {
    notFound();
  }

  // 获取字典数据
  const dictionary = await getDictionary(locale);

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      </head>
      <body>
        <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
            <QueryProvider>
              <HybridAuthProvider>
                <ConditionalLayout locale={locale} dictionary={dictionary}>
                  {children}
                </ConditionalLayout>
              </HybridAuthProvider>
              <script
                dangerouslySetInnerHTML={{
                  __html: `
                    // 语言偏好和认证状态初始化脚本
                    (function() {
                      // 确保语言偏好同步
                      const currentLocale = '${locale}';
                      const storedLocale = localStorage.getItem('preferred_locale');
                      
                      if (!storedLocale || storedLocale !== currentLocale) {
                        localStorage.setItem('preferred_locale', currentLocale);
                        // 同步到cookie
                        const maxAge = 365 * 24 * 60 * 60; // 1年
                        const isSecure = window.location.protocol === 'https:';
                        const secureFlag = isSecure ? '; Secure' : '';
                        document.cookie = 'preferred_locale=' + currentLocale + '; path=/; max-age=' + maxAge + '; SameSite=Lax' + secureFlag;
                      }
                      
                      // 拦截fetch请求，添加语言和认证头
                      const originalFetch = window.fetch;
                      window.fetch = function(url, options = {}) {
                        options.headers = options.headers || {};
                        
                        // 添加语言偏好头
                        const preferredLocale = localStorage.getItem('preferred_locale');
                        if (preferredLocale) {
                          options.headers['x-preferred-locale'] = preferredLocale;
                        }
                        
                        // 添加认证头（如果存在token）
                        const authToken = localStorage.getItem('auth_token');
                        if (authToken && !options.headers['Authorization']) {
                          options.headers['Authorization'] = 'Bearer ' + authToken;
                        }
                        
                        return originalFetch(url, options);
                      };
                    })();
                  `
                }}
              />
            </QueryProvider>
          </ThemeProvider>
        </GoogleOAuthProvider>
      </body>
    </html>
  );
}

// 生成静态路径
export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

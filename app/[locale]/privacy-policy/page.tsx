'use client';

import type React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useTranslation } from '@/lib/translation-context';
import {
  Shield,
  Database,
  Settings,
  Scale,
  Share2,
  Clock,
  Lock,
  Baby,
  Eye,
  Cookie,
  Globe,
  FileText,
  Mail,
  AlertTriangle
} from 'lucide-react';
import { Header2 } from '@/components/header2';
import appConfig from '@/config/app';

interface ContentItem {
  subtitle?: string;
  text?: string;
  list?: string[];
}

interface Section {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  content: ContentItem[];
}

export default function PrivacyPage() {
  const { t } = useTranslation();

  const sections: Section[] = [
    {
      id: 'commitment',
      title: String(t('privacy.commitment.title')),
      icon: Shield,
      content: [
        {
          text: String(t('privacy.commitment.content'))
        }
      ]
    },
    {
      id: 'information',
      title: String(t('privacy.information.title')),
      icon: Database,
      content: [
        {
          subtitle: String(t('privacy.information.personal.title')),
          text: String(t('privacy.information.personal.description')),
          list: [
            String(t('privacy.information.personal.name')),
            String(t('privacy.information.personal.email')),
            String(t('privacy.information.personal.username')),
            String(t('privacy.information.personal.payment')),
            String(t('privacy.information.personal.academic'))
          ]
        },
        {
          subtitle: String(t('privacy.information.automatic.title')),
          text: String(t('privacy.information.automatic.description')),
          list: [
            String(t('privacy.information.automatic.device')),
            String(t('privacy.information.automatic.usage')),
            String(t('privacy.information.automatic.cookies'))
          ]
        },
        {
          text: String(t('privacy.information.cookieNote'))
        }
      ]
    },
    {
      id: 'usage',
      title: String(t('privacy.usage.title')),
      icon: Settings,
      content: [
        {
          text: String(t('privacy.usage.description')),
          list: [
            String(t('privacy.usage.provide')),
            String(t('privacy.usage.authenticate')),
            String(t('privacy.usage.process')),
            String(t('privacy.usage.send')),
            String(t('privacy.usage.prevent')),
            String(t('privacy.usage.fulfill'))
          ]
        }
      ]
    },
    {
      id: 'legal',
      title: String(t('privacy.legal.title')),
      icon: Scale,
      content: [
        {
          text: String(t('privacy.legal.description')),
          list: [
            String(t('privacy.legal.consent')),
            String(t('privacy.legal.contractual')),
            String(t('privacy.legal.compliance')),
            String(t('privacy.legal.legitimate'))
          ]
        },
        {
          text: String(t('privacy.legal.withdrawal'))
        }
      ]
    },
    {
      id: 'sharing',
      title: String(t('privacy.sharing.title')),
      icon: Share2,
      content: [
        {
          text: String(t('privacy.sharing.description')),
          list: [
            String(t('privacy.sharing.providers')),
            String(t('privacy.sharing.authorities')),
            String(t('privacy.sharing.acquirers'))
          ]
        },
        {
          text: String(t('privacy.sharing.noSale'))
        }
      ]
    },
    {
      id: 'retention',
      title: String(t('privacy.retention.title')),
      icon: Clock,
      content: [
        {
          text: String(t('privacy.retention.content'))
        }
      ]
    },
    {
      id: 'security',
      title: String(t('privacy.security.title')),
      icon: Lock,
      content: [
        {
          text: String(t('privacy.security.description')),
          list: [
            String(t('privacy.security.encrypted')),
            String(t('privacy.security.access')),
            String(t('privacy.security.audits'))
          ]
        },
        {
          text: String(t('privacy.security.disclaimer'))
        }
      ]
    },
    {
      id: 'children',
      title: String(t('privacy.children.title')),
      icon: Baby,
      content: [
        {
          text: String(t('privacy.children.content'))
        }
      ]
    },
    {
      id: 'rights',
      title: String(t('privacy.rights.title')),
      icon: Eye,
      content: [
        {
          text: String(t('privacy.rights.description')),
          list: [
            String(t('privacy.rights.access')),
            String(t('privacy.rights.object')),
            String(t('privacy.rights.withdraw')),
            String(t('privacy.rights.export'))
          ]
        },
        {
          text: String(t('privacy.rights.requests'))
        }
      ]
    },
    {
      id: 'cookies',
      title: String(t('privacy.cookies.title')),
      icon: Cookie,
      content: [
        {
          text: String(t('privacy.cookies.usage'))
        },
        {
          text: String(t('privacy.cookies.tracking'))
        }
      ]
    },
    {
      id: 'compliance',
      title: String(t('privacy.compliance.title')),
      icon: AlertTriangle,
      content: [
        {
          text: String(t('privacy.compliance.description')),
          list: [
            String(t('privacy.compliance.copyright')),
            String(t('privacy.compliance.faculty')),
            String(t('privacy.compliance.transcripts'))
          ]
        },
        {
          text: String(t('privacy.compliance.aiGenerated'))
        }
      ]
    },
    {
      id: 'international',
      title: String(t('privacy.international.title')),
      icon: Globe,
      content: [
        {
          text: String(t('privacy.international.content'))
        }
      ]
    },
    {
      id: 'updates',
      title: String(t('privacy.updates.title')),
      icon: FileText,
      content: [
        {
          text: String(t('privacy.updates.content'))
        }
      ]
    }
  ];

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header2 showProgress={false} isOutline={false} />
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">{String(t('privacy.title'))}</h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">{String(t('privacy.subtitle'))}</p>
            {/* <p className="text-sm text-muted-foreground mt-4">
            {String(t('privacy.lastUpdated'))}
          </p> */}
          </div>

          {/* Privacy Sections */}
          <div className="space-y-8">
            {sections.map((section, index) => (
              <Card key={section.id} className="overflow-hidden">
                <CardHeader className="bg-muted/50">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <section.icon className="w-6 h-6 text-primary" />
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    {section.content.map((item, itemIndex) => (
                      <div key={itemIndex}>
                        {item.subtitle && <h4 className="font-semibold text-foreground mb-3">{item.subtitle}</h4>}
                        {item.text && <p className="text-muted-foreground leading-relaxed mb-4">{item.text}</p>}
                        {item.list && (
                          <ul className="space-y-2 ml-4">
                            {item.list.map((listItem, listIndex) => (
                              <li key={listIndex} className="flex items-start gap-2 text-muted-foreground">
                                <span className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0" />
                                <span className="leading-relaxed">{listItem}</span>
                              </li>
                            ))}
                          </ul>
                        )}
                        {itemIndex < section.content.length - 1 && item.subtitle && <Separator className="mt-4" />}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Footer */}
          <Card className="mt-12">
            <CardContent className="pt-6 text-center">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Mail className="w-4 h-4 text-primary" />
                <p className="text-muted-foreground">{String(t('privacy.footer.contact'))}</p>
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                {String(t('privacy.footer.email'))}: {appConfig.email}
              </p>
              <p className="text-xs text-muted-foreground">
                {String(t('privacy.footer.integration'))}{' '}
                <a href="/terms-of-service" className="text-primary hover:underline">
                  {String(t('privacy.footer.terms'))}
                </a>
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

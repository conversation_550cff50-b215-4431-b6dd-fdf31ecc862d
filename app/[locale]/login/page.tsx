'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Mail, Timer } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useHybridAuth, useHybridIsAuthenticated } from '@/lib/stores/hybrid-auth-store';
import { useTranslation } from '@/lib/translation-context';
import appConfig from '@/config/app';
import Image from 'next/image';
import { toast } from 'sonner';
import { useTheme } from 'next-themes';
import { isDarkMode } from '@/lib/utils/theme';
import { GoogleLoginButton } from '@/components/auth/google-login-button';
import { getReferralData } from '@/lib/utils/referral-storage';

export default function AuthPage() {
  const [email, setEmail] = useState('');
  const [captcha, setCaptcha] = useState('');
  const [isCapchaSent, setIsCapchaSent] = useState(false);
  const [captchaCountdown, setCaptchaCountdown] = useState(0);
  const [isCapchaLoading, setIsCapchaLoading] = useState(false);
  const [isGoogleAuthLoading, setIsGoogleAuthLoading] = useState(false);
  const [isEmailLoginLoading, setIsEmailLoginLoading] = useState(false);
  const [errors, setErrors] = useState<{
    email?: string;
    captcha?: string;
    general?: string;
  }>({});

  const router = useRouter();
  const { loginWithCaptcha, sendCaptcha, loginWithGoogle } = useHybridAuth();
  const isAuthenticated = useHybridIsAuthenticated();
  const { t, locale } = useTranslation();
  const { theme } = useTheme();



  // Google登录成功处理
  const handleGoogleSuccess = async (response: any) => {
    try {
      setIsGoogleAuthLoading(true);

      // 获取URL参数中的推荐人和重定向信息
      const searchParams = new URLSearchParams(window.location.search);
      const recommender = getReferralData() || undefined;

      console.log('Google login response:', response);

      // 处理不同类型的响应
      const credential = response.credential || response.access_token;
      if (!credential) {
        throw new Error('No credential received from Google');
      }

      // 使用获取到的credential进行登录
      await loginWithGoogle({
        idToken: credential,
        recommender
      });

      // 登录成功后处理重定向
      const redirectTo = searchParams.get('redirect');
      if (redirectTo && redirectTo !== '/login') {
        router.push(redirectTo);
      } else {
        router.push('/');
      }
    } catch (error: any) {
      console.error('Google login error:', error);
      toast.error(error.message || t('auth.errors.google.default') || 'Google login failed');
    } finally {
      setIsGoogleAuthLoading(false);
    }
  };

  // Google登录开始处理
  const handleGoogleStart = () => {
    setIsGoogleAuthLoading(true);
  };

  // Google登录错误处理
  const handleGoogleError = (error?: any) => {
    setIsGoogleAuthLoading(false);
  };

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      const searchParams = new URLSearchParams(window.location.search);
      const redirectTo = searchParams.get('redirect');

      if (redirectTo && redirectTo !== '/login') {
        router.push(redirectTo);
      } else {
        router.push('/');
      }
    }
  }, [isAuthenticated, router]);

  // 验证码倒计时
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (captchaCountdown > 0) {
      interval = setInterval(() => {
        setCaptchaCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [captchaCountdown]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateForm = () => {
    const newErrors: {
      email?: string;
      captcha?: string;
    } = {};

    if (!email) {
      newErrors.email = t('auth.errors.email.required') || 'Email is required';
    } else if (!validateEmail(email)) {
      newErrors.email = t('auth.errors.email.invalid') || 'Invalid email format';
    }

    if (!captcha) {
      newErrors.captcha = t('auth.errors.captcha.required') || 'Verification code is required';
    } else if (captcha.length < 4) {
      newErrors.captcha = t('auth.errors.captcha.minLength') || 'Verification code must be at least 4 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSendCaptcha = async () => {
    if (!email) {
      setErrors({ email: t('auth.errors.email.required') || 'Email is required' });
      return;
    }

    if (!validateEmail(email)) {
      setErrors({ email: t('auth.errors.email.invalid') || 'Invalid email format' });
      return;
    }

    try {
      setIsCapchaLoading(true);
      setErrors({});

      await sendCaptcha(email);

      setIsCapchaSent(true);
      setCaptchaCountdown(60); // 60秒倒计时
      toast.success(t('auth.captcha.sent') || 'Verification code sent successfully');
    } catch (error: any) {
      setErrors({
        general:
          error?.response?.data?.msg ||
          error?.message ||
          t('auth.errors.captcha.sendFailed') ||
          'Failed to send verification code'
      });
    } finally {
      setIsCapchaLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setErrors({});

    try {
      setIsEmailLoginLoading(true);
      const recommender = getReferralData() || undefined;
      await loginWithCaptcha({
        email,
        captcha,
        recommender
      });

      // 登录成功后处理重定向
      const searchParams = new URLSearchParams(window.location.search);
      const redirectTo = searchParams.get('redirect');

      if (redirectTo && redirectTo !== '/login') {
        router.push(redirectTo);
      } else {
        router.push('/');
      }
    } catch (error: any) {
      setErrors({
        general: error?.response?.data?.msg || error?.message || t('auth.errors.login') || 'Login failed'
      });
    } finally {
      setIsEmailLoginLoading(false);
    }
  };

  return (
    <div className="min-h-screen from-blue-50 via-white to-purple-50 dark:from-neutral-950 dark:via-neutral-900 dark:to-neutral-950 flex">
      {/* Right Side - Auth Form */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <Card className="border-0 shadow-none bg-transparent">
            <CardHeader className="text-center space-y-2 pb-6">
              <div className="flex items-center justify-center mb-4">
                <Image
                  src={isDarkMode(theme) ? appConfig.logo2_dark : appConfig.logo2}
                  alt={appConfig.name}
                  width={551 / 4}
                  height={424 / 4}
                />
              </div>
              <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                {t('auth.login.title') || 'Welcome Back'}
              </CardTitle>
              <CardDescription className="text-base text-gray-600 dark:text-gray-300">
                {t('auth.login.subtitle') || 'Sign in to your account with email verification'}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Google Auth */}
              <div className="flex justify-center">
                <GoogleLoginButton
                  onSuccess={handleGoogleSuccess}
                  onError={handleGoogleError}
                  isLoading={isGoogleAuthLoading}
                  variant="custom"
                />
              </div>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-gray-200 dark:border-neutral-700" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white dark:bg-transparent px-3 text-muted-foreground font-medium">
                    {t('auth.login.or') || 'Or'}
                  </span>
                </div>
              </div>

              {/* Auth Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                {errors.general && (
                  <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <p className="text-sm text-red-700 dark:text-red-300">{errors.general}</p>
                  </div>
                )}

                {/* Email Input */}
                <div className="space-y-2">
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="email"
                      type="email"
                      placeholder={t('auth.login.email') || 'Enter your email address'}
                      value={email}
                      onChange={(e) => {
                        setEmail(e.target.value);
                        if (errors.email) setErrors((prev) => ({ ...prev, email: undefined }));
                      }}
                      className={`flex h-12 pl-10 pr-4 w-full rounded-lg border bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-md transition-all duration-200 ${
                        errors.email ? 'border-red-500 focus-visible:ring-red-500' : 'border-primary/10'
                      }`}
                      disabled={isEmailLoginLoading}
                      autoComplete="email"
                    />
                  </div>
                  {errors.email && <p className="text-sm text-red-600 dark:text-red-400">{errors.email}</p>}
                </div>

                {/* Captcha Input */}
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Input
                        id="captcha"
                        type="text"
                        placeholder={t('auth.login.captcha') || 'Enter verification code'}
                        value={captcha}
                        onChange={(e) => {
                          setCaptcha(e.target.value);
                          if (errors.captcha) setErrors((prev) => ({ ...prev, captcha: undefined }));
                        }}
                        className={`flex h-12 p-4 w-full rounded-lg border bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-md transition-all duration-200 ${
                          errors.captcha ? 'border-red-500 focus-visible:ring-red-500' : 'border-primary/10'
                        }`}
                        disabled={isEmailLoginLoading}
                        maxLength={6}
                        autoComplete="one-time-code"
                      />
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      className="h-12 px-4 whitespace-nowrap min-w-[120px]"
                      onClick={handleSendCaptcha}
                      disabled={isCapchaLoading || captchaCountdown > 0 || !email || isEmailLoginLoading}
                    >
                      {isCapchaLoading ? (
                        <span className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                          {t('auth.captcha.sending') || 'Sending...'}
                        </span>
                      ) : captchaCountdown > 0 ? (
                        <span className="flex items-center">
                          <Timer className="h-4 w-4 mr-1" />
                          {captchaCountdown}s
                        </span>
                      ) : (
                        t('auth.captcha.send') || 'Send Code'
                      )}
                    </Button>
                  </div>
                  {errors.captcha && <p className="text-sm text-red-600 dark:text-red-400">{errors.captcha}</p>}
                  {isCapchaSent && !errors.captcha && (
                    <p className="text-sm text-green-600 dark:text-green-400">
                      {t('auth.captcha.sentSuccess') || 'Verification code sent to your email'}
                    </p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-gray-800 hover:bg-gray-900 dark:bg-gray-200 dark:hover:bg-gray-100 text-white dark:text-black font-medium transition-all duration-200 rounded-lg"
                  disabled={!email || !captcha || isEmailLoginLoading}
                >
                  {isEmailLoginLoading ? (
                    <span className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                      {t('auth.login.logging') || 'Signing In...'}
                    </span>
                  ) : (
                    t('auth.login.submit') || 'Sign In / Sign Up'
                  )}
                </Button>
              </form>
            </CardContent>

            {/* Privacy and Terms Links */}
            <div className="px-6 pb-4 flex justify-center gap-4">
              <Link href="/privacy-policy">
                <Button
                  variant="link"
                  className="p-0 h-auto text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                >
                  {t('sidebar.privacy') || 'Privacy Policy'}
                </Button>
              </Link>
              <span className="text-xs text-gray-400">•</span>
              <Link href="/terms-of-service">
                <Button
                  variant="link"
                  className="p-0 h-auto text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                >
                  {t('sidebar.terms') || 'Terms of Service'}
                </Button>
              </Link>
            </div>

            <CardFooter className="text-center pt-6">
              <p className="text-sm text-muted-foreground w-full">
                {t('auth.login.captchaInfo') ||
                  'By continuing, you will either sign in to your existing account or create a new one automatically.'}
              </p>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams } from 'next/navigation';
// import { ArticleContent } from '@/components/article/article-content';
import { RightSidebar } from '@/components/article/right-sidebar';
import { MessageCircle, Layers, HelpCircle, BookOpen, FileText, Eye, EyeOff } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { useGetContentDetail, useCacheInvalidation } from '@/lib/api/hooks';
import { toast } from 'sonner';
import { useAppStore } from '@/lib/stores/app-store';
import { useArticleCacheStore } from '@/lib/stores/article-cache-store';
import dynamic from 'next/dynamic';
import { ArticleContent } from '@/components/article/article-content';

// const ArticleContent = dynamic(() => import('@/components/article/article-content'), {
//   ssr: false,
//   loading: () => (
//     <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
//       {/* <div className="text-center">
//         <p className="text-lg mb-2">Loading PDF viewer...123123123</p>
//         <p className="text-sm">Pleas12321s</p>
//       </div> */}
//     </div>
//   )
// });
// 内容数据接口
interface ContentData {
  id?: number;
  title?: string;
  fileUrl?: string;
  spaceId?: number;
  createAt?: string;
  updateAt?: string;
}

export default function ArticlePage() {
  const params = useParams();
  const contentId = params?.id ? Number(params.id) : 0;
  const [activeTab, setActiveTab] = useState<string>('chat');
  const [showArticleOnMobile, setShowArticleOnMobile] = useState<boolean>(false);
  const [showArticleOnDesktop, setShowArticleOnDesktop] = useState<boolean>(true);

  const { t } = useTranslation();

  // 使用 app store 设置标题 - 分别选择函数避免无限循环
  const setArticleTitle = useAppStore((state) => state.setArticleTitle);
  const clearArticleTitle = useAppStore((state) => state.clearArticleTitle);

  // 使用 article-cache-store 管理数据
  const {
    getNewArticleChat,
    setContentData,
    setNewArticleChatData,
    clearAutoSendChatData
  } = useArticleCacheStore();

  // 使用缓存失效机制
  const { invalidateAllContent } = useCacheInvalidation();

  // 使用 hook 获取内容数据
  const {
    data: contentResponse,
    isLoading: isLoadingContent,
    error: contentError,
    isError: hasContentError,
    refetch: refetchContent
  } = useGetContentDetail(contentId);

  // 获取内容数据 - 让 React Query 管理数据刷新，不完全依赖缓存
  const contentData = useMemo(() => {
    // 优先使用 React Query 返回的数据，确保数据的新鲜度
    return contentResponse?.data || null;
  }, [contentResponse?.data]);

  // 当API数据返回时，缓存到store（用于跨页面状态管理）
  useEffect(() => {
    if (contentResponse?.data) {
      console.log('✅ ArticlePage: Caching content data for contentId:', contentId);
      setContentData(contentId, contentResponse.data);
      
      // 检查是否为视频类型且没有fileUrl，自动触发视频描述
      if (contentResponse.data.type === 'video' && !contentResponse.data.fileUrl) {
        console.log('🎥 Video content detected without fileUrl, triggering auto description');
        // 自动切换到聊天标签
        setActiveTab('chat');
        // 延迟设置自动发送数据，确保 ChatComponent 已经渲染
        setTimeout(() => {
          setNewArticleChatData(contentId, contentResponse.data);
        }, 100);
      }

      // 检查是否为音频类型且没有fileUrl，自动触发音频描述
      if (contentResponse.data.type === 'audio' && !contentResponse.data.fileUrl) {
        console.log('🎵 Audio content detected without fileUrl, triggering auto description');
        // 自动切换到聊天标签
        setActiveTab('chat');
        // 延迟设置自动发送数据，确保 ChatComponent 已经渲染
        setTimeout(() => {
          setNewArticleChatData(contentId, contentResponse.data);
        }, 100);
      }
    }
  }, [contentResponse?.data, contentId, setContentData]);

  // 计算derived state
  const articleTitle = contentData?.title || t('article.defaultTitle');
  const isPdf = useMemo(() => {
    return !!(contentData?.fileUrl && contentData.fileUrl.trim() !== '');
  }, [contentData?.fileUrl]);

  // 获取当前的自动发送数据 - 使用订阅方式
  const currentAutoSendData = useArticleCacheStore((state) => {
    const autoSendData = state.autoSendChatData[contentId];
    if (autoSendData) {
      return {
        initialInputValue: autoSendData.initialInputValue,
        initialSelectedModelId: autoSendData.initialSelectedModelId,
        autoSend: autoSendData.autoSend
      };
    }
    return null;
  });

  // 内容刷新回调
  const handleContentRefresh = useCallback(async () => {
    console.log('🔄 Refreshing content data...');
    try {
      // 使用 React Query 的缓存失效机制，确保获取最新数据
      invalidateAllContent();

      // 重新获取数据
      const result = await refetchContent();
      if (result.data?.data) {
        setContentData(contentId, result.data.data);
        console.log('✅ Content data refreshed successfully');
        toast.success('内容数据已刷新');
      }
    } catch (error) {
      console.error('❌ Failed to refresh content data:', error);
      toast.error('刷新内容数据失败');
    }
  }, [contentId, refetchContent, setContentData, invalidateAllContent]);

  // 当文章标题获取到或改变时，设置到 store 中
  useEffect(() => {
    if (articleTitle) {
      setArticleTitle(articleTitle);
    }

    // 清理函数：组件卸载时清除标题
    return () => {
      clearArticleTitle();
    };
  }, [articleTitle, setArticleTitle, clearArticleTitle]);

  // 检查 newArticleChat 数据
  useEffect(() => {
    if (!contentId) return;

    const chatData = getNewArticleChat(contentId);

    if (chatData?.modelId && chatData?.aiQuery) {
      console.log('🔍 Setting up new article chat:', {
        contentId,
        aiQuery: chatData.aiQuery,
        modelId: chatData.modelId
      });

      // 使用新的统一方法，传入 doc 类型的配置
      // 对于新创建的内容，即使 contentData 还没完全加载，也要设置自动发送数据
      const fallbackContentData = contentData || { type: 'doc' as const };
      console.log('📝 Setting auto-send data:', {
        contentId,
        fallbackContentData,
        options: {
          initialInputValue: chatData.aiQuery,
          initialSelectedModelId: chatData.modelId,
          autoSend: true
        }
      });

      // 延迟设置，确保 ChatComponent 已经渲染并订阅了 store
      setTimeout(() => {
        setNewArticleChatData(contentId, fallbackContentData, {
          initialInputValue: chatData.aiQuery,
          initialSelectedModelId: chatData.modelId,
          autoSend: true
        });
      }, 50);

      // 自动切换到聊天标签
      setActiveTab('chat');
    } else {
      // 清理状态
      clearAutoSendChatData(contentId);
    }
  }, [contentId, contentData, getNewArticleChat, setNewArticleChatData, clearAutoSendChatData]);

  // 处理自动发送完成
  const handleAutoSendComplete = useCallback(() => {
    if (contentId) {
      // 清除对应的 newArticleChat 数据
      const state = useArticleCacheStore.getState();
      const newChatData = { ...state.newArticleChat };
      delete newChatData[contentId];
      useArticleCacheStore.setState({
        newArticleChat: newChatData
      });

      // 清除自动发送数据
      clearAutoSendChatData(contentId);
    }
  }, [contentId, clearAutoSendChatData]);

  // 处理标签页切换
  const handleTabChange = useCallback((tabId: string) => {
    setActiveTab(tabId);
  }, []);

  // 处理标题变更
  const handleTitleChange = useCallback(
    (newTitle: string) => {
      // TODO: 实现标题更新 API 调用
      console.log('Title change requested:', newTitle);
      toast.info(t('article.titleUpdateNotSupported'));
    },
    [t]
  );

  // 如果内容加载失败则显示错误提示
  useEffect(() => {
    if (hasContentError) {
      console.error('Failed to load content:', contentError);
      toast.error(t('article.loadError'));
    }
  }, [hasContentError, contentError, t]);

  // 优化tabs配置
  const tabs = useMemo(
    () => [
      { id: 'chat', label: t('article.tabs.chat'), icon: MessageCircle },
      { id: 'flashcards', label: t('article.tabs.flashcards'), icon: Layers },
      { id: 'quiz', label: t('article.tabs.quiz'), icon: HelpCircle },
      { id: 'summary', label: t('article.tabs.summary'), icon: FileText },
      { id: 'chapters', label: t('article.tabs.chapters'), icon: BookOpen }
    ],
    [t]
  );

  // 如果内容 ID 无效则显示加载状态
  if (!contentId || contentId <= 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">{t('article.invalidId')}</h1>
          <p className="text-gray-500">{t('article.invalidIdDesc')}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* 移动端布局 (屏幕 < md) */}
      <div className="md:hidden flex flex-col h-[calc(100vh-57px)] w-full">
        {/* 文章内容 - 条件渲染 */}
        {isPdf && showArticleOnMobile && (
          <div className="flex-shrink-0 basis-1/2 overflow-hidden">
            <ArticleContent
              title={articleTitle}
              pdfUrl={contentData?.fileUrl || ''}
              onTitleChange={handleTitleChange}
            />
          </div>
        )}
        {/* 切换按钮 - 位于文章和工具之间 - 只在有有效PDF时显示 */}
        {isPdf && (
          <div className="p-2 border-y dark:border-neutral-700 bg-gray-50 dark:bg-neutral-800/50 flex-shrink-0">
            <button
              onClick={() => setShowArticleOnMobile(!showArticleOnMobile)}
              className="w-full flex items-center justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-neutral-700 hover:bg-gray-50 dark:hover:bg-neutral-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {showArticleOnMobile ? <EyeOff className="mr-2 h-4 w-4" /> : <Eye className="mr-2 h-4 w-4" />}
              {showArticleOnMobile ? t('article.hideArticle') : t('article.showArticle')}
            </button>
          </div>
        )}
        {/* 右侧边栏（工具）- 占用剩余空间 */}
        <div className="flex-grow overflow-hidden">
          <RightSidebar
            tabs={tabs}
            activeTab={activeTab}
            setActiveTab={handleTabChange}
            contentId={contentId}
            newArticleChatData={currentAutoSendData}
            onAutoSendComplete={handleAutoSendComplete}
            hasValidPdf={isPdf}
            contentData={contentData}
            onContentRefresh={handleContentRefresh}
          />
        </div>
      </div>

      {/* 桌面端分屏视图 (屏幕 >= md) */}
      <div className="hidden md:flex h-[calc(100vh-56px)] lg:h-[calc(100vh-72px)] w-full overflow-hidden lg:gap-6 gap-4 py-2">
        {/* 左侧文章内容 - 根据状态控制显示/隐藏 - 只在有有效PDF时显示 */}
        {isPdf && (
          <div
            className={`${
              showArticleOnDesktop ? 'w-1/2' : 'w-0'
            } flex flex-col h-full  transition-all duration-300 ease-in-out overflow-hidden`}
          >
            {showArticleOnDesktop && (
              <ArticleContent
                title={articleTitle}
                pdfUrl={contentData?.fileUrl || ''}
                onTitleChange={handleTitleChange}
              />
            )}
          </div>
        )}

        {/* 右侧边栏 - 根据左侧状态调整宽度 */}
        <div
          className={`${
            isPdf && showArticleOnDesktop ? 'w-1/2' : 'w-full'
          } flex flex-col h-full transition-all duration-300 ease-in-out`}
        >
          <RightSidebar
            tabs={tabs}
            activeTab={activeTab}
            setActiveTab={handleTabChange}
            contentId={contentId}
            showArticleOnDesktop={showArticleOnDesktop}
            onToggleArticle={isPdf ? () => setShowArticleOnDesktop(!showArticleOnDesktop) : undefined}
            newArticleChatData={currentAutoSendData}
            onAutoSendComplete={handleAutoSendComplete}
            hasValidPdf={isPdf}
            contentData={contentData}
            onContentRefresh={handleContentRefresh}
          />
        </div>
      </div>
    </>
  );
}

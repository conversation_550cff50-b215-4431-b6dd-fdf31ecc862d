'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, EyeOff, ArrowLeft, Mail, Shield, Key } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useHybridIsAuthenticated } from '@/lib/stores/hybrid-auth-store';
import { useTranslation } from '@/lib/translation-context';
import appConfig from '@/config/app';
import Image from 'next/image';
import { toast } from 'sonner';
import { useTheme } from 'next-themes';
import { isDarkMode } from '@/lib/utils/theme';

// 步骤枚举
enum ForgotPasswordStep {
  EMAIL = 'email',
  CODE = 'code',
  PASSWORD = 'password'
}

export default function ForgotPasswordPage() {
  const [currentStep, setCurrentStep] = useState<ForgotPasswordStep>(ForgotPasswordStep.EMAIL);
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [errors, setErrors] = useState<{
    email?: string;
    verificationCode?: string;
    newPassword?: string;
    confirmPassword?: string;
    general?: string;
  }>({});

  const router = useRouter();
  const isAuthenticated = useHybridIsAuthenticated();
  const { t } = useTranslation();
  const { theme, setTheme } = useTheme();

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/');
    }
  }, [isAuthenticated, router]);

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateForm = () => {
    const newErrors: {
      email?: string;
      verificationCode?: string;
      newPassword?: string;
      confirmPassword?: string;
    } = {};

    if (currentStep === ForgotPasswordStep.EMAIL) {
      if (!email) {
        newErrors.email = t('auth.errors.email.required');
      } else if (!validateEmail(email)) {
        newErrors.email = t('auth.errors.email.invalid');
      }
    } else if (currentStep === ForgotPasswordStep.CODE) {
      if (!verificationCode) {
        newErrors.verificationCode = t('auth.errors.verificationCode.required');
      } else if (verificationCode.length !== 6) {
        newErrors.verificationCode = t('auth.errors.verificationCode.invalid');
      }
    } else if (currentStep === ForgotPasswordStep.PASSWORD) {
      if (!newPassword) {
        newErrors.newPassword = t('auth.errors.password.required');
      } else if (newPassword.length < 6) {
        newErrors.newPassword = t('auth.errors.password.minLength');
      }

      if (!confirmPassword) {
        newErrors.confirmPassword = t('auth.errors.confirmPassword.required');
      } else if (newPassword !== confirmPassword) {
        newErrors.confirmPassword = t('auth.errors.confirmPassword.mismatch');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSendCode = async () => {
    if (!validateForm()) {
      return;
    }

    setErrors({});
    setIsLoading(true);

    try {
      // 模拟发送验证码 API 调用
      // 在实际应用中，这里应该调用真实的API
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast.success(t('auth.forgotPassword.codeSent'));
      setCountdown(300); // 5分钟倒计时
      setCurrentStep(ForgotPasswordStep.CODE);
    } catch (error: any) {
      setErrors({
        general: error?.message || t('auth.errors.forgotPassword')
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!validateForm()) {
      return;
    }

    setErrors({});
    setIsLoading(true);

    try {
      // 模拟验证码验证 API 调用
      // 在实际应用中，这里应该调用真实的API
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // 模拟验证成功
      if (verificationCode === '123456') {
        setCurrentStep(ForgotPasswordStep.PASSWORD);
      } else {
        throw new Error('验证码错误');
      }
    } catch (error: any) {
      setErrors({
        general: error?.message || t('auth.errors.verificationCode.invalid')
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async () => {
    if (!validateForm()) {
      return;
    }

    setErrors({});
    setIsLoading(true);

    try {
      // 模拟重置密码 API 调用
      // 在实际应用中，这里应该调用真实的API
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast.success('密码重置成功！');
      router.push('/login');
    } catch (error: any) {
      setErrors({
        general: error?.message || t('auth.errors.forgotPassword')
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (countdown > 0) return;

    setIsLoading(true);
    try {
      // 模拟重新发送验证码
      await new Promise((resolve) => setTimeout(resolve, 1000));
      toast.success(t('auth.forgotPassword.codeSent'));
      setCountdown(300);
    } catch (error) {
      toast.error('重新发送失败，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (currentStep === ForgotPasswordStep.EMAIL) {
      await handleSendCode();
    } else if (currentStep === ForgotPasswordStep.CODE) {
      await handleVerifyCode();
    } else if (currentStep === ForgotPasswordStep.PASSWORD) {
      await handleResetPassword();
    }
  };

  const handleBack = () => {
    if (currentStep === ForgotPasswordStep.CODE) {
      setCurrentStep(ForgotPasswordStep.EMAIL);
    } else if (currentStep === ForgotPasswordStep.PASSWORD) {
      setCurrentStep(ForgotPasswordStep.CODE);
    }
  };

  const getStepIcon = (step: ForgotPasswordStep) => {
    switch (step) {
      case ForgotPasswordStep.EMAIL:
        return <Mail className="w-5 h-5" />;
      case ForgotPasswordStep.CODE:
        return <Shield className="w-5 h-5" />;
      case ForgotPasswordStep.PASSWORD:
        return <Key className="w-5 h-5" />;
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case ForgotPasswordStep.EMAIL:
        return t('auth.forgotPassword.title');
      case ForgotPasswordStep.CODE:
        return t('auth.forgotPassword.steps.code');
      case ForgotPasswordStep.PASSWORD:
        return t('auth.forgotPassword.steps.password');
    }
  };

  const getStepSubtitle = () => {
    switch (currentStep) {
      case ForgotPasswordStep.EMAIL:
        return t('auth.forgotPassword.subtitle');
      case ForgotPasswordStep.CODE:
        return t('auth.forgotPassword.codeExpiry');
      case ForgotPasswordStep.PASSWORD:
        return '请设置您的新密码';
    }
  };

  const getSubmitButtonText = () => {
    if (isLoading) {
      return t('auth.forgotPassword.loading');
    }

    switch (currentStep) {
      case ForgotPasswordStep.EMAIL:
        return t('auth.forgotPassword.sendCode');
      case ForgotPasswordStep.CODE:
        return '验证并继续';
      case ForgotPasswordStep.PASSWORD:
        return t('auth.forgotPassword.submit');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-neutral-950 dark:via-neutral-900 dark:to-neutral-950 flex">
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <Card className="border-0 shadow-none bg-transparent">
            <CardHeader className="text-center space-y-2 pb-6">
              <div className="flex items-center justify-center mb-4">
                <Link
                  href="/"
                  className="flex items-center gap-2 max-lg:flex lg:hidden hover:opacity-80 transition-opacity"
                >
                  <Image
                    src={isDarkMode(theme) ? appConfig.logo_dark : appConfig.logo}
                    alt={appConfig.name}
                    width={117}
                    height={31.3333}
                  />
                </Link>
              </div>

              {/* 步骤指示器 */}
              <div className="flex items-center justify-center mb-6">
                <div className="flex items-center space-x-4">
                  {Object.values(ForgotPasswordStep).map((step, index) => (
                    <div key={step} className="flex items-center">
                      <div
                        className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${
                          currentStep === step
                            ? 'border-primary bg-primary text-white'
                            : Object.values(ForgotPasswordStep).indexOf(currentStep) > index
                            ? 'border-green-500 bg-green-500 text-white'
                            : 'border-gray-300 text-gray-400'
                        }`}
                      >
                        {getStepIcon(step)}
                      </div>
                      {index < Object.values(ForgotPasswordStep).length - 1 && (
                        <div
                          className={`w-8 h-px mx-2 ${
                            Object.values(ForgotPasswordStep).indexOf(currentStep) > index
                              ? 'bg-green-500'
                              : 'bg-gray-300'
                          }`}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">{getStepTitle()}</CardTitle>
              <CardDescription className="text-base text-gray-600 dark:text-gray-300">
                {getStepSubtitle()}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-4">
                {errors.general && (
                  <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <p className="text-sm text-red-700 dark:text-red-300">{errors.general}</p>
                  </div>
                )}

                {/* 邮箱输入步骤 */}
                {currentStep === ForgotPasswordStep.EMAIL && (
                  <div className="space-y-2">
                    <Input
                      id="email"
                      type="email"
                      placeholder={t('auth.forgotPassword.email')}
                      value={email}
                      onChange={(e) => {
                        setEmail(e.target.value);
                        if (errors.email) setErrors((prev) => ({ ...prev, email: undefined }));
                      }}
                      className={`flex h-12 p-4 w-full rounded-lg border bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-md transition-all duration-200 ${
                        errors.email ? 'border-red-500 focus-visible:ring-red-500' : 'border-primary/10'
                      }`}
                      disabled={isLoading}
                      autoComplete="email"
                    />
                    {errors.email && <p className="text-sm text-red-600 dark:text-red-400">{errors.email}</p>}
                  </div>
                )}

                {/* 验证码输入步骤 */}
                {currentStep === ForgotPasswordStep.CODE && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Input
                        id="verificationCode"
                        type="text"
                        placeholder={t('auth.forgotPassword.verificationCode')}
                        value={verificationCode}
                        onChange={(e) => {
                          setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6));
                          if (errors.verificationCode) setErrors((prev) => ({ ...prev, verificationCode: undefined }));
                        }}
                        className={`flex h-12 p-4 w-full rounded-lg border bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-md transition-all duration-200 text-center tracking-widest ${
                          errors.verificationCode ? 'border-red-500 focus-visible:ring-red-500' : 'border-primary/10'
                        }`}
                        disabled={isLoading}
                        maxLength={6}
                      />
                      {errors.verificationCode && (
                        <p className="text-sm text-red-600 dark:text-red-400">{errors.verificationCode}</p>
                      )}
                    </div>

                    <div className="flex justify-center">
                      <Button
                        type="button"
                        variant="link"
                        onClick={handleResendCode}
                        disabled={countdown > 0 || isLoading}
                        className="text-sm"
                      >
                        {countdown > 0
                          ? `${t('auth.forgotPassword.resendIn')} ${Math.floor(countdown / 60)}:${String(
                              countdown % 60
                            ).padStart(2, '0')}`
                          : t('auth.forgotPassword.resendCode')}
                      </Button>
                    </div>
                  </div>
                )}

                {/* 密码设置步骤 */}
                {currentStep === ForgotPasswordStep.PASSWORD && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="relative">
                        <Input
                          id="newPassword"
                          type={showNewPassword ? 'text' : 'password'}
                          placeholder={t('auth.forgotPassword.newPassword')}
                          value={newPassword}
                          onChange={(e) => {
                            setNewPassword(e.target.value);
                            if (errors.newPassword) setErrors((prev) => ({ ...prev, newPassword: undefined }));
                          }}
                          className={`flex h-12 p-4 w-full rounded-lg border bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-md transition-all duration-200 pr-12 ${
                            errors.newPassword ? 'border-red-500 focus-visible:ring-red-500' : 'border-primary/10'
                          }`}
                          disabled={isLoading}
                          autoComplete="new-password"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-2 top-1/2 h-8 w-8 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          disabled={isLoading}
                        >
                          {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                      {errors.newPassword && (
                        <p className="text-sm text-red-600 dark:text-red-400">{errors.newPassword}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          type={showConfirmPassword ? 'text' : 'password'}
                          placeholder={t('auth.forgotPassword.confirmPassword')}
                          value={confirmPassword}
                          onChange={(e) => {
                            setConfirmPassword(e.target.value);
                            if (errors.confirmPassword) setErrors((prev) => ({ ...prev, confirmPassword: undefined }));
                          }}
                          className={`flex h-12 p-4 w-full rounded-lg border bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-md transition-all duration-200 pr-12 ${
                            errors.confirmPassword ? 'border-red-500 focus-visible:ring-red-500' : 'border-primary/10'
                          }`}
                          disabled={isLoading}
                          autoComplete="new-password"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-2 top-1/2 h-8 w-8 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          disabled={isLoading}
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                      {errors.confirmPassword && (
                        <p className="text-sm text-red-600 dark:text-red-400">{errors.confirmPassword}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* 提交按钮 */}
                <Button
                  type="submit"
                  className="w-full h-12 bg-gray-800 hover:bg-gray-900 dark:bg-gray-200 dark:hover:bg-gray-100 text-white dark:text-black font-medium transition-all duration-200 rounded-lg"
                  disabled={isLoading}
                >
                  {getSubmitButtonText()}
                </Button>

                {/* 返回按钮 */}
                <div className="flex justify-between items-center pt-2">
                  {currentStep !== ForgotPasswordStep.EMAIL ? (
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleBack}
                      disabled={isLoading}
                      className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
                    >
                      <ArrowLeft className="w-4 h-4" />
                      返回上一步
                    </Button>
                  ) : (
                    <div></div>
                  )}

                  <Link href="/login">
                    <Button variant="link" className="text-muted-foreground hover:text-foreground">
                      {t('auth.forgotPassword.backToLogin')}
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

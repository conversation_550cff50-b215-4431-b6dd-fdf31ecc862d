'use client';


import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Play, FileText, MessageSquare, Users, Zap } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';

const quickStartSteps = [
  {
    id: '1',
    titleKey: 'guidePage.quickStart.step1.title',
    descriptionKey: 'guidePage.quickStart.step1.description',
    icon: FileText,
    completed: true
  },
  {
    id: '2',
    titleKey: 'guidePage.quickStart.step2.title',
    descriptionKey: 'guidePage.quickStart.step2.description',
    icon: Zap,
    completed: false
  },
  {
    id: '3',
    titleKey: 'guidePage.quickStart.step3.title',
    descriptionKey: 'guidePage.quickStart.step3.description',
    icon: BookOpen,
    completed: false
  },
  {
    id: '4',
    titleKey: 'guidePage.quickStart.step4.title',
    descriptionKey: 'guidePage.quickStart.step4.description',
    icon: MessageSquare,
    completed: false
  }
];

const features = [
  {
    titleKey: 'guidePage.features.feature1.title',
    descriptionKey: 'guidePage.features.feature1.description',
    icon: Zap
  },
  {
    titleKey: 'guidePage.features.feature2.title',
    descriptionKey: 'guidePage.features.feature2.description',
    icon: Users
  },
  {
    titleKey: 'guidePage.features.feature3.title',
    descriptionKey: 'guidePage.features.feature3.description',
    icon: MessageSquare
  },
  {
    titleKey: 'guidePage.features.feature4.title',
    descriptionKey: 'guidePage.features.feature4.description',
    icon: BookOpen
  }
];

const faqs = [
  {
    questionKey: 'guidePage.faq.question1',
    answerKey: 'guidePage.faq.answer1'
  },
  {
    questionKey: 'guidePage.faq.question2',
    answerKey: 'guidePage.faq.answer2'
  },
  {
    questionKey: 'guidePage.faq.question3',
    answerKey: 'guidePage.faq.answer3'
  },
  {
    questionKey: 'guidePage.faq.question4',
    answerKey: 'guidePage.faq.answer4'
  },
  {
    questionKey: 'guidePage.faq.question5',
    answerKey: 'guidePage.faq.answer5'
  }
];

export default function GuidePage() {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto max-w-4xl py-8 px-4">
      <header className="text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white">{t('guidePage.title')}</h1>
        <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">{t('guidePage.subtitle')}</p>
      </header>

      {/* Quick Start */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">{t('guidePage.quickStart.title')}</h2>
        <div className="grid gap-4">
          {quickStartSteps.map((step, index) => (
            <Card
              key={step.id}
              className={`${step.completed ? 'border-green-200 bg-green-50 dark:bg-green-900/20' : ''}`}
            >
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div
                    className={`p-2 rounded-full ${
                      step.completed ? 'bg-green-100 dark:bg-green-800' : 'bg-gray-100 dark:bg-gray-800'
                    }`}
                  >
                    <step.icon
                      className={`h-5 w-5 ${
                        step.completed ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'
                      }`}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-500">{t('guidePage.quickStart.step')} {step.id}</span>
                      {step.completed && (
                        <Badge variant="secondary" className="bg-green-100 text-green-700">
                          {t('guidePage.quickStart.completed')}
                        </Badge>
                      )}
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">{t(step.titleKey)}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{t(step.descriptionKey)}</p>
                  </div>
                  {!step.completed && <Button size="sm">{t('guidePage.quickStart.start')}</Button>}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Features */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">{t('guidePage.features.title')}</h2>
        <div className="grid md:grid-cols-2 gap-6">
          {features.map((feature, index) => (
            <Card key={index}>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <feature.icon className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{t(feature.titleKey)}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription>{t(feature.descriptionKey)}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Video Tutorial */}
      <section className="mb-12">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              {t('guidePage.videoTutorial.title')}
            </CardTitle>
            <CardDescription>{t('guidePage.videoTutorial.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
              <Button size="lg">
                <Play className="h-5 w-5 mr-2" />
                {t('guidePage.videoTutorial.playButton')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* FAQ */}
      <section>
        <h2 className="text-2xl font-bold mb-6">{t('guidePage.faq.title')}</h2>
        <Accordion type="single" collapsible className="w-full">
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left">{t(faq.questionKey)}</AccordionTrigger>
              <AccordionContent className="text-gray-600 dark:text-gray-400">{t(faq.answerKey)}</AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </section>
    </div>
  );
}

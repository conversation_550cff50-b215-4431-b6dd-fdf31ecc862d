/*
 * @Date: 2025-06-02 18:31:53
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-10 16:52:21
 * @FilePath: /tutoro-ai-front/app/[locale]/exam/layout.tsx
 */
'use client';

import type React from 'react';
import { TakeLayout } from '@/components/layout/examLayout';
import { usePathname } from 'next/navigation';

// 这个layout将完全替代MainLayout，创建独立的考试环境
export default function ExamLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // 根据路径判断layout配置
  const getLayoutProps = () => {
    if (pathname?.includes('/results')) {
      // 结果页面配置
      return {
        showProgress: false
      };
    } else if (pathname?.includes('/take')) {
      // 考试进行页面配置
      return {
        showProgress: true
      };
    }

    // 默认配置
    return {
      showProgress: false
    };
  };

  const layoutProps = getLayoutProps();

  return <TakeLayout {...layoutProps}>{children}</TakeLayout>;
}

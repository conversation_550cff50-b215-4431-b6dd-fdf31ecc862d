'use client';

import { useState, useMemo, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { QuizQuestion, MultipleChoiceQuestion, FillInTheBlankQuestion } from '@/types';
import { ChevronRight } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { useAppStore } from '@/lib/stores/app-store';
import { ExamSkeleton } from '@/components/ui/exam-skeleton';

// Sample exam questions (same as before)
const sampleExamQuestions: QuizQuestion[] = [
  {
    id: 'q1',
    type: 'multiple-choice',
    question: 'What is the primary role of props in React components?',
    options: [
      'To store internal component data that can change.',
      'To pass data from a parent component to a child component.',
      'To manage component lifecycle events.',
      'To handle user interactions and events.'
    ],
    correctAnswer: 'To pass data from a parent component to a child component.'
  },
  {
    id: 'q2',
    type: 'fill-in-the-blank',
    question: 'In React, the hook is used to manage state within functional components.',
    questionParts: ['In React, the `', '` hook is used to manage state within functional components.'],
    correctAnswer: 'useState'
  },
  {
    id: 'q3',
    type: 'multiple-choice',
    question: 'Which of the following is NOT a valid way to style React components?',
    options: [
      'Inline styles using the `style` attribute.',
      'CSS Modules.',
      'Using `<style>` tags directly within JSX.',
      'Styled-components library.'
    ],
    correctAnswer: 'Using `<style>` tags directly within JSX.'
  },
  {
    id: 'q4',
    type: 'fill-in-the-blank',
    question: 'What are the limitations of ErrorBoundaries and how would you implement keep-alive functionality in React?',
    questionParts: [
      'What are the limitations of ErrorBoundaries and how would you implement keep-alive functionality in React? ',
      ''
    ],
    correctAnswer:
      "ErrorBoundaries catch errors during rendering, in lifecycle methods, and in constructors of the whole tree below them. They don't catch errors for: event handlers, async code, server-side rendering, or errors in the ErrorBoundary itself. For keep-alive, React doesn't have a direct equivalent, but techniques like context, state management (Redux/Zustand), or manually preserving component state and re-rendering with previous state can achieve similar effects, or using libraries that offer offscreen rendering."
  },
  {
    id: 'q5',
    type: 'multiple-choice',
    question: 'How do props differ from state in React components?',
    options: [
      'Props are managed internally; state is shared across all components.',
      'Props can be modified directly; state remains constant throughout.',
      'Props are immutable inputs; state is mutable and managed internally.',
      'Props are mutable values; state is passed down from parent components.'
    ],
    correctAnswer: 'Props are immutable inputs; state is mutable and managed internally.'
  }
];

export default function TakeExamPage() {
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const examId = 'exam-b6a17d8f72634f74940c'; // Example exam ID
  const { t } = useTranslation();
  const { examProgress, setExamProgress, setExamLoading, resetExamProgress } = useAppStore();

  const answeredQuestionsCount = useMemo(() => {
    return Object.values(answers).filter((answer) => answer && answer.trim() !== '').length;
  }, [answers]);

  const totalQuestions = questions.length;
  const allQuestionsAnswered = answeredQuestionsCount === totalQuestions;

  // 模拟加载题目数据
  useEffect(() => {
    const loadExamData = async () => {
      setExamLoading(true);
      
      // 模拟 API 调用延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setQuestions(sampleExamQuestions);
      setExamLoading(false);
    };

    loadExamData();
    
    // 组件卸载时重置状态
    return () => {
      resetExamProgress();
    };
  }, [setExamLoading, resetExamProgress]);

  // 更新答题进度到 store
  useEffect(() => {
    if (totalQuestions > 0) {
      setExamProgress(totalQuestions, answeredQuestionsCount);
    }
  }, [totalQuestions, answeredQuestionsCount, setExamProgress]);

  const handleAnswerChange = (questionId: string, value: string) => {
    setAnswers((prev) => ({ ...prev, [questionId]: value }));
  };

  const handleSkipQuestion = (questionId: string) => {
    handleAnswerChange(questionId, 'SKIPPED_PLACEHOLDER_VALUE_THAT_COUNTS_AS_ANSWERED');
    console.log(`Question ${questionId} skipped.`);
  };

  const handleSubmitExam = () => {
    if (!allQuestionsAnswered) {
      alert(t('exam.take.submitAlert'));
      return;
    }
    console.log('Exam submitted with answers:', answers);
    alert(t('exam.take.submitSuccess'));
  };

  // 如果正在加载，显示骨架屏
  if (examProgress.isLoading) {
    return <ExamSkeleton />;
  }

  return (
    <div className="max-w-3xl mx-auto space-y-8">
      {questions.map((q, index) => (
        <Card key={q.id} className="shadow-sm">
          <CardHeader className="flex flex-row justify-between items-start">
            <CardTitle className="text-lg font-medium">{t('exam.take.question')} {index + 1}</CardTitle>
            <Button variant="ghost" size="sm" onClick={() => handleSkipQuestion(q.id)}>
              {t('exam.take.skip')} <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent>
            {q.type === 'multiple-choice' ? (
              <div className="space-y-3">
                <p className="mb-3 text-foreground">{(q as MultipleChoiceQuestion).question}</p>
                <RadioGroup value={answers[q.id]} onValueChange={(value) => handleAnswerChange(q.id, value)}>
                  {(q as MultipleChoiceQuestion).options.map((option, optIndex) => (
                    <div
                      key={optIndex}
                      className="flex items-center space-x-2 p-3 rounded-md border border-input hover:bg-accent has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                    >
                      <RadioGroupItem value={option} id={`${q.id}-option-${optIndex}`} />
                      <Label htmlFor={`${q.id}-option-${optIndex}`} className="flex-1 cursor-pointer">
                        {String.fromCharCode(65 + optIndex)}. {option}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            ) : (
              <div>
                <p className="mb-3 text-foreground">
                  {(q as FillInTheBlankQuestion).questionParts[0]}
                  {(q as FillInTheBlankQuestion).questionParts.length > 1 &&
                  (q as FillInTheBlankQuestion).questionParts[1] !== '' ? (
                    <span className="italic text-muted-foreground mx-1">[{t('exam.take.fillAnswer')}]</span>
                  ) : null}
                  {(q as FillInTheBlankQuestion).questionParts[1]}
                </p>
                <Textarea
                  placeholder={t('exam.take.enterAnswer')}
                  value={answers[q.id] || ''}
                  onChange={(e) => handleAnswerChange(q.id, e.target.value)}
                  rows={(q as FillInTheBlankQuestion).questionParts[1] === '' ? 5 : 2}
                />
              </div>
            )}
          </CardContent>
        </Card>
      ))}

      <div className="flex justify-center pt-8 pb-8">
        <Button size="lg" onClick={handleSubmitExam} disabled={!allQuestionsAnswered} className="w-full max-w-xs">
          {t('exam.take.submit')}
        </Button>
      </div>
    </div>
  );
}

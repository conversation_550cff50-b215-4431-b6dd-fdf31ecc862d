'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { ArrowLeft, ArrowRight, BarChart3, FileText, List, MessageSquare, RefreshCw, Share2, ChevronLeft, ChevronRight } from 'lucide-react';
import { ExamResultsSummary } from '@/components/exam-results/exam-results-summary';
import { ExamResultsDetails } from '@/components/exam-results/exam-results-details';
import { ExamResultsChart } from '@/components/exam-results/exam-results-chart';
import { ExamFeedbackModal } from '@/components/exam-results/exam-feedback-modal';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/lib/translation-context';

// Skeleton components
const SkeletonCard = ({ className = "" }: { className?: string }) => (
  <div className={`animate-pulse ${className}`}>
    <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-full mb-2"></div>
    <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-3/4 mb-2"></div>
    <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-1/2"></div>
  </div>
);

const CircularProgressSkeleton = () => (
  <div className="animate-pulse">
    <div className="relative w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 mb-4 mx-auto">
      <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
      <div className="absolute inset-4 bg-white dark:bg-gray-800 rounded-full"></div>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-gray-200 dark:bg-gray-700 rounded h-8 w-16 mb-2"></div>
          <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-12"></div>
        </div>
      </div>
    </div>
    <div className="grid grid-cols-3 w-full gap-2 sm:gap-4">
      {[1, 2, 3].map((i) => (
        <div key={i} className="text-center">
          <div className="bg-gray-200 dark:bg-gray-700 rounded h-6 w-full mb-1"></div>
          <div className="bg-gray-200 dark:bg-gray-700 rounded h-3 w-full"></div>
        </div>
      ))}
    </div>
  </div>
);

const CategoryPerformanceSkeleton = () => (
  <div className="space-y-3 sm:space-y-4">
    {[1, 2, 3, 4, 5].map((i) => (
      <div key={i} className="space-y-1 animate-pulse">
        <div className="flex justify-between">
          <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-32"></div>
          <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-12"></div>
        </div>
        <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-2 w-full"></div>
      </div>
    ))}
  </div>
);

const TabContentSkeleton = () => (
  <div className="space-y-4 animate-pulse">
    {[1, 2, 3].map((i) => (
      <div key={i} className="border rounded-lg p-4">
        <div className="bg-gray-200 dark:bg-gray-700 rounded h-5 w-3/4 mb-3"></div>
        <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-full mb-2"></div>
        <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-5/6 mb-2"></div>
        <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-2/3"></div>
      </div>
    ))}
  </div>
);

// Sample exam results data array
const examResults = [
  {
    examId: 'exam-b6a17d8f72634f74940c',
    examTitle: '考试1',
    score: 8,
    totalScore: 15,
    percentage: 53.3,
    timeSpent: '20分钟',
    totalQuestions: 15,
    correctAnswers: 8,
    incorrectAnswers: 7,
    skippedQuestions: 0,
    sections: [
      {
        id: 'section1',
        title: 'JavaScript 基础',
        score: 5,
        totalScore: 8,
        questions: []
      },
      {
        id: 'section2',
        title: 'React 进阶',
        score: 3,
        totalScore: 7,
        questions: []
      }
    ],
    categoryPerformance: [
      { category: 'JavaScript 基础', correct: 5, total: 8, percentage: 62.5 },
      { category: 'React 进阶', correct: 3, total: 7, percentage: 42.9 }
    ]
  },
  {
    examId: 'exam-c7b28e9f83745g85051d',
    examTitle: '考试2',
    score: 12,
    totalScore: 15,
    percentage: 80.0,
    timeSpent: '18分钟',
    totalQuestions: 15,
    correctAnswers: 12,
    incorrectAnswers: 3,
    skippedQuestions: 0,
    sections: [
      {
        id: 'section1',
        title: 'Vue.js 框架',
        score: 7,
        totalScore: 9,
        questions: []
      },
      {
        id: 'section2',
        title: 'TypeScript',
        score: 5,
        totalScore: 6,
        questions: []
      }
    ],
    categoryPerformance: [
      { category: 'Vue.js 框架', correct: 7, total: 9, percentage: 77.8 },
      { category: 'TypeScript', correct: 5, total: 6, percentage: 83.3 }
    ]
  },
  {
    examId: 'exam-d8c39fag94856h96162e',
    examTitle: '考试3',
    score: 6,
    totalScore: 15,
    percentage: 40.0,
    timeSpent: '22分钟',
    totalQuestions: 15,
    correctAnswers: 6,
    incorrectAnswers: 9,
    skippedQuestions: 0,
    sections: [
      {
        id: 'section1',
        title: 'Node.js 后端开发',
        score: 3,
        totalScore: 8,
        questions: []
      },
      {
        id: 'section2',
        title: '数据库操作',
        score: 3,
        totalScore: 7,
        questions: []
      }
    ],
    categoryPerformance: [
      { category: 'Node.js 后端开发', correct: 3, total: 8, percentage: 37.5 },
      { category: '数据库操作', correct: 3, total: 7, percentage: 42.9 }
    ]
  },
  {
    examId: 'exam-e9d4agbh05967i07273f',
    examTitle: '考试4',
    score: 5,
    totalScore: 15,
    percentage: 33.3,
    timeSpent: '25分钟',
    totalQuestions: 15,
    correctAnswers: 5,
    incorrectAnswers: 10,
    skippedQuestions: 0,
    sections: [
      {
        id: 'section1',
        title: 'React 面试复习指南',
        score: 5,
        totalScore: 15,
        questions: [
          {
            id: 'q1',
            number: 1,
            title: 'React18 有哪些更新?',
            userAnswer: 'Concurrent Mode, Server Components, Automatic Batching',
            correctAnswer: 'Concurrent Mode, Server Components, Automatic Batching, Transitions, Suspense improvements',
            isCorrect: false,
            score: 0,
            maxScore: 2,
            pageReference: '页面1-1'
          },
          {
            id: 'q2',
            number: 3,
            title: '简述React 的生命周期',
            userAnswer:
              '挂载阶段: constructor, render, componentDidMount; 更新阶段: shouldComponentUpdate, render, componentDidUpdate; 卸载阶段: componentWillUnmount',
            correctAnswer:
              '挂载阶段: constructor, getDerivedStateFromProps, render, componentDidMount; 更新阶段: getDerivedStateFromProps, shouldComponentUpdate, render, getSnapshotBeforeUpdate, componentDidUpdate; 卸载阶段: componentWillUnmount',
            isCorrect: false,
            score: 0,
            maxScore: 4,
            pageReference: '页面1-2'
          },
          {
            id: 'q3',
            number: 5,
            title: 'Redux 工作原理',
            userAnswer: 'Redux 是一个状态管理库，通过 action, reducer, store 来管理状态',
            correctAnswer: 'Redux 是一个状态管理库，通过 action, reducer, store 来管理状态，遵循单向数据流',
            isCorrect: true,
            score: 1,
            maxScore: 1,
            pageReference: '页面2-3'
          }
          // More questions...
        ]
      },
      {
        id: 'section2',
        title: '前端面试复习系列文章',
        score: 0,
        totalScore: 10,
        questions: [
          {
            id: 'q11',
            number: 1,
            title: '说说对 html 语义化的理解',
            userAnswer: '使用正确的标签表达正确的内容，如使用 header, footer, article 等',
            correctAnswer:
              '使用正确的标签表达正确的内容，如使用 header, footer, article 等，有利于SEO，提高可访问性，代码可读性',
            isCorrect: false,
            score: 0,
            maxScore: 3,
            pageReference: '页面1-1'
          }
          // More questions...
        ]
      }
    ],
    categoryPerformance: [
      { category: 'React 基础', correct: 2, total: 5, percentage: 40 },
      { category: 'React Hooks', correct: 1, total: 3, percentage: 33.3 },
      { category: '状态管理 (Redux/Context)', correct: 1, total: 2, percentage: 50 },
      { category: '路由 (React Router)', correct: 0, total: 2, percentage: 0 },
      { category: 'HTML & CSS', correct: 1, total: 3, percentage: 33.3 }
    ]
  }
];

export default function ExamResultsPage() {
  const router = useRouter();
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [currentExamIndex, setCurrentExamIndex] = useState(3); // Start with "考试4" (index 3)
  const [isLoading, setIsLoading] = useState(true); // Initial loading
  const [isSwitching, setIsSwitching] = useState(false); // Switching between exams
  const { t } = useTranslation();

  const currentExamResult = examResults[currentExamIndex];

  // Simulate initial loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1200); // 1.2 seconds initial loading

    return () => clearTimeout(timer);
  }, []);

  const handlePreviousExam = async () => {
    if (currentExamIndex > 0) {
      setIsSwitching(true);
      // Simulate loading delay
      setTimeout(() => {
        setCurrentExamIndex(currentExamIndex - 1);
        setIsSwitching(false);
      }, 600); // 0.6 seconds switching delay
    }
  };

  const handleNextExam = async () => {
    if (currentExamIndex < examResults.length - 1) {
      setIsSwitching(true);
      // Simulate loading delay
      setTimeout(() => {
        setCurrentExamIndex(currentExamIndex + 1);
        setIsSwitching(false);
      }, 600); // 0.6 seconds switching delay
    }
  };

  const handleRetryExam = () => {
    // Logic to retry the exam
    router.push(`/take-exam/${currentExamResult.examId}`);
  };

  const handleNewQuestions = () => {
    // Logic to generate new questions
    router.push(`/create-exam?source=${currentExamResult.examId}`);
  };

  const handleCreateExam = () => {
    // Logic to create a new exam
    router.push('/create-exam');
  };

  // Show initial loading skeleton
  if (isLoading) {
    return (
      <div className="container mx-auto py-4 sm:py-6 px-4">
        {/* Header Skeleton */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex items-center gap-4 flex-1">
            <div className="flex items-center gap-2">
              <div className="bg-gray-200 dark:bg-gray-700 rounded h-8 w-8 animate-pulse"></div>
              <div className="bg-gray-200 dark:bg-gray-700 rounded h-8 w-32 animate-pulse"></div>
              <div className="bg-gray-200 dark:bg-gray-700 rounded h-8 w-8 animate-pulse"></div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="bg-gray-200 dark:bg-gray-700 rounded h-9 w-24 animate-pulse"></div>
            <div className="bg-gray-200 dark:bg-gray-700 rounded h-9 w-20 animate-pulse"></div>
          </div>
        </div>

        {/* Main Content Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column Skeleton */}
          <div className="lg:col-span-1 order-2 lg:order-1">
            <Card className="mb-6">
              <CardContent className="pt-6">
                <CircularProgressSkeleton />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="bg-gray-200 dark:bg-gray-700 rounded h-5 w-32 animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <CategoryPerformanceSkeleton />
              </CardContent>
            </Card>
          </div>

          {/* Right Column Skeleton */}
          <div className="lg:col-span-2 order-1 lg:order-2">
            <Card>
              <CardHeader className="pb-2">
                <div className="bg-gray-200 dark:bg-gray-700 rounded h-6 w-40 animate-pulse mb-4"></div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 mb-4 gap-2">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded h-10 animate-pulse"></div>
                  ))}
                </div>
                <TabContentSkeleton />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons Skeleton */}
        <div className="mt-8 flex flex-col sm:flex-row gap-3 sm:gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded h-10 flex-1 animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-4 sm:py-6 px-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <div className="flex items-center gap-4">
            {/* Exam Navigation */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handlePreviousExam}
                disabled={currentExamIndex === 0 || isSwitching}
                className="h-8 w-8"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              
              <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-center min-w-[100px]">
                {isSwitching ? (
                  <div className="bg-gray-200 dark:bg-gray-700 rounded h-8 w-20 animate-pulse mx-auto"></div>
                ) : (
                  currentExamResult.examTitle
                )}
              </div>
              
              <Button
                variant="ghost"
                size="icon"
                onClick={handleNextExam}
                disabled={currentExamIndex === examResults.length - 1 || isSwitching}
                className="h-8 w-8"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
        {/* <div className="flex items-center gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFeedbackModalOpen(true)}
            className="flex-1 sm:flex-none"
            disabled={isSwitching}
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">{t('exam.results.actions.feedback')}</span>
            <span className="sm:hidden">{t('exam.results.actions.feedbackShort')}</span>
          </Button>
          <Button variant="outline" size="sm" className="flex-1 sm:flex-none" disabled={isSwitching}>
            <Share2 className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">{t('exam.results.actions.share')}</span>
            <span className="sm:hidden">{t('exam.results.actions.shareShort')}</span>
          </Button>
        </div> */}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Score Summary */}
        <div className="lg:col-span-1 order-2 lg:order-1">
          <Card className="mb-6">
            <CardContent className="pt-6">
              {isSwitching ? (
                <CircularProgressSkeleton />
              ) : (
                <div className="flex flex-col items-center justify-center">
                  <div className="relative w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 mb-4">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold">
                          {currentExamResult.percentage.toFixed(1)}%
                        </h2>
                        <p className="text-muted-foreground text-sm">{t('exam.results.score')}</p>
                      </div>
                    </div>
                    <svg className="w-full h-full" viewBox="0 0 100 100">
                      {/* Background circle */}
                      <circle cx="50" cy="50" r="45" fill="none" stroke="#e5e7eb" strokeWidth="10" />
                      {/* Progress circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="45"
                        fill="none"
                        stroke={currentExamResult.percentage >= 60 ? '#10b981' : '#f43f5e'}
                        strokeWidth="10"
                        strokeDasharray={`${currentExamResult.percentage * 2.83} 283`}
                        strokeDashoffset="0"
                        transform="rotate(-90 50 50)"
                      />
                    </svg>
                  </div>
                  <div className="grid grid-cols-3 w-full gap-2 sm:gap-4 text-center">
                    <div>
                      <p className="text-lg sm:text-xl lg:text-2xl font-bold">{currentExamResult.correctAnswers}</p>
                      <p className="text-xs text-muted-foreground">{t('exam.results.passed')}</p>
                    </div>
                    <div>
                      <p className="text-lg sm:text-xl lg:text-2xl font-bold">
                        {currentExamResult.score}/{currentExamResult.totalScore}
                      </p>
                      <p className="text-xs text-muted-foreground">{t('exam.results.score')}</p>
                    </div>
                    <div>
                      <p className="text-lg sm:text-xl lg:text-2xl font-bold">{currentExamResult.timeSpent}</p>
                      <p className="text-xs text-muted-foreground">{t('exam.results.timeSpent')}</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">{t('exam.results.categoryPerformance')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 sm:space-y-4">
              {isSwitching ? (
                <CategoryPerformanceSkeleton />
              ) : (
                currentExamResult.categoryPerformance.map((category, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="truncate mr-2">{category.category}</span>
                      <span className="font-medium flex-shrink-0">
                        {category.correct}/{category.total}
                      </span>
                    </div>
                    <Progress value={category.percentage} className="h-2" />
                  </div>
                ))
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Detailed Results */}
        <div className="lg:col-span-2 order-1 lg:order-2">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg sm:text-xl mb-4">{t('exam.results.detailedResults')}</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="summary" className="w-full">
                <TabsList className="grid grid-cols-3 mb-4 w-full">
                  <TabsTrigger value="summary" className="text-xs sm:text-sm" disabled={isSwitching}>
                    <List className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">{t('exam.results.tabs.summary')}</span>
                  </TabsTrigger>
                  <TabsTrigger value="details" className="text-xs sm:text-sm" disabled={isSwitching}>
                    <FileText className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">{t('exam.results.tabs.details')}</span>
                  </TabsTrigger>
                  <TabsTrigger value="analytics" className="text-xs sm:text-sm" disabled={isSwitching}>
                    <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">{t('exam.results.tabs.analytics')}</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="summary" className="mt-0">
                  {isSwitching ? (
                    <TabContentSkeleton />
                  ) : (
                    <ExamResultsSummary examResult={currentExamResult} />
                  )}
                </TabsContent>

                <TabsContent value="details" className="mt-0">
                  {isSwitching ? (
                    <TabContentSkeleton />
                  ) : (
                    <ExamResultsDetails examResult={currentExamResult} />
                  )}
                </TabsContent>

                <TabsContent value="analytics" className="mt-0">
                  {isSwitching ? (
                    <TabContentSkeleton />
                  ) : (
                    <ExamResultsChart examResult={currentExamResult} />
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mt-8 flex flex-col sm:flex-row gap-3 sm:gap-4">
        <Button onClick={handleRetryExam} className="flex-1" disabled={isSwitching}>
          <RefreshCw className="h-4 w-4 mr-2" />
          {t('exam.results.actions.retry')}
        </Button>
        <Button variant="outline" onClick={handleNewQuestions} className="flex-1" disabled={isSwitching}>
          <RefreshCw className="h-4 w-4 mr-2" />
          {t('exam.results.actions.newQuestions')}
        </Button>
        <Button variant="outline" onClick={handleCreateExam} className="flex-1" disabled={isSwitching}>
          <FileText className="h-4 w-4 mr-2" />
          {t('exam.results.actions.createExam')}
        </Button>
      </div>

      <ExamFeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        examId={currentExamResult.examId}
      />
    </div>
  );
}

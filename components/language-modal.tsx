'use client';

import { Check, Globe, ChevronRight } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { type Locale, locales, localeNames, getLocaleFlag } from '@/i18n.config';
import { useTranslation } from '@/lib/translation-context';
import { TokenManager } from '@/lib/api/client';
import { toast } from 'sonner';

interface LanguageModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function LanguageModal({ isOpen, onClose }: LanguageModalProps) {
  const { locale: currentLocale, t } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageSelect = (locale: Locale) => {
    try {
      // 获取当前路径并移除任何现有的语言前缀
      const segments = pathname.split('/').filter(Boolean); // 移除空字符串
      
      // 移除当前的语言前缀（如果存在）
      let pathWithoutLocale = pathname;
      if (segments.length > 0 && locales.includes(segments[0] as Locale)) {
        // 当前路径有语言前缀，移除它
        pathWithoutLocale = '/' + segments.slice(1).join('/');
      }
      
      // 确保路径以 / 开头
      if (!pathWithoutLocale.startsWith('/')) {
        pathWithoutLocale = '/' + pathWithoutLocale;
      }
      
      // 构建新路径
      let newPath: string;
      if (locale === 'en') {
        // 英语不需要前缀
        newPath = pathWithoutLocale;
      } else {
        // 其他语言添加前缀
        newPath = `/${locale}${pathWithoutLocale}`;
      }
      
      // 确保路径格式正确
      if (newPath === '//' || newPath === '') {
        newPath = '/';
      }

      // 使用优化的TokenManager保存语言偏好
      TokenManager.setPreferredLocale(locale);

      // 导航到新路径
      router.push(newPath);
      onClose();
    } catch (error) {
      toast.error('Failed to change language');
      console.error('Language switch error:', error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[90%] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <div className="flex items-center gap-2">
            <Globe className="w-5 h-5 text-primary" />
            <DialogTitle className="text-lg font-semibold">{t('languageModal.title')}</DialogTitle>
          </div>
          {/* <p className="text-sm text-muted-foreground">
            Choose your preferred language for the interface
          </p> */}
        </DialogHeader>

        <div className="space-y-2">
          {locales.map((locale) => {
            const isSelected = currentLocale === locale;
            return (
              <Button
                key={locale}
                variant={isSelected ? 'secondary' : 'ghost'}
                className={`w-full justify-between h-auto p-4 transition-all duration-200 ${
                  isSelected ? 'bg-primary/10 border border-primary/20 hover:bg-primary/15' : 'hover:bg-muted/50'
                }`}
                onClick={() => handleLanguageSelect(locale)}
              >
                <div className="flex items-center gap-3">
                  <span className="text-xl">{getLocaleFlag(locale)}</span>
                  <div className="text-left">
                    <div className="font-medium">{localeNames[locale]}</div>
                    <div className="text-xs text-muted-foreground">
                      {locale === 'en' && 'English'}
                      {locale === 'zh' && 'Simplified Chinese'}
                      {locale === 'zh-HK' && 'Traditional Chinese'}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {isSelected && <Check className="w-4 h-4 text-primary" />}
                  {!isSelected && <ChevronRight className="w-4 h-4 text-muted-foreground opacity-50" />}
                </div>
              </Button>
            );
          })}
        </div>

        {/* <div className="pt-4 border-t">
          <p className="text-xs text-muted-foreground text-center">
            Your language preference will be saved and applied across the application
          </p>
        </div> */}
      </DialogContent>
    </Dialog>
  );
}

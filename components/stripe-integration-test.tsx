"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { postStripeCreateCheckoutSession, postStripeCreatePortalSession } from '@/servers/api/zhifu';

interface StripeIntegrationTestProps {
  className?: string;
}

export function StripeIntegrationTest({ className }: StripeIntegrationTestProps) {
  const [loading, setLoading] = useState<string | null>(null);

  const handleCreateCheckout = async () => {
    setLoading('checkout');
    try {
      const response = await postStripeCreateCheckoutSession({ 
        lookupKey: 'pro_monthly' 
      });
      
      console.log('Checkout response:', response);
      
      if (response.data) {
        toast.success('Checkout session created successfully!');
        // 在实际应用中，这里会重定向到Stripe Checkout
        console.log('Checkout URL:', response.data);
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error('Failed to create checkout session');
    } finally {
      setLoading(null);
    }
  };

  const handleCreatePortal = async () => {
    setLoading('portal');
    try {
      // 使用一个示例 session ID
      const response = await postStripeCreatePortalSession({ 
        sessionId: 'test_session_id' 
      });
      
      console.log('Portal response:', response);
      
      if (response.data) {
        toast.success('Portal session created successfully!');
        // 在实际应用中，这里会重定向到Stripe Customer Portal
        console.log('Portal URL:', response.data);
      } else {
        throw new Error('No portal URL returned');
      }
    } catch (error) {
      console.error('Error creating portal session:', error);
      toast.error('Failed to create portal session');
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>Stripe Integration Test</CardTitle>
          <CardDescription>
            Test the Stripe API integration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={handleCreateCheckout}
            disabled={loading === 'checkout'}
            className="w-full"
          >
            {loading === 'checkout' && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Test Create Checkout Session
          </Button>
          
          <Button 
            onClick={handleCreatePortal}
            disabled={loading === 'portal'}
            variant="outline"
            className="w-full"
          >
            {loading === 'portal' && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Test Create Portal Session
          </Button>
        </CardContent>
      </Card>
    </div>
  );
} 
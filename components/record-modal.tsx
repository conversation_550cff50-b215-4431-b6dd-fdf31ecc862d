'use client';

import { useState, useRef, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Mic, Square, Play, Pause, Loader2, Upload, Monitor, X, Shield, AlertTriangle } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import { postInfraFilesUpload } from '@/servers/api/wendangshangchuan';
import { useAppDataStore } from '@/lib/stores/app-data-store';
import { useRouter } from 'next/navigation';

interface RecordModalProps {
  isOpen: boolean;
  onClose: () => void;
  spaceId?: number;
  onCreateSpace?: (title: string, description?: string) => Promise<number | null>;
}

type RecordingState = 'idle' | 'recording' | 'paused' | 'stopped';
type AudioSource = 'microphone' | 'browser-tab';
type ModalStep = 'source-selection' | 'permission-request' | 'recording';

export function RecordModal({ isOpen, onClose, spaceId, onCreateSpace }: RecordModalProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { createContent } = useAppDataStore();

  const [currentStep, setCurrentStep] = useState<ModalStep>('source-selection');
  const [selectedSource, setSelectedSource] = useState<AudioSource | null>(null);
  const [recordingState, setRecordingState] = useState<RecordingState>('idle');
  const [recordingTime, setRecordingTime] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);
  const [browserTabSupported, setBrowserTabSupported] = useState(true);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 检查浏览器是否支持屏幕录制
  useEffect(() => {
    const checkBrowserSupport = () => {
      // 检查是否支持 getDisplayMedia
      if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        setBrowserTabSupported(false);
        return;
      }

      // 检查是否在安全上下文中（HTTPS 或 localhost）
      if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
        setBrowserTabSupported(false);
        return;
      }

      setBrowserTabSupported(true);
    };

    checkBrowserSupport();
  }, []);

  // 清理函数
  const cleanup = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    setRecordingTime(0);
    setRecordingState('idle');
    setAudioBlob(null);
    setAudioUrl(null);
    setIsPlaying(false);
    setCurrentStep('source-selection');
    setSelectedSource(null);
    setIsRequestingPermission(false);
    audioChunksRef.current = [];
  };

  // 关闭弹窗时清理
  useEffect(() => {
    if (!isOpen) {
      cleanup();
    }
  }, [isOpen]);

  // 确保有 spaceId，如果没有则创建新空间
  const ensureSpaceId = async (title: string, description?: string): Promise<number> => {
    let currentSpaceId = spaceId;

    if (!currentSpaceId) {
      // 优先使用传入的 onCreateSpace 方法
      if (onCreateSpace) {
        const newSpaceId = await onCreateSpace(title, description || title);
        if (!newSpaceId) {
          throw new Error('Failed to create space');
        }
        currentSpaceId = newSpaceId;
      } else {
        throw new Error('No space ID provided and no create space method available');
      }
    }

    return currentSpaceId;
  };

  // 选择音频源
  const handleSourceSelect = async (source: AudioSource) => {
    setSelectedSource(source);

    if (source === 'microphone') {
      // 麦克风直接开始录音
      setCurrentStep('recording');
      await startMicrophoneRecording();
    } else if (source === 'browser-tab') {
      if (!browserTabSupported) {
        toast.error(t('record.browserNotSupported') || 'Browser tab recording is not supported in this environment');
        return;
      }
      // 浏览器标签页先显示权限申请界面
      setCurrentStep('permission-request');
    }
  };

  // 申请浏览器标签页权限并开始录音
  const handleRequestBrowserTabPermission = async () => {
    setIsRequestingPermission(true);
    try {
      await startBrowserTabRecording();
      setCurrentStep('recording');
    } catch (error) {
      console.error('Error requesting browser tab permission:', error);

      // 根据错误类型显示不同的提示
      if (error instanceof Error) {
        if (error.name === 'NotSupportedError') {
          toast.error(t('record.browserNotSupported') || 'Browser tab recording is not supported in this environment');
        } else if (error.name === 'NotAllowedError') {
          toast.error(t('record.permissionDenied') || 'Permission denied. Please allow screen recording to continue.');
        } else if (error.name === 'AbortError') {
          toast.error(t('record.userCancelled') || 'Recording cancelled by user');
        } else {
          toast.error(t('record.browserTabAccessDenied') || 'Browser tab audio access denied');
        }
      }

      // 权限被拒绝或出错，返回选择界面
      setCurrentStep('source-selection');
      setSelectedSource(null);
    } finally {
      setIsRequestingPermission(false);
    }
  };

  // 开始麦克风录音
  const startMicrophoneRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      });

      startRecordingWithStream(stream);
    } catch (error) {
      console.error('Error starting microphone recording:', error);
      toast.error(t('record.microphoneAccessDenied') || 'Microphone access denied');
      setCurrentStep('source-selection');
    }
  };

  // 开始浏览器标签页录音
  const startBrowserTabRecording = async () => {
    try {
      // 检查支持性
      if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        throw new Error('getDisplayMedia not supported');
      }

      // 请求屏幕/音频捕获
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: false,
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      });

      startRecordingWithStream(stream);
    } catch (error) {
      console.error('Error starting browser tab recording:', error);
      throw error; // 重新抛出错误，让调用者处理
    }
  };

  // 使用给定的流开始录音
  const startRecordingWithStream = (stream: MediaStream) => {
    const mediaRecorder = new MediaRecorder(stream, {
      mimeType: MediaRecorder.isTypeSupported('audio/webm;codecs=opus') ? 'audio/webm;codecs=opus' : 'audio/webm'
    });

    mediaRecorderRef.current = mediaRecorder;
    audioChunksRef.current = [];

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunksRef.current.push(event.data);
      }
    };

    mediaRecorder.onstop = () => {
      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
      setAudioBlob(audioBlob);
      const url = URL.createObjectURL(audioBlob);
      setAudioUrl(url);

      // 停止所有音轨
      stream.getTracks().forEach((track) => track.stop());
    };

    mediaRecorder.start(100); // 每100ms收集一次数据
    setRecordingState('recording');

    // 开始计时
    timerRef.current = setInterval(() => {
      setRecordingTime((prev) => prev + 1);
    }, 1000);
  };

  // 暂停录音
  const pauseRecording = () => {
    if (mediaRecorderRef.current && recordingState === 'recording') {
      mediaRecorderRef.current.pause();
      setRecordingState('paused');
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // 恢复录音
  const resumeRecording = () => {
    if (mediaRecorderRef.current && recordingState === 'paused') {
      mediaRecorderRef.current.resume();
      setRecordingState('recording');
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && (recordingState === 'recording' || recordingState === 'paused')) {
      mediaRecorderRef.current.stop();
      setRecordingState('stopped');
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // 播放/暂停音频
  const togglePlayback = () => {
    if (!audioUrl) return;

    if (!audioRef.current) {
      audioRef.current = new Audio(audioUrl);
      audioRef.current.onended = () => setIsPlaying(false);
    }

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  // 重新录音
  const retryRecording = () => {
    cleanup();
  };

  // 返回音频源选择
  const backToSourceSelection = () => {
    // 停止当前录音
    if (recordingState === 'recording' || recordingState === 'paused') {
      stopRecording();
    }

    // 清理并返回源选择界面
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    setRecordingTime(0);
    setRecordingState('idle');
    setAudioBlob(null);
    setAudioUrl(null);
    setIsPlaying(false);
    setCurrentStep('source-selection');
    setSelectedSource(null);
    setIsRequestingPermission(false);
    audioChunksRef.current = [];
  };

  // 上传并保存录音
  const handleSave = async () => {
    if (!audioBlob) return;

    setIsUploading(true);
    try {
      // 创建文件对象
      const sourceType = selectedSource === 'microphone' ? 'microphone' : 'browser-tab';
      const fileName = `${sourceType}_recording_${new Date().getTime()}.webm`;
      const audioFile = new File([audioBlob], fileName, { type: 'audio/webm' });

      // 上传文件
      const formData = new FormData();
      formData.append('file', audioFile);

      const uploadResponse = await postInfraFilesUpload({
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (uploadResponse?.code !== '0' || !uploadResponse?.data?.id) {
        throw new Error(uploadResponse?.msg || 'File upload failed');
      }

      const fileId = uploadResponse.data.id;
      const sourceLabel =
        selectedSource === 'microphone'
          ? t('record.microphoneRecording') || 'Microphone Recording'
          : t('record.browserTabRecording') || 'Browser Tab Recording';
      const title = `${sourceLabel} ${new Date().toLocaleString()}`;

      console.log('🚀 ~ handleSave ~ title:', title);

      // 确保有 spaceId
      const currentSpaceId = await ensureSpaceId(title, `Audio recording from ${sourceType}: ${fileName}`);

      // 创建内容
      const contentResponse = await createContent({
        uploadId: fileId,
        spaceId: currentSpaceId,
        title: title
      });

      if (contentResponse?.code !== '0' || !contentResponse?.data?.id) {
        throw new Error(contentResponse?.msg || 'Content creation failed');
      }

      const contentId = contentResponse.data.id;

      // 跳转到文章页面
      router.push(`/article/${contentId}`);
      // 关闭弹窗
      onClose();
    } catch (error) {
      console.error('Save recording error:', error);
      toast.error(error instanceof Error ? error.message : t('record.saveError') || 'Failed to save recording');
    } finally {
      setIsUploading(false);
    }
  };

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 渲染音频源选择界面
  const renderSourceSelection = () => (
    <>
      <DialogHeader>
        <DialogTitle className="text-xl font-semibold">{t('record.selectAudioSource') || '选择音频源'}</DialogTitle>
      </DialogHeader>

      <div className="space-y-4 py-6">
        {/* 麦克风选项 */}
        <button
          onClick={() => handleSourceSelect('microphone')}
          className="w-full p-6 border-2 border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors group"
        >
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg group-hover:bg-blue-100 dark:group-hover:bg-blue-900/40 transition-colors">
              <Mic className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-left">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                {t('record.microphone') || '麦克风'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {t('record.microphoneDescription') || '录制您的声音或课程'}
              </p>
            </div>
          </div>
        </button>

        {/* 浏览器选项卡选项 */}
        <button
          onClick={() => handleSourceSelect('browser-tab')}
          disabled={!browserTabSupported}
          className={`w-full p-6 border-2 rounded-lg transition-colors group ${
            browserTabSupported
              ? 'border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400'
              : 'border-gray-100 dark:border-gray-800 opacity-50 cursor-not-allowed'
          }`}
        >
          <div className="flex items-center space-x-4">
            <div
              className={`p-3 rounded-lg transition-colors ${
                browserTabSupported
                  ? 'bg-blue-50 dark:bg-blue-900/20 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/40'
                  : 'bg-gray-50 dark:bg-gray-800'
              }`}
            >
              {browserTabSupported ? (
                <Monitor className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              ) : (
                <AlertTriangle className="h-8 w-8 text-gray-400" />
              )}
            </div>
            <div className="text-left">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                {t('record.browserTab') || '浏览器选项卡'}
                {!browserTabSupported && (
                  <span className="text-sm text-gray-400 ml-2">({t('record.notSupported') || '不支持'})</span>
                )}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {browserTabSupported
                  ? t('record.browserTabDescription') || '捕捉浏览器标签页中播放的音频'
                  : t('record.requiresHttps') || '需要HTTPS环境或较新的浏览器版本'}
              </p>
            </div>
          </div>
        </button>
      </div>
    </>
  );

  // 渲染权限申请界面
  const renderPermissionRequest = () => (
    <>
      <DialogHeader>
        <div className="flex items-center justify-between">
          <DialogTitle className="text-xl font-semibold">{t('record.requestPermission') || '申请权限'}</DialogTitle>
          <Button variant="ghost" size="sm" onClick={backToSourceSelection} className="h-8 w-8 p-0">
            <X className="h-4 w-4" />
          </Button>
        </div>
      </DialogHeader>

      <div className="flex flex-col items-center space-y-6 py-6">
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-full">
          <Shield className="h-12 w-12 text-blue-600 dark:text-blue-400" />
        </div>

        <div className="text-center space-y-3">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {t('record.permissionTitle') || '需要屏幕录制权限'}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 max-w-sm">
            {t('record.permissionDescription') ||
              '为了录制浏览器标签页的音频，我们需要访问您的屏幕录制权限。点击下方按钮将弹出系统权限窗口。'}
          </p>
        </div>

        <div className="flex space-x-3 w-full">
          <Button onClick={backToSourceSelection} variant="outline" className="flex-1">
            {t('common.cancel') || '取消'}
          </Button>
          <Button onClick={handleRequestBrowserTabPermission} disabled={isRequestingPermission} className="flex-1">
            {isRequestingPermission ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {t('record.requestingPermission') || '申请中...'}
              </>
            ) : (
              <>
                <Shield className="w-4 h-4 mr-2" />
                {t('record.allowPermission') || '允许权限'}
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  );

  // 渲染录音界面
  const renderRecordingInterface = () => (
    <>
      <DialogHeader>
        <div className="flex items-center justify-between">
          <DialogTitle className="text-xl font-semibold">
            {selectedSource === 'microphone'
              ? t('record.microphoneRecording') || '麦克风录音'
              : t('record.browserTabRecording') || '浏览器录音'}
          </DialogTitle>
          <Button variant="ghost" size="sm" onClick={backToSourceSelection} className="h-8 w-8 p-0">
            <X className="h-4 w-4" />
          </Button>
        </div>
      </DialogHeader>

      <div className="flex flex-col items-center space-y-6 py-4">
        {/* 录音时间显示 */}
        <div className="text-3xl font-mono font-bold text-center">{formatTime(recordingTime)}</div>

        {/* 录音按钮 */}
        <div className="flex items-center space-x-4">
          {recordingState === 'recording' && (
            <>
              <Button onClick={pauseRecording} className="h-12 w-12 rounded-full" variant="outline">
                <Pause className="h-6 w-6" />
              </Button>
              <Button
                onClick={stopRecording}
                className="h-16 w-16 rounded-full bg-red-500 hover:bg-red-600 text-white"
                size="lg"
              >
                <Square className="h-8 w-8" />
              </Button>
            </>
          )}

          {recordingState === 'paused' && (
            <>
              <Button
                onClick={resumeRecording}
                className="h-12 w-12 rounded-full bg-green-500 hover:bg-green-600 text-white"
              >
                <Mic className="h-6 w-6" />
              </Button>
              <Button
                onClick={stopRecording}
                className="h-16 w-16 rounded-full bg-red-500 hover:bg-red-600 text-white"
                size="lg"
              >
                <Square className="h-8 w-8" />
              </Button>
            </>
          )}

          {recordingState === 'stopped' && audioUrl && (
            <Button
              onClick={togglePlayback}
              className="h-12 w-12 rounded-full bg-blue-500 hover:bg-blue-600 text-white"
            >
              {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
            </Button>
          )}
        </div>

        {/* 录音状态提示 */}
        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          {recordingState === 'recording' && (
            <span className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span>{t('record.recording') || 'Recording...'}</span>
            </span>
          )}
          {recordingState === 'paused' && (t('record.paused') || 'Recording paused')}
          {recordingState === 'stopped' && (t('record.completed') || 'Recording completed')}
        </div>

        {/* 操作按钮 */}
        {recordingState === 'stopped' && (
          <div className="flex space-x-3 w-full">
            <Button onClick={retryRecording} variant="outline" className="flex-1">
              {t('record.retry') || 'Retry'}
            </Button>
            <Button onClick={handleSave} disabled={isUploading} className="flex-1">
              {isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t('record.saving') || 'Saving...'}
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  {t('record.save') || 'Save'}
                </>
              )}
            </Button>
          </div>
        )}
      </div>
    </>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        {currentStep === 'source-selection' && renderSourceSelection()}
        {currentStep === 'permission-request' && renderPermissionRequest()}
        {currentStep === 'recording' && renderRecordingInterface()}
      </DialogContent>
    </Dialog>
  );
}

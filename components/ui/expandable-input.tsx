'use client';

import type React from 'react';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowUp, Globe, Search, Loader2 } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { useAppDataStore, type Model } from '@/lib/stores/app-data-store';
import { useRouter } from 'next/navigation';
import appConfig from '@/config/app';

interface ExpandableInputProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  className?: string;
  selectedMode?: string;
  onModeChange?: (mode: string) => void;
  useModels?: boolean; // 是否使用 models 数据，默认为 true
  isSubmitting?: boolean; // 是否正在提交
  isAuthenticated?: boolean; // 是否已登录
}

export function ExpandableInput({
  placeholder = '',
  value,
  onChange,
  onSubmit,
  className = '',
  selectedMode = 'default',
  onModeChange,
  useModels = true,
  isSubmitting = false,
  isAuthenticated = false
}: ExpandableInputProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [mode, setMode] = useState(selectedMode);
  const [isGlobeActive, setIsGlobeActive] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const { models } = useAppDataStore();
  const router = useRouter();

  // 移除了初始化逻辑，现在由 HybridAuthProvider 统一管理

  // 同步 selectedMode prop 的变化
  useEffect(() => {
    setMode(selectedMode);
  }, [selectedMode]);

  const handleFocus = () => {
    setIsExpanded(true);
  };

  const handleBlur = (e: React.FocusEvent) => {
    // 只在真正离开整个输入区域时才收起
    if (containerRef.current && !containerRef.current.contains(e.relatedTarget as Node)) {
      // 只在输入为空时收起
      if (!value.trim()) {
        setIsExpanded(false);
      }
    }
  };

  const handleSubmit = () => {
    if (isSubmitting) return;
    if (!isAuthenticated) {
      router.push('/login?redirect=/&search=' + value);
      return;
    }
    onSubmit();
    if (!value.trim()) {
      setIsExpanded(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isSubmitting) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleModeChange = (newMode: string) => {
    setMode(newMode);
    onModeChange?.(newMode);
  };

  useEffect(() => {
    if (isExpanded && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isExpanded]);

  // 根据 useModels 决定使用 models 数据还是默认选项
  const displayModes =
    useModels && models.length > 0
      ? models.map((model) => ({
          key: model.name || 'Unknown',
          label: model.name || 'Unknown Model'
        }))
      : [];

  // 获取当前模式的显示标签
  const currentModeLabel = displayModes.find((m) => m.key === mode)?.label || mode;

  return (
    <div
      ref={containerRef}
      className={`flex w-full flex-col transition-all pb-1.5 pt-1.5 duration-150 justify-center px-2.5 border rounded-2xl space-y-1 focus-within:border hover:shadow-md hover:dark:shadow-[0_0_8px_rgba(255,255,255,0.1)] bg-neutral-100/20 dark:bg-neutral-800/50 relative ${className}`}
    >
      <div className="flex items-center">
        <Input
          ref={inputRef as any}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyPress={handleKeyPress}
          className="pr-12 py-3 text-base border-0 bg-transparent shadow-none rounded-full focus:ring-0 focus:ring-offset-0 focus:border-0 focus:outline-none dark:text-white dark:placeholder:text-gray-400"
          style={{ boxShadow: 'none', border: 'none' }}
        />
      </div>
      {/* Bottom Controls - Compact Design */}
      <div
        className={`overflow-hidden transition-all relative ${
          isExpanded ? 'max-h-28 opacity-100 mb-0' : 'max-h-0 opacity-0 '
        }`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {isAuthenticated && (
              <div
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <Select value={mode} onValueChange={handleModeChange}>
                  <SelectTrigger
                    className="w-auto border-0 bg-transparent p-0 h-auto text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-200 px-2"
                    tabIndex={0}
                    style={{ boxShadow: 'none', border: 'none' }}
                  >
                    <SelectValue placeholder={currentModeLabel} />
                  </SelectTrigger>
                  <SelectContent className="min-w-[120px] border-0 shadow-none dark:bg-neutral-800 dark:border-neutral-700">
                    {displayModes.map((modeOption) => (
                      <SelectItem
                        key={modeOption.key}
                        value={modeOption.key}
                        className="py-2 text-xs dark:text-gray-200"
                      >
                        {modeOption.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            {/* <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsGlobeActive(!isGlobeActive)}
              className={`h-7 px-2 text-xs rounded-md transition-all duration-200 ${
                isGlobeActive
                  ? 'text-blue-600 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-400 border border-blue-600 dark:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-neutral-700'
              }`}
            >
              <Globe className={`w-3 h-3 mr-1 ${isGlobeActive ? 'text-blue-600 dark:text-blue-400' : ''}`} />
              {t('mainContent.search')}
            </Button> */}
          </div>
        </div>
      </div>
      <div className="absolute transition-all right-3 bottom-3">
        <Button
          size="icon"
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="h-8 w-8 rounded-full bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 transition-all duration-200 disabled:opacity-50"
        >
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 text-white animate-spin" />
          ) : (
            <Search className="h-4 w-4 text-white" />
          )}
        </Button>
      </div>
    </div>
  );
}

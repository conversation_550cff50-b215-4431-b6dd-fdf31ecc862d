'use client';

import type React from 'react';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HorizontalScrollContainerProps {
  children: React.ReactNode;
  className?: string;
  showNavigation?: boolean;
  showGradients?: boolean;
}

export function HorizontalScrollContainer({
  children,
  className,
  showNavigation = true,
  showGradients = true
}: HorizontalScrollContainerProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const checkScrollability = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollability();
    const handleResize = () => checkScrollability();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [children]);

  const scrollLeft = () => {
    if (scrollRef.current) {
      const scrollAmount = 320; // Width of one card plus gap
      scrollRef.current.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      const scrollAmount = 320; // Width of one card plus gap
      scrollRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  return (
    <div className={cn('relative group', className)}>
      {/* Left Gradient Overlay */}
      {showGradients && canScrollLeft && (
        <div className="absolute left-0 top-0 bottom-0 w-16 bg-gradient-to-r from-white dark:from-neutral-900 via-white/80 dark:via-neutral-900/80 to-transparent z-10 pointer-events-none" />
      )}

      {/* Right Gradient Overlay */}
      {showGradients && canScrollRight && (
        <div className="absolute right-0 top-0 bottom-0 w-16 bg-gradient-to-l from-white dark:from-neutral-900 via-white/80 dark:via-neutral-900/80 to-transparent z-10 pointer-events-none" />
      )}

      {/* Left Navigation Button */}
      {showNavigation && canScrollLeft && (
        <Button
          variant="ghost"
          size="icon"
          className="absolute left-2 top-1/2 -translate-y-1/2 z-20 bg-white/90 dark:bg-neutral-900/90 hover:bg-white   hover:text-white   dark:hover:bg-neutral-900 dark:hover:text-white shadow-lg border opacity-0 group-hover:opacity-100 transition-opacity duration-200 h-10 w-10"
          onClick={scrollLeft}
          aria-label="Scroll left"
        >
          <ChevronLeft className="h-5 w-5 text-gray-600 dark:text-white" />
        </Button>
      )}

      {/* Scrollable Content */}
      <div
        ref={scrollRef}
        className="overflow-x-auto scrollbar-hide pb-4"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        onScroll={checkScrollability}
      >
        <div className="flex gap-4 w-max px-4">{children}</div>
      </div>

      {/* Right Navigation Button */}
      {showNavigation && canScrollRight && (
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-2 top-1/2 -translate-y-1/2 z-20 bg-white/90 dark:bg-neutral-900/90 hover:bg-white hover:text-white dark:hover:bg-neutral-900 dark:hover:text-white shadow-lg border opacity-0 group-hover:opacity-100 transition-opacity duration-200 h-10 w-10"
          onClick={scrollRight}
          aria-label="Scroll right"
        >
          <ChevronRight className="h-5 w-5 text-gray-600 dark:text-white" />
        </Button>
      )}
    </div>
  );
}

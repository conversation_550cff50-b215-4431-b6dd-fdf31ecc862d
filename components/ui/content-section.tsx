/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-17 13:52:17
 * @FilePath: /tutoro-ai-front/components/ui/content-section.tsx
 */
"use client"

import type React from "react"

import { But<PERSON> } from "@/components/ui/button"
import { ContentGrid } from "./content-grid"
import type { ContentBlockData, ContentBlockActions } from "./content-block"
import { ChevronRight } from "lucide-react"

interface AvailableSpace {
  id: string
  name: string
  icon?: React.ComponentType<{ className?: string }>
}

interface ContentSectionProps {
  title: string
  description?: string
  items: ContentBlockData[]
  actions?: ContentBlockActions
  variant?: "grid" | "list"
  columns?: 1 | 2 | 3 | 4
  showActions?: boolean
  showViewAll?: boolean
  onViewAll?: () => void
  className?: string
  emptyState?: React.ReactNode
}

export function ContentSection({
  title,
  description,
  items,
  actions,
  variant = "grid",
  columns = 3,
  showActions = true,
  showViewAll = false,
  onViewAll,
  className,
  emptyState,
}: ContentSectionProps) {
  return (
    <section className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          {description && <p className="text-gray-600 mt-1">{description}</p>}
        </div>
        {showViewAll && onViewAll && (
          <Button variant="ghost" onClick={onViewAll} className="text-gray-600 hover:text-gray-900">
            查看全部
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        )}
      </div>

      {/* Content Grid */}
      <ContentGrid
        items={items}
        actions={actions}
        variant={variant}
        columns={columns}
        showActions={showActions}
        emptyState={emptyState}
      />
    </section>
  )
}

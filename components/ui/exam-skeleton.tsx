import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function ExamSkeleton() {
  return (
    <div className="max-w-3xl mx-auto space-y-8">
      {/* 生成5个问题的骨架 */}
      {Array.from({ length: 5 }).map((_, index) => (
        <Card key={index} className="shadow-sm">
          <CardHeader className="flex flex-row justify-between items-start">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-8 w-16" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              
              {/* 选项骨架 */}
              <div className="space-y-2 mt-4">
                {Array.from({ length: 4 }).map((_, optIndex) => (
                  <div key={optIndex} className="flex items-center space-x-2 p-3 rounded-md border">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 flex-1" />
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {/* 提交按钮骨架 */}
      <div className="flex justify-center pt-8">
        <Skeleton className="h-12 w-full max-w-xs" />
      </div>
    </div>
  );
} 
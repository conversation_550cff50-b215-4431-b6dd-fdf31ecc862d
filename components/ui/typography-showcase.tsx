"use client"

import { ResponsiveText } from "./responsive-text"
import { FontSizeControl } from "./font-size-control"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "./card"

export function TypographyShowcase() {
  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Typography System</CardTitle>
          <FontSizeControl />
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <ResponsiveText as="h1" size="6xl" weight="bold">
              Heading 1 - Main Title
            </ResponsiveText>
            <ResponsiveText as="h2" size="4xl" weight="semibold">
              Heading 2 - Section Title
            </ResponsiveText>
            <ResponsiveText as="h3" size="3xl" weight="semibold">
              Heading 3 - Subsection
            </ResponsiveText>
            <ResponsiveText as="h4" size="2xl" weight="medium">
              Heading 4 - Component Title
            </ResponsiveText>
            <ResponsiveText as="h5" size="xl" weight="medium">
              Heading 5 - Small Title
            </ResponsiveText>
            <ResponsiveText as="h6" size="lg" weight="medium">
              Heading 6 - Micro Title
            </ResponsiveText>
          </div>

          <div className="space-y-3">
            <ResponsiveText size="lg" leading="relaxed">
              Large paragraph text for important content that needs emphasis and better readability.
            </ResponsiveText>
            <ResponsiveText size="base" leading="relaxed">
              Regular paragraph text for standard content. This is the default size for most body text and provides
              optimal readability across all devices.
            </ResponsiveText>
            <ResponsiveText size="sm" leading="normal">
              Small text for secondary information, captions, or less important details.
            </ResponsiveText>
            <ResponsiveText size="xs" leading="normal">
              Extra small text for fine print, metadata, or minimal space requirements.
            </ResponsiveText>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <ResponsiveText as="h4" size="lg" weight="semibold">
                Font Weights
              </ResponsiveText>
              <ResponsiveText weight="light">Light weight text</ResponsiveText>
              <ResponsiveText weight="normal">Normal weight text</ResponsiveText>
              <ResponsiveText weight="medium">Medium weight text</ResponsiveText>
              <ResponsiveText weight="semibold">Semibold weight text</ResponsiveText>
              <ResponsiveText weight="bold">Bold weight text</ResponsiveText>
            </div>

            <div className="space-y-2">
              <ResponsiveText as="h4" size="lg" weight="semibold">
                Line Heights
              </ResponsiveText>
              <ResponsiveText leading="tight">Tight leading for compact text</ResponsiveText>
              <ResponsiveText leading="snug">Snug leading for headings</ResponsiveText>
              <ResponsiveText leading="normal">Normal leading for body text</ResponsiveText>
              <ResponsiveText leading="relaxed">Relaxed leading for comfortable reading</ResponsiveText>
              <ResponsiveText leading="loose">Loose leading for spacious layouts</ResponsiveText>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

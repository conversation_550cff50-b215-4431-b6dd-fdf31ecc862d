'use client';

import type React from 'react';

import { useState, useRef, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger
} from '@/components/ui/dropdown-menu';
import {
  MoreHorizontal,
  Edit3,
  Check,
  X,
  Share,
  Trash2,
  FolderOpen,
  Box,
  FileText,
  Video,
  BookOpen,
  Folder
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { useTranslation } from '@/lib/translation-context';
import { useAppDataStore } from '@/lib/stores/app-data-store';
import { toast } from 'sonner';

export interface ContentBlockData {
  id: string;
  title: string;
  description?: string;
  imageUrl?: string;
  type: 'article' | 'exam' | 'space' | 'document' | 'video';
  date?: string;
  status?: 'completed' | 'in_progress' | 'viewed' | 'draft';
  space?: {
    name: string;
    id: string;
  };
  metadata?: {
    progress?: number;
    score?: number;
    duration?: string;
  };
}

export interface ContentBlockActions {
  onTitleEdit?: (id: string, newTitle: string) => void;
  onMove?: (id: string, targetSpaceId: string) => void;
  onDelete?: (id: string) => void;
  onShare?: (id: string) => void;
  onClick?: (id: string) => void;
}

interface AvailableSpace {
  id: string;
  name: string;
  icon?: React.ComponentType<{ className?: string }>;
}

export interface ContentBlockProps {
  data: ContentBlockData;
  actions?: ContentBlockActions;
  variant?: 'card' | 'list' | 'horizontal';
  showActions?: boolean;
  showMetadata?: boolean;
  className?: string;
}

export function ContentBlock({
  data,
  actions,
  variant = 'card',
  showActions = true,
  showMetadata = true,
  className
}: ContentBlockProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(data.title);
  const [isTitleHovered, setIsTitleHovered] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const { t } = useTranslation();
  const { updateHistoryRecord, removeHistoryRecord } = useAppDataStore();

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleTitleEdit = () => {
    setIsEditing(true);
    setEditTitle(data.title);
  };

  const handleTitleSave = async () => {
    if (editTitle.trim() && editTitle !== data.title && !isUpdating) {
      setIsUpdating(true);
      try {
        // 先调用store中的方法更新数据
        await updateHistoryRecord(data.id, editTitle.trim());
        toast.success(t('contentBlock.actions.updateSuccess'));
        // 再调用传入的回调函数（如果有的话）
        actions?.onTitleEdit?.(data.id, editTitle.trim());
      } catch (error) {
        // 如果API调用失败，恢复原标题
        setEditTitle(data.title);
        toast.error(t('contentBlock.actions.updateFailed'));
      } finally {
        setIsUpdating(false);
      }
    }
    setIsEditing(false);
  };

  const handleTitleCancel = () => {
    setEditTitle(data.title);
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSave();
    } else if (e.key === 'Escape') {
      handleTitleCancel();
    }
  };

  const handleMove = (spaceId: string) => {
    actions?.onMove?.(data.id, spaceId);
  };

  const handleDelete = async () => {
    if (isDeleting) return;
    
    setIsDeleting(true);
    try {
      // 先调用store中的方法删除数据
      await removeHistoryRecord(data.id);
      toast.success(t('contentBlock.actions.deleteSuccess'));
      // 再调用传入的回调函数（如果有的话）
      actions?.onDelete?.(data.id);
    } catch (error) {
      toast.error(t('contentBlock.actions.deleteFailed'));
      // 错误处理已在store中完成
    } finally {
      setIsDeleting(false);
    }
  };

  const handleShare = () => {
    actions?.onShare?.(data.id);
  };

  const handleClick = () => {
    if (!isEditing) {
      actions?.onClick?.(data.id);
    }
  };

  const getTypeIcon = () => {
    switch (data.type) {
      case 'article':
        return FileText;
      case 'video':
        return Video;
      case 'exam':
        return BookOpen;
      case 'document':
        return FileText;
      case 'space':
        return Folder;
      default:
        return Box;
    }
  };

  const getStatusBadge = () => {
    if (!data.status) return null;

    const statusConfig = {
      completed: { label: t('contentBlock.status.completed'), variant: 'default' as const, icon: Check },
      in_progress: { label: t('contentBlock.status.inProgress'), variant: 'secondary' as const, icon: undefined },
      viewed: { label: t('contentBlock.status.viewed'), variant: 'outline' as const, icon: undefined },
      draft: { label: t('contentBlock.status.draft'), variant: 'secondary' as const, icon: undefined }
    };

    const config = statusConfig[data.status];
    if (!config) return null;

    return (
      <Badge variant={config.variant} className="text-xs">
        {config.icon && <config.icon className="w-3 h-3 mr-1" />}
        {/* {config.label} */}
      </Badge>
    );
  };

  if (variant === 'list') {
    return (
      <div
        className={cn(
          'group flex items-center gap-2 p-2 hover:bg-gray-50 transition-all duration-200 border-b border-gray-100 cursor-pointer rounded-md',
          className
        )}
        onClick={handleClick}
      >
        {/* Icon/Image */}
        <div className="flex-shrink-0">
          {data.imageUrl ? (
            <div className="w-8 h-8 rounded-md overflow-hidden dark:bg-neutral-900 bg-gray-100">
              <Image
                src={data.imageUrl || '/images/placeholder.svg'}
                alt={data.title}
                width={32}
                height={32}
                className="object-cover"
              />
            </div>
          ) : (
            <div className="w-8 h-8 rounded-md dark:bg-neutral-900 dark:border dark:border-neutral-800 bg-gray-100 flex items-center justify-center">
              {(() => {
                const IconComponent = getTypeIcon();
                return <IconComponent className="w-4 h-4 text-gray-400 dark:text-gray-400" />;
              })()}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-1 mb-0.5">
            {/* Title with fixed height to prevent jitter */}
            <div
              className="flex items-center gap-1 flex-1 min-w-0 h-5"
              onMouseEnter={() => setIsTitleHovered(true)}
              onMouseLeave={() => setIsTitleHovered(false)}
            >
              {isEditing ? (
                <div className="flex items-center gap-1 flex-1">
                  <Input
                    ref={inputRef}
                    value={editTitle}
                    onChange={(e) => setEditTitle(e.target.value)}
                    onKeyDown={handleKeyPress}
                    className="h-5 text-xs py-0 px-1"
                    onClick={(e) => e.stopPropagation()}
                  />
                                <Button size="sm" variant="ghost" onClick={handleTitleSave} className="h-5 w-5 p-0" disabled={isUpdating}>
                <Check className="w-3 h-3 text-green-600" />
              </Button>
                  <Button size="sm" variant="ghost" onClick={handleTitleCancel} className="h-5 w-5 p-0">
                    <X className="w-3 h-3 text-red-600" />
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-1 flex-1 min-w-0">
                  <h3 className="font-normal text-gray-900 dark:text-white truncate text-xs leading-5">{data.title}</h3>
                  <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                    {isTitleHovered && showActions && actions?.onTitleEdit && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTitleEdit();
                        }}
                        className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Edit3 className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Status Badge */}
            {getStatusBadge()}
          </div>

          {/* Description */}
          {showMetadata && data.description && <p className="text-[11px] text-gray-500 truncate">{data.description}</p>}

          {/* Metadata */}
          {showMetadata && (
            <div className="flex items-center gap-2 mt-0.5 text-[11px] text-gray-400">
              {data.date && <span>{data.date}</span>}
              {data.space && (
                <Badge variant="outline" className="text-[10px] px-1 py-0">
                  {data.space.name}
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Actions - Always visible, click to show menu */}
        {showActions && !isEditing && (
          <div
            className="flex-shrink-0 relative z-10"
            onClick={(e) => e.stopPropagation()}
            onMouseDown={(e) => e.stopPropagation()}
          >
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 relative z-10"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  onMouseDown={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                >
                  <MoreHorizontal className="w-3 h-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-40 z-50"
                onClick={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
              >
                {/* {actions?.onMove && availableSpaces.length > 0 && (
                  <DropdownMenuSub>
                    <DropdownMenuSubTrigger>
                      <FolderOpen className="w-4 h-4 mr-2" />
                      {t("contentBlock.actions.move")}
                    </DropdownMenuSubTrigger>
                    <DropdownMenuSubContent className="z-50">
                      {availableSpaces.map((space) => (
                        <DropdownMenuItem
                          key={space.id}
                          onClick={(e) => {
                            e.stopPropagation()
                            handleMove(space.id)
                          }}
                        >
                          {space.icon ? <space.icon className="w-4 h-4 mr-2" /> : <Box className="w-4 h-4 mr-2" />}
                          {space.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuSubContent>
                  </DropdownMenuSub>
                )} */}
                {/* {actions?.onShare && (
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShare();
                    }}
                  >
                    <Share className="w-4 h-4 mr-2" />
                    {t('contentBlock.actions.share')}
                  </DropdownMenuItem>
                )} */}
                {actions?.onDelete && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete();
                      }}
                      className="text-red-600 focus:text-red-600"
                      disabled={isDeleting}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      {isDeleting ? t('contentBlock.actions.deleting') || 'Deleting...' : t('contentBlock.actions.delete')}
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
    );
  }

  // Card variant
  return (
    <Card
      className={cn(
        'group cursor-pointer hover:shadow-sm transition-all duration-300 overflow-hidden rounded-md',
        variant === 'horizontal' && 'flex-shrink-0 w-56',
        className
      )}
      onClick={handleClick}
    >
      {/* Image - Reduced aspect ratio and better no-image handling */}
      <div
        className={cn(
          'relative bg-gray-100 dark:bg-neutral-800 overflow-hidden',
          data.imageUrl ? 'aspect-[4/3]' : 'aspect-[4/2]'
        )}
      >
        {data.imageUrl ? (
          <Image
            src={data.imageUrl}
            alt={data.title}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            {(() => {
              const IconComponent = getTypeIcon();
              return <IconComponent className="w-8 h-8 text-gray-400 dark:text-gray-500" />;
            })()}
          </div>
        )}

        {/* Space Badge - absolute left bottom */}
        {data.space && (
          <Badge variant="secondary" className="absolute left-2 bottom-2 text-[10px] px-2 py-0 flex items-center">
            <Box className="w-3 h-3 mr-1 dark:text-white" />
            {data.space.name}
          </Badge>
        )}

        {/* Actions Overlay */}
        {showActions && !isEditing && (
          <div className="absolute top-1.5 right-1.5 z-10">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="secondary"
                  size="sm"
                  className="h-6 w-6 p-0 bg-white/90 hover:bg-white dark:bg-neutral-900/90 dark:hover:bg-neutral-900 shadow-sm"
                  style={{
                    border: 'none',
                    boxShadow: 'none'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <MoreHorizontal className="w-3 h-3 dark:text-white" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40" onClick={(e) => e.stopPropagation()}>
                {/* {actions?.onMove && availableSpaces.length > 0 && (
                  <DropdownMenuSub>
                    <DropdownMenuSubTrigger>
                      <FolderOpen className="w-4 h-4 mr-2" />
                      {t("contentBlock.actions.move")}
                    </DropdownMenuSubTrigger>
                    <DropdownMenuSubContent>
                      {availableSpaces.map((space) => (
                        <DropdownMenuItem
                          key={space.id}
                          onClick={(e) => {
                            e.stopPropagation()
                            handleMove(space.id)
                          }}
                        >
                          {space.icon ? <space.icon className="w-4 h-4 mr-2" /> : <Box className="w-4 h-4 mr-2" />}
                          {space.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuSubContent>
                  </DropdownMenuSub>
                )} */}
                {/* {actions?.onShare && (
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShare();
                    }}
                  >
                    <Share className="w-4 h-4 mr-2" />
                    {t('contentBlock.actions.share')}
                  </DropdownMenuItem>
                )} */}
                {actions?.onDelete && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete();
                      }}
                      className="text-red-600 focus:text-red-600"
                      disabled={isDeleting}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      {isDeleting ? t('contentBlock.actions.deleting') || 'Deleting...' : t('contentBlock.actions.delete')}
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      <CardContent className="p-3">
        {/* Title with larger size and support for two lines */}
        <div
          className="flex items-start gap-1 mb-2"
          onMouseEnter={() => setIsTitleHovered(true)}
          onMouseLeave={() => setIsTitleHovered(false)}
        >
          {isEditing ? (
            <div className="flex items-center gap-1 w-full">
              <Input
                ref={inputRef}
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                onKeyDown={handleKeyPress}
                className="h-6 text-sm px-1"
                onClick={(e) => e.stopPropagation()}
              />
              <Button size="sm" variant="ghost" onClick={handleTitleSave} className="h-6 w-6 p-0 flex-shrink-0" disabled={isUpdating}>
                <Check className="w-3 h-3 text-green-600" />
              </Button>
              <Button size="sm" variant="ghost" onClick={handleTitleCancel} className="h-6 w-6 p-0 flex-shrink-0">
                <X className="w-3 h-3 text-red-600" />
              </Button>
            </div>
          ) : (
            <div className="flex items-start gap-1 w-full min-w-0">
              <h3 className="font-medium text-gray-900 dark:text-white flex-1 text-sm leading-tight line-clamp-2 min-h-[2.5rem]">
                {data.title}
              </h3>
              <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center mt-0.5">
                {isTitleHovered && showActions && actions?.onTitleEdit && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTitleEdit();
                    }}
                    className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Edit3 className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Description - Only show if showMetadata is true */}
        {showMetadata && data.description && (
          <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">{data.description}</p>
        )}

        {/* Footer - Only show if showMetadata is true */}
        {showMetadata && (
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>{data.date}</span>
            {getStatusBadge()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

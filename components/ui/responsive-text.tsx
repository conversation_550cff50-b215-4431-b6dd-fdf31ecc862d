"use client"

import type { ReactNode, HTMLAttributes } from "react"
import { cn } from "@/lib/utils"

interface ResponsiveTextProps extends HTMLAttributes<HTMLElement> {
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p" | "span" | "div"
  size?: "xs" | "sm" | "base" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl" | "6xl"
  weight?: "light" | "normal" | "medium" | "semibold" | "bold" | "extrabold"
  leading?: "tight" | "snug" | "normal" | "relaxed" | "loose"
  responsive?: boolean
  children: ReactNode
}

export function ResponsiveText({
  as: Component = "p",
  size = "base",
  weight = "normal",
  leading = "normal",
  responsive = true,
  className,
  children,
  ...props
}: ResponsiveTextProps) {
  const weightClasses = {
    light: "font-light",
    normal: "font-normal",
    medium: "font-medium",
    semibold: "font-semibold",
    bold: "font-bold",
    extrabold: "font-extrabold",
  }

  const leadingClasses = {
    tight: "leading-tight",
    snug: "leading-snug",
    normal: "leading-normal",
    relaxed: "leading-relaxed",
    loose: "leading-loose",
  }

  const responsiveClasses = responsive
    ? `text-${size} sm:text-${size} md:text-${size} lg:text-${size} xl:text-${size}`
    : `text-${size}`

  const classes = cn(
    responsiveClasses,
    weightClasses[weight],
    leadingClasses[leading],
    "transition-all duration-300 ease-in-out",
    className,
  )

  return (
    <Component className={classes} {...props}>
      {children}
    </Component>
  )
}

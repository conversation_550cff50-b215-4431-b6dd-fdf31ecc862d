"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useFontSize } from "@/hooks/use-font-size"
import { Minus, Plus, RotateCcw, Type } from "lucide-react"

interface FontSizeControlProps {
  variant?: "compact" | "full"
  className?: string
}

export function FontSizeControl({ variant = "full", className = "" }: FontSizeControlProps) {
  const { currentScale, setFontScale, increaseFontSize, decreaseFontSize, resetFontSize, availableScales } =
    useFontSize()

  const scaleLabels = {
    xs: "Extra Small",
    sm: "Small",
    base: "Normal",
    lg: "Large",
    xl: "Extra Large",
  }

  if (variant === "compact") {
    return (
      <div className={`flex items-center gap-1 ${className}`}>
        <Button
          variant="ghost"
          size="sm"
          onClick={decreaseFontSize}
          disabled={currentScale === "xs"}
          className="h-8 w-8 p-0"
        >
          <Minus className="h-3 w-3" />
        </Button>
        <Button variant="ghost" size="sm" onClick={resetFontSize} className="h-8 w-8 p-0">
          <Type className="h-3 w-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={increaseFontSize}
          disabled={currentScale === "xl"}
          className="h-8 w-8 p-0"
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>
    )
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Type className="h-4 w-4 text-muted-foreground" />
      <div className="flex items-center gap-1">
        <Button variant="outline" size="sm" onClick={decreaseFontSize} disabled={currentScale === "xs"}>
          <Minus className="h-3 w-3" />
        </Button>
        <Select value={currentScale} onValueChange={setFontScale}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {availableScales.map((scale) => (
              <SelectItem key={scale} value={scale}>
                {scaleLabels[scale as keyof typeof scaleLabels]}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button variant="outline" size="sm" onClick={increaseFontSize} disabled={currentScale === "xl"}>
          <Plus className="h-3 w-3" />
        </Button>
      </div>
      <Button variant="ghost" size="sm" onClick={resetFontSize} className="text-muted-foreground">
        <RotateCcw className="h-3 w-3 mr-1" />
        Reset
      </Button>
    </div>
  )
}

"use client"

import type React from "react"

import { ContentBlock, type ContentBlockData, type ContentBlockActions } from "./content-block"
import { HorizontalScrollContainer } from "./horizontal-scroll-container"
import { cn } from "@/lib/utils"

interface AvailableSpace {
  id: string
  name: string
  icon?: React.ComponentType<{ className?: string }>
}

interface ContentGridProps {
  items: ContentBlockData[]
  actions?: ContentBlockActions
  variant?: "grid" | "list" | "horizontal"
  columns?: 1 | 2 | 3 | 4
  showActions?: boolean
  showMetadata?: boolean
  className?: string
  emptyState?: React.ReactNode
}

export function ContentGrid({
  items,
  actions,
  variant = "grid",
  columns = 3,
  showActions = true,
  showMetadata = true,
  className,
  emptyState,
}: ContentGridProps) {
  if (items.length === 0 && emptyState) {
    return <div className={className}>{emptyState}</div>
  }

  // Horizontal scrolling variant with navigation buttons
  if (variant === "horizontal") {
    return (
      <HorizontalScrollContainer className={className}>
        {items.map((item) => (
          <ContentBlock
            key={item.id}
            data={item}
            actions={actions}
            variant="horizontal"
            showActions={showActions}
            showMetadata={showMetadata}
          />
        ))}
      </HorizontalScrollContainer>
    )
  }

  // List variant
  if (variant === "list") {
    return (
      <div className={cn("bg-white dark:bg-neutral-900 rounded-lg border overflow-hidden", className)}>
        {items.map((item) => (
          <ContentBlock
            key={item.id}
            data={item}
            actions={actions}
            variant="list"
            showActions={showActions}
            showMetadata={showMetadata}
          />
        ))}
      </div>
    )
  }

  // Grid variant
  const gridClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
  }

  return (
    <div className={cn("grid gap-6", gridClasses[columns], className)}>
      {items.map((item) => (
        <ContentBlock
          key={item.id}
          data={item}
          actions={actions}
          variant="card"
          showActions={showActions}
          showMetadata={showMetadata}
        />
      ))}
    </div>
  )
}

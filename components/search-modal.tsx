'use client';

import type React from 'react';

import { useState, useEffect, useRef, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X, AlignLeft, Play, Box, MessageSquare, BookOpen, FileText, Clock } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { useAppDataStore } from '@/lib/stores/app-data-store';

interface SearchResult {
  id: string;
  title: string;
  type: 'space' | 'study' | 'exam' | 'chat' | 'other';
  spaceName?: string;
  spaceId?: string;
  createdAt?: Date;
  description?: string;
}

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const { t, locale } = useTranslation();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Get data from AppDataStore
  const { spaces, historyRecords, isLoadingSpaces, isLoadingHistoryRecords } = useAppDataStore();

  // Convert spaces and history records to search results
  const allSearchableItems = useMemo(() => {
    const spaceResults: SearchResult[] = spaces.map((space) => ({
      id: `space-${space.id}`,
      title: space.title || 'Untitled Space',
      type: 'space' as const,
      description: space.description,
      createdAt: space.createAt
        ? (() => {
            const date = new Date(space.createAt);
            return isNaN(date.getTime()) ? undefined : date;
          })()
        : undefined
    }));

    const historyResults: SearchResult[] = historyRecords.map((record) => ({
      id: `history-${record.id}`,
      title: record.title,
      type: record.type,
      spaceName: record.spaceName,
      spaceId: record.spaceId,
      createdAt:
        record.createdAt instanceof Date
          ? record.createdAt
          : (() => {
              const date = new Date(record.createdAt);
              return isNaN(date.getTime()) ? undefined : date;
            })()
    }));

    return [...spaceResults, ...historyResults];
  }, [spaces, historyRecords]);

  // Filter results based on search query
  const searchResults = useMemo(() => {
    if (!searchQuery.trim()) {
      return allSearchableItems;
    }

    const query = searchQuery.toLowerCase();
    return allSearchableItems.filter((item) => {
      return (
        item.title.toLowerCase().includes(query) ||
        item.spaceName?.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query)
      );
    });
  }, [searchQuery, allSearchableItems]);

  // Separate results by type
  const recentActivities = useMemo(() => {
    return searchResults
      .filter((item) => item.type !== 'space')
      .sort((a, b) => {
        if (!a.createdAt && !b.createdAt) return 0;
        if (!a.createdAt) return 1;
        if (!b.createdAt) return -1;
        return b.createdAt.getTime() - a.createdAt.getTime();
      })
      .slice(0, 10); // Limit to 10 recent activities
  }, [searchResults]);

  const searchableSpaces = useMemo(() => {
    return searchResults.filter((item) => item.type === 'space');
  }, [searchResults]);

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  // Handle result click
  const handleResultClick = (result: SearchResult) => {
    onClose();

    if (result.type === 'space') {
      // Navigate to space
      const spaceId = result.id.replace('space-', '');
      router.push(`/${locale}/space/${spaceId}`);
    } else {
      // Navigate to content/article
      const contentId = result.id.replace('history-', '');
      router.push(`/${locale}/article/${contentId}`);
    }
  };

  // Get icon based on result type
  const getResultIcon = (type: string) => {
    switch (type) {
      case 'study':
        return <BookOpen className="h-5 w-5 text-blue-500" />;
      case 'exam':
        return <FileText className="h-5 w-5 text-green-500" />;
      case 'chat':
        return <MessageSquare className="h-5 w-5 text-purple-500" />;
      case 'space':
        return <Box className="h-5 w-5 text-orange-500" />;
      case 'other':
        return <AlignLeft className="h-5 w-5 text-gray-500" />;
      default:
        return <AlignLeft className="h-5 w-5 text-gray-500" />;
    }
  };

  // Get type label
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'study':
        return t('searchModal.typeStudy') || 'Study';
      case 'exam':
        return t('searchModal.typeExam') || 'Exam';
      case 'chat':
        return t('searchModal.typeChat') || 'Chat';
      case 'space':
        return t('searchModal.typeSpace') || 'Space';
      case 'other':
        return t('searchModal.typeOther') || 'Other';
      default:
        return t('searchModal.typeOther') || 'Other';
    }
  };

  // Format date
  const formatDate = (date?: Date | string) => {
    if (!date) return '';

    // Ensure we have a valid Date object
    let dateObj: Date;
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return '';
    }

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return t('searchModal.today') || 'Today';
    } else if (diffDays === 1) {
      return t('searchModal.yesterday') || 'Yesterday';
    } else if (diffDays < 7) {
      return t('searchModal.daysAgo') || `${diffDays} days ago`;
    } else {
      return dateObj.toLocaleDateString(locale === 'en' ? 'en-US' : 'zh-CN');
    }
  };

  const isLoading = isLoadingSpaces || isLoadingHistoryRecords;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl p-0 gap-0 overflow-hidden">
        <div className="flex items-center p-4 border-b">
          <Search className="h-5 w-5 text-muted-foreground mr-2" />
          <Input
            ref={inputRef}
            value={searchQuery}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={t('searchModal.searchPlaceholder') || 'Search spaces and content...'}
            className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-lg"
          />
        </div>

        <div className="max-h-[80vh] overflow-y-auto">
          {/* Loading State */}
          {isLoading && (
            <div className="p-8 text-center text-muted-foreground">
              <div className="flex items-center justify-center gap-2">
                <Search className="h-5 w-5 animate-pulse" />
                <p>{t('searchModal.loading') || 'Loading...'}</p>
              </div>
            </div>
          )}

          {/* Recent Activities Section */}
          {!isLoading && recentActivities.length > 0 && (
            <div className="p-4">
              <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {t('searchModal.recentActivities') || 'Recent Activities'}
              </h3>
              <div className="space-y-1">
                {recentActivities.map((result) => (
                  <div
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className="flex items-center justify-between p-3 hover:bg-accent rounded-md cursor-pointer transition-colors"
                  >
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      {getResultIcon(result.type)}
                      <div className="flex-1 min-w-0">
                        <p className="truncate font-medium">{result.title}</p>
                        {result.spaceName && (
                          <p className="text-xs text-muted-foreground truncate">
                            {t('searchModal.inSpace') || 'in'} {result.spaceName}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground shrink-0">
                      {/* <span className="bg-muted px-2 py-1 rounded-full">{getTypeLabel(result.type)}</span> */}
                      {result.createdAt && <span>{formatDate(result.createdAt)}</span>}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Spaces Section */}
          {!isLoading && searchableSpaces.length > 0 && (
            <div className="p-4 border-t">
              <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                <Box className="h-4 w-4" />
                {t('searchModal.spaces') || 'Spaces'}
              </h3>
              <div className="space-y-1">
                {searchableSpaces.map((space) => (
                  <div
                    key={space.id}
                    onClick={() => handleResultClick(space)}
                    className="flex items-center justify-between p-3 hover:bg-accent rounded-md cursor-pointer transition-colors"
                  >
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <Box className="h-5 w-5 text-orange-500" />
                      <div className="flex-1 min-w-0">
                        <p className="truncate font-medium">{space.title}</p>
                        {space.description && (
                          <p className="text-xs text-muted-foreground truncate">{space.description}</p>
                        )}
                      </div>
                    </div>
                    {space.createdAt && (
                      <span className="text-xs text-muted-foreground shrink-0">{formatDate(space.createdAt)}</span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No Results State */}
          {!isLoading && searchQuery && searchResults.length === 0 && (
            <div className="p-8 text-center text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">{t('searchModal.noResults') || 'No results found'}</p>
              <p className="text-sm">
                {t('searchModal.noResultsDescription') || 'Try different keywords or check your spelling'}
              </p>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !searchQuery && allSearchableItems.length === 0 && (
            <div className="p-8 text-center text-muted-foreground">
              <Box className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">{t('searchModal.emptyState') || 'No content yet'}</p>
              <p className="text-sm">
                {t('searchModal.emptyStateDescription') || 'Create some content to see it here'}
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

'use client';

import type React from 'react';

import { useState } from 'react';
import { format } from 'date-fns';
import { ContentGrid } from '@/components/ui/content-grid';
import type { ContentBlockData, ContentBlockActions } from '@/components/ui/content-block';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Upload, Link, Mic, ArrowUp, Users, MessageCircle, Calendar, ArrowRight, Box } from 'lucide-react';
import { PasteContentModal } from './paste-content-modal';
import { useTranslation } from '@/lib/translation-context';
import { useAppDataStore, useHistoryActions, useHistoryRecords } from '@/lib/stores';
import { useRouter } from 'next/navigation';

export function MainContent() {
  const [isPasteModalOpen, setIsPasteModalOpen] = useState(false);
  const { t } = useTranslation();
  const router = useRouter();
  const historyRecords = useHistoryRecords();
  const { removeHistoryRecord } = useHistoryActions();

  // 将历史记录转换为继续学习的格式
  const continueItems: ContentBlockData[] = historyRecords.map((record) => ({
    id: record.id,
    title: record.title,
    description: record.content,
    type: record.type === 'exam' ? 'exam' : 'article',
    date: format(record.createdAt, 'yyyy年MM月dd日'),
    status: record.type === 'exam' ? 'completed' : 'viewed',
    space: record.spaceId
      ? {
          name: record.spaceName,
          id: record.spaceId
        }
      : undefined
  }));

  const [exploreItems, setExploreItems] = useState<ContentBlockData[]>([
    {
      id: 'explore-1',
      title: '机器学习入门指南',
      // imageUrl: '/images/placeholder.svg?height=180&width=300&text=ML+Guide',
      type: 'article',
      space: {
        name: 'AI 学习',
        id: 'ai-space'
      }
    },
    {
      id: 'explore-2',
      title: '数据结构与算法详解',
      // imageUrl: '/images/placeholder.svg?height=180&width=300&text=Data+Structures',
      type: 'article',
      space: {
        name: '计算机科学',
        id: 'cs-space'
      }
    },
    {
      id: 'explore-3',
      title: 'Web 安全基础知识',
      // imageUrl: '/images/placeholder.svg?height=180&width=300&text=Web+Security',
      type: 'article',
      space: {
        name: '网络安全',
        id: 'security-space'
      }
    },
    {
      id: 'explore-4',
      title: '移动应用开发实践',
      // imageUrl: '/images/placeholder.svg?height=180&width=300&text=Mobile+Dev',
      type: 'article',
      space: {
        name: '移动开发',
        id: 'mobile-space'
      }
    },
    {
      id: 'explore-5',
      title: '云计算架构设计',
      // imageUrl: '/images/placeholder.svg?height=180&width=300&text=Cloud+Computing',
      type: 'article',
      space: {
        name: '云计算',
        id: 'cloud-space'
      }
    }
  ]);

  const handleTitleEdit =
    (items: ContentBlockData[], setItems: React.Dispatch<React.SetStateAction<ContentBlockData[]>>) =>
    (id: string, newTitle: string) => {
      setItems((prev) => prev.map((item) => (item.id === id ? { ...item, title: newTitle } : item)));
    };

  const handleMove =
    (items: ContentBlockData[], setItems: React.Dispatch<React.SetStateAction<ContentBlockData[]>>) =>
    (id: string, targetSpaceId: string) => {
      // const targetSpace = availableSpaces.find((space) => space.id === targetSpaceId);
      // if (!targetSpace) return;
      // setItems((prev) =>
      //   prev.map((item) => (item.id === id ? { ...item, space: { name: targetSpace.name, id: targetSpace.id } } : item))
      // );
    };

  const handleDelete = (id: string) => {
    // 这里可以添加删除历史记录的逻辑
    console.log('Delete not implemented yet:', id);
    // 删除后刷新数据
    refetch();
  };

  const handleShare = (id: string) => {
    console.log('Sharing item:', id);
    // Implement share functionality
  };

  const handleClick = (id: string) => {
    console.log('Clicked item:', id);
    window.location.href = `/article/${id}`;
  };

  const continueActions: ContentBlockActions = {
    onTitleEdit: (id: string, newTitle: string) => {
      // 对于历史记录，我们可以选择不支持标题编辑，或者添加相应的逻辑
      console.log('Title edit for history record:', { id, newTitle });
    },
    onMove: (id: string, targetSpaceId: string) => {
      // 移动历史记录到其他空间的逻辑
      console.log('Move history record:', { id, targetSpaceId });
    },
    onDelete: handleDelete,
    onShare: handleShare,
    onClick: handleClick
  };

  const exploreActions: ContentBlockActions = {
    onTitleEdit: handleTitleEdit(exploreItems, setExploreItems),
    onMove: handleMove(exploreItems, setExploreItems),
    onDelete: (id: string) => {
      setExploreItems((prev) => prev.filter((item) => item.id !== id));
    },
    onShare: handleShare,
    onClick: handleClick
  };

  return (
    <main className="flex-1 overflow-auto w-full bg-white dark:bg-neutral-900">
      <div className="w-full">
        {/* Continue Learning Section */}
        {historyRecords.length > 0 && (
          <section className="mb-6">
            <div className="flex items-center justify-between mb-3 px-2">
              <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold">{t('mainContent.continueLearning')}</h2>
              <Button
                variant="ghost"
                className="text-sm text-muted-foreground hover:text-foreground"
                onClick={() => router.push('/history')}
              >
                {t('mainContent.viewAll')} <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>

            <ContentGrid
              items={continueItems}
              actions={continueActions}
              variant="horizontal"
              showActions={true}
              showMetadata={false}
            />
          </section>
        )}

        {/* 探索主题 */}
        {/* <section className="mb-6">
          <div className="flex items-center justify-between mb-3 px-2">
            <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold">{t('mainContent.exploreTopics')}</h2>
            <Button variant="ghost" className="text-sm text-muted-foreground hover:text-foreground">
              {t('mainContent.viewAll')} <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>

          <ContentGrid
            items={exploreItems}
            actions={exploreActions}
            variant="horizontal"
            showActions={true}
            showMetadata={false}
          />
        </section> */}
      </div>
      <PasteContentModal isOpen={isPasteModalOpen} onClose={() => setIsPasteModalOpen(false)} />
    </main>
  );
}

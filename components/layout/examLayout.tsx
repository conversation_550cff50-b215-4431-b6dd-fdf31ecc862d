/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-11 20:53:32
 * @FilePath: /tutoro-ai-front/components/layout/examLayout.tsx
 */
'use client';

import type React from 'react';
import { useState } from 'react';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Upload, UserCircle, Settings } from 'lucide-react';
import { ShareExamDialog } from '@/components/exam/share-exam-dialog';
import { Header2 } from '@/components/header2';
import { ToastProvider } from '@/components/providers/toast-provider';
import { useAppStore } from '@/lib/stores';

interface TakeLayoutProps {
  children: React.ReactNode;
  showProgress?: boolean;
}

export function TakeLayout({ children, showProgress }: TakeLayoutProps) {
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);

  // Construct a dummy or real share link
  // const shareLink =
  //   typeof window !== 'undefined'
  //     ? `${window.location.origin}/exam/${examId}/share`
  //     : `https://yourapp.com/exam/${examId}/share`;

  return (
    <>
      <ToastProvider />
      <div className="flex flex-col min-h-screen bg-background">
        {/* Exam Header */}
        <Header2
          showProgress={showProgress}
          setIsShareDialogOpen={setIsShareDialogOpen}
        />

        {/* Main Content */}
        <main className="flex-1 bg-white dark:bg-neutral-900">{children}</main>

        {/* Modals */}
        <ShareExamDialog isOpen={isShareDialogOpen} onClose={() => setIsShareDialogOpen(false)} examLink={'shareLink'} />
      </div>
    </>
  );
}

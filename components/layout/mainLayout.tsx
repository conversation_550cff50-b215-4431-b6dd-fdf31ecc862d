/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-17 17:01:31
 * @FilePath: /tutoro-ai-front/components/layout/mainLayout.tsx
 */
'use client';

import type React from 'react';
import { useEffect } from 'react';
import { Sidebar } from '@/components/sidebar';
import { Header } from '@/components/header';
import { LanguageModal } from '@/components/language-modal';
import { FeedbackModal } from '@/components/feedback-modal';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { UpgradeModal } from '@/components/upgrade-modal';
import { SpaceChatWindow } from '@/components/space/space-chat-window';
import { ToastProvider } from '@/components/providers/toast-provider';
import { TranslationProvider } from '@/lib/translation-context';
import { useAppStore } from '@/lib/stores';
import type { Locale } from '@/i18n.config';

interface MainLayoutProps {
  children: React.ReactNode;
  hideHeader?: boolean;
  locale: Locale;
  dictionary: any;
}

export function MainLayout({ children, hideHeader = false, locale, dictionary }: MainLayoutProps) {
  // 使用 zustand store 替代本地状态
  const {
    isLanguageModalOpen,
    isFeedbackModalOpen,
    isUpgradeModalOpen,
    isChatOpen,
    isMobile,
    openLanguageModal,
    closeLanguageModal,
    openFeedbackModal,
    closeFeedbackModal,
    openUpgradeModal,
    closeUpgradeModal,
    closeChat,
    setMobile
  } = useAppStore();

  // 检测屏幕尺寸，设置移动端状态
  useEffect(() => {
    const checkScreenSize = () => {
      setMobile(window.innerWidth < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, [setMobile]);

  return (
    <>
      <ToastProvider />
      <TranslationProvider locale={locale} dictionary={dictionary}>
        <SidebarProvider>
          <div className="flex min-h-screen w-full bg-background">
            <Sidebar onFeedbackClick={openFeedbackModal} onUpgradeClick={openUpgradeModal} />
            <SidebarInset className="flex flex-1 flex-col overflow-hidden">
              {!hideHeader && <Header onUpgradeClick={openUpgradeModal} onLanguageClick={openLanguageModal} />}
              <main className="flex-grow px-6 bg-white dark:bg-neutral-900 h-[calc(100vh-56px)] lg:h-[calc(100vh-72px)] overflow-y-auto">
                {children}
              </main>
            </SidebarInset>
          </div>

          <LanguageModal isOpen={isLanguageModalOpen} onClose={closeLanguageModal} />
          <FeedbackModal isOpen={isFeedbackModalOpen} onClose={closeFeedbackModal} />
          <UpgradeModal isOpen={isUpgradeModalOpen} onClose={closeUpgradeModal} />
        </SidebarProvider>
      </TranslationProvider>
    </>
  );
}

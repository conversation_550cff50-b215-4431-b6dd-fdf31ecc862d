/*
 * @Date: 2025-01-03 10:00:00
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-23 23:04:58
 * @FilePath: /tutoro-ai-front/components/layout/conditional-layout.tsx
 */
'use client';

import type React from 'react';
import { usePathname } from 'next/navigation';
import { MainLayout } from '@/components/layout/mainLayout';
import { TranslationProvider } from '@/lib/translation-context';
import { ToastProvider } from '@/components/providers/toast-provider';
import type { Locale } from '@/i18n.config';

interface ConditionalLayoutProps {
  children: React.ReactNode;
  locale: Locale;
  dictionary: any;
}

export function ConditionalLayout({ children, locale, dictionary }: ConditionalLayoutProps) {
  const pathname = usePathname();
  
  // 检查是否是考试相关路径
  const isExamPath = pathname?.includes('/exam/');
  
  // 检查是否是隐私政策或条款页面
  const isPrivacyOrTermsPath = pathname?.includes('/privacy-policy') || pathname?.includes('/terms-of-service');
  
  // 如果是考试路径或隐私政策/条款页面，只提供基础的Provider，不使用MainLayout
  if (isExamPath || isPrivacyOrTermsPath) {
    return (
      <>
        <ToastProvider />
        <TranslationProvider locale={locale} dictionary={dictionary}>
          {children}
        </TranslationProvider>
      </>
    );
  }
  
  // 非考试路径且非隐私政策/条款页面使用标准的MainLayout
  return (
    <MainLayout locale={locale} dictionary={dictionary}>
      {children}
    </MainLayout>
  );
} 
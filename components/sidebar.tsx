'use client';

import type React from 'react';

import { useState, useCallback, useRef } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Image from 'next/image';
import {
  Plus,
  History,
  Activity,
  ChevronDown,
  ChevronRight,
  MessageSquare,
  Chrome,
  MessageCircle,
  Settings,
  User,
  CreditCard,
  LogOut,
  Crown,
  X,
  Box,
  Home,
  FileText,
  DollarSign,
  MessageCircleQuestion,
  Clock,
  MoreHorizontal,
  Play,
  BookOpen,
  Shield
} from 'lucide-react';
import Link from 'next/link';
import {
  Sidebar as SidebarPrimitive,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarTrigger,
  useSidebar
} from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/lib/translation-context';
import { useMySpaces, useHistoryRecords, useSpaceActions, useHistoryActions, useAppDataLoading } from '@/lib/stores';
import { cn } from '@/lib/utils';
import appConfig from '@/config/app';
import { useHybridAuth, useHybridUser, useHybridIsAuthenticated } from '@/lib/stores/hybrid-auth-store';
import { useCreateSpace } from '@/lib/api/hooks';
import { toast } from 'sonner';
import { useAppStore } from '@/lib/stores/app-store';
import { useTheme } from 'next-themes';
import { isDarkMode } from '@/lib/utils/theme';
import { AsyncExecutionGuard } from '@/lib/utils/debounce';
import { useAppDataStore } from '@/lib/stores/app-data-store';

interface SidebarProps {
  onFeedbackClick: () => void;
  onUpgradeClick: () => void;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  badge?: string;
}

interface SpaceItem {
  id: string;
  name: string;
  items: { id: string; title: string; href: string }[];
  href: string;
}

export function Sidebar({ onFeedbackClick, onUpgradeClick }: SidebarProps) {
  const { t } = useTranslation();
  const pathname = usePathname();
  const router = useRouter();
  const user = useHybridUser();
  const isAuthenticated = useHybridIsAuthenticated();
  const { logout } = useHybridAuth();
  const { theme } = useTheme() || {};

  // 获取侧边栏上下文，用于移动端自动关闭
  const { isMobile, setOpenMobile } = useSidebar();

  // 移动端点击后自动关闭侧边栏的通用处理函数
  const handleMobileClose = () => {
    if (isMobile) {
      setOpenMobile(false);
    }
  };

  // 从统一的应用数据store获取数据和方法
  const mySpaces = useMySpaces();
  const historyRecords = useHistoryRecords();
  const { removeSpace } = useSpaceActions();
  const { removeHistoryRecord } = useHistoryActions();
  const { isLoadingSpaces, isLoadingModels, isInitialized } = useAppDataLoading();
  const { addSpaceToStore } = useAppDataStore();

  // 创建空间的mutation hook
  const createSpaceMutation = useCreateSpace({
    onSuccess: (response) => {
      if (response?.data?.id) {
        // 添加到本地store
        addSpaceToStore({
          id: response.data.id.toString(),
          title: response.data.title || '',
          description: response.data.description || ''
        });

        // 跳转到新创建的空间页面
        router.push(`/space/${response.data.id}`);

        toast.success('空间创建成功');
      } else {
        toast.error('空间创建失败：响应数据格式错误');
      }
    },
    onError: (error: any) => {
      console.error('创建空间失败:', error);
      toast.error(error?.message || '创建空间失败');
    }
  });

  // 是否展开空间
  const [isSpacesOpen, setIsSpacesOpen] = useState(true);
  // 是否展开工具
  const [isToolsOpen, setIsToolsOpen] = useState(true);
  // 是否展开近期活动
  const [isRecentActivityOpen, setIsRecentActivityOpen] = useState(true);
  // 是否展开空间
  const [expandedSpaces, setExpandedSpaces] = useState<string[]>([]);
  // 是否显示所有空间
  const [showAllSpaces, setShowAllSpaces] = useState(false);

  // Main navigation items
  const navigationItems: NavigationItem[] = [
    // {
    //   id: 'home',
    //   label: t('sidebar.home'),
    //   icon: Home,
    //   href: '/'
    // },
    {
      id: 'history',
      label: t('sidebar.history'),
      icon: History,
      href: '/history'
    },
    {
      id: 'notes',
      label: t('sidebar.notes'),
      icon: FileText,
      href: '/notes'
    }
    // {
    //   id: 'space',
    //   label: t('sidebar.space'),
    //   icon: Box,
    //   href: '/space/1'
    // },
    // {
    //   id: 'take-exam',
    //   label: '考试',
    //   icon: FileText,
    //   href: '/exam/take'
    // },
    // {
    //   id: 'exam-results',
    //   label: '考试结果',
    //   icon: FileText,
    //   href: '/exam/results'
    // }
    // {
    //   id: 'aritcle',
    //   label: '文章',
    //   icon: FileText,
    //   href: '/article/1'
    // },
    // {
    //   id: 'profile',
    //   label: t('sidebar.profile'),
    //   icon: User,
    //   href: '/profile'
    // },
    // {
    //   id: 'contact',
    //   label: t('sidebar.contact'),
    //   icon: MessageCircleQuestion,
    //   href: '/contact'
    // }
  ];

  // 将 mySpaces 转换为侧边栏需要的格式，并从 historyRecords 中获取每个空间的文章
  const spaces: SpaceItem[] = mySpaces.map((space) => {
    // 获取该空间的历史记录作为文章列表
    const spaceRecords = historyRecords
      .filter((record) => record.spaceId === String(space.id) && record.type === 'study')
      .map((record) => {
        return {
          id: record.id,
          title: record.title,
          href: `/article/${record.id}`
        };
      });

    return {
      id: String(space.id || ''),
      name: space.title || '', // 修复: 使用 space.title 而不是 space.name
      items: spaceRecords,
      href: `/space/${space.id}`
    };
  });

  // Tools and help items
  const tools = [
    {
      icon: MessageSquare,
      label: t('sidebar.feedback'),
      onClick: () => {
        onFeedbackClick();
        handleMobileClose();
      }
    },
    // { icon: MessageCircle, label: t('sidebar.chat'), onClick: () => {
    //   const { openChat } = useAppStore.getState();
    //   openChat();
    // }},
    // { icon: Settings, label: t('sidebar.quickGuide'), href: '/guide' },
    // { icon: Chrome, label: t("sidebar.chromeExtension"), href: "/extension" },
    // { icon: MessageCircle, label: t("sidebar.discordServer"), href: "/discord" },
    // {
    //   id: 'profile',
    //   label: t('sidebar.profile'),
    //   icon: User,
    //   href: '/profile'
    // },
    { icon: DollarSign, label: t('sidebar.inviteAndEarn'), href: '/invite' },
    {
      label: t('sidebar.contact') as string,
      icon: MessageCircleQuestion,
      href: '/contact'
    },
    {
      label: t('sidebar.terms') as string,
      icon: FileText,
      href: '/terms-of-service'
    },
    {
      label: t('sidebar.privacy') as string,
      icon: Shield,
      href: '/privacy-policy'
    }
  ];

  const toggleSpace = (spaceId: string) => {
    setExpandedSpaces((prev) => (prev.includes(spaceId) ? prev.filter((id) => id !== spaceId) : [...prev, spaceId]));
  };

  const isActiveRoute = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  // 判断当前是否在指定的空间页面
  const isCurrentSpace = (spaceId: string) => {
    // 处理带有语言前缀的路径，如 /zh/space/123 或 /space/123
    return pathname === `/space/${spaceId}` || pathname.endsWith(`/space/${spaceId}`);
  };

  // 判断当前是否在指定的文章页面
  const isCurrentArticle = (articleId: string) => {
    // 处理带有语言前缀的路径，如 /zh/article/456 或 /article/456
    return pathname === `/article/${articleId}` || pathname.endsWith(`/article/${articleId}`);
  };

  const handleNavigation = (href: string) => {
    router.push(href);
    handleMobileClose();
  };

  const handleLogin = () => {
    router.push('/login');
    handleMobileClose();
  };

  // 最近的记录 - 仅获取最近5条
  const recentRecords = historyRecords.slice(0, 5);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}天前`;
    }
  };

  const getRecordIcon = (type: string) => {
    switch (type) {
      case 'exam':
        return FileText;
      case 'study':
        return BookOpen;
      case 'chat':
        return MessageCircle;
      default:
        return Activity;
    }
  };

  const handleActivityMenuAction = (recordId: string, action: string) => {
    switch (action) {
      case 'view':
        // 跳转到对应页面
        const record = historyRecords.find((r) => r.id === recordId);
        if (record) {
          if (record.type === 'exam') {
            router.push('/exam/results');
          } else {
            router.push('/history');
          }
        }
        break;
      case 'share':
        console.log('Share record:', recordId);
        break;
      case 'delete':
        removeHistoryRecord(recordId);
        break;
    }
  };

  const handleSpaceAction = (spaceId: string, action: string) => {
    switch (action) {
      case 'view':
        router.push(`/space/${spaceId}`);
        break;
      case 'edit':
        console.log('Edit space:', spaceId);
        break;
      case 'delete':
        removeSpace(spaceId);
        toast.success(t('spacePage.deleteSpaceSuccess'));
        break;
    }
  };

  // 处理创建空间 - 添加防重复执行保护
  const handleCreateSpace = useCallback(async () => {
    const result = await AsyncExecutionGuard.guard('create-space-sidebar', async () => {
      await createSpaceMutation.mutateAsync({
        title: `${t('spacePage.newSpaceTitle')} ${mySpaces.length + 1}`,
        description: ''
      });
      handleMobileClose();
    });

    if (result === null) {
      console.log('Create space already in progress, skipping...');
    }
  }, [createSpaceMutation, t, mySpaces.length, handleMobileClose]);

  // 显示的空间列表 - 根据showAllSpaces状态决定显示数量
  const displaySpaces = showAllSpaces ? spaces : spaces.slice(0, 4);

  // 骨架屏组件 - 参考Space Header的处理方式
  const SpaceSkeleton = () => (
    <div className="space-y-3">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="flex items-center gap-3 py-2 px-3">
          <div className="w-2 h-2 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse flex-shrink-0" />
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse flex-1" />
        </div>
      ))}
    </div>
  );

  const RecentActivitySkeleton = () => (
    <div className="space-y-3">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="flex items-center gap-3 py-2 px-3">
          <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse flex-shrink-0" />
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse flex-1" />
        </div>
      ))}
    </div>
  );

  // 如果未登录，显示简化的侧边栏
  if (!isAuthenticated) {
    return (
      <SidebarPrimitive className="border-r bg-sidebar bg-neutral-100">
        <SidebarHeader className="px-2 lg:px-6 py-2 lg:py-4">
          <div className="flex items-center justify-between h-10">
            <Link
              href="/"
              className="flex items-center gap-2 hover:opacity-80 transition-opacity"
              onClick={handleMobileClose}
            >
              <div className="pl-4">
                <Image
                  src={isDarkMode(theme) ? appConfig.logo_dark : appConfig.logo}
                  alt={appConfig.name}
                  width={117}
                  height={31.3333}
                />
              </div>
            </Link>
            {/* 收回图标，小于1280px时显示 */}
            <SidebarTrigger state="expanded" className="max-xl:flex xl:hidden h-8 w-8 p-0" />
          </div>
        </SidebarHeader>

        <SidebarContent className="px-2 flex flex-col">
          {/* Add Content Button */}
          <SidebarGroup>
            <SidebarMenu>
              <SidebarMenuItem className="mb-6">
                <SidebarMenuButton
                  className="w-full justify-start text-muted-foreground hover:text-foreground border-2 border-dashed border-gray-300 hover:border-gray-400 py-3 px-4 rounded-lg"
                  onClick={() => {
                    router.push('/');
                    handleMobileClose();
                  }}
                >
                  <Plus className="w-4 h-4" />
                  {t('sidebar.addContent')}
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>

          {/* Welcome Content */}
          <div className="flex-1 flex flex-col justify-center px-4 py-8">
            <div className="text-center space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{t('sidebar.welcomeTitle')}</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('sidebar.welcomeDescription')}</p>
              <p className="text-xs text-gray-500 dark:text-gray-500 leading-relaxed">{t('sidebar.welcomeDetails')}</p>
              <p className="text-xs text-gray-500 dark:text-gray-500 leading-relaxed">{t('sidebar.welcomeFeedback')}</p>
            </div>
          </div>
        </SidebarContent>

        {/* Login Section */}
        <div className="mt-auto border-t pt-4 px-2 pb-4 dark:bg-neutral-900">
          <div className="text-center space-y-3">
            <p className="text-sm text-gray-600 dark:text-gray-400">{t('sidebar.loginPrompt')}</p>
            <Button onClick={handleLogin} className="w-full bg-black hover:bg-gray-800 text-white">
              {t('sidebar.loginButton')}
            </Button>
          </div>
        </div>
      </SidebarPrimitive>
    );
  }

  return (
    <SidebarPrimitive className="border-r bg-sidebar bg-neutral-100">
      <SidebarHeader className="px-2 lg:px-6 py-2 lg:py-4">
        <div className="flex items-center justify-between h-10">
          <Link
            href="/"
            className="flex items-center gap-2 hover:opacity-80 transition-opacity"
            onClick={handleMobileClose}
          >
            <div className="pl-4">
              <Image
                src={isDarkMode(theme) ? appConfig.logo_dark : appConfig.logo}
                alt={appConfig.name}
                width={117}
                height={31.3333}
              />
            </div>
          </Link>
          {/* 收回图标，小于1280px时显示 */}
          <SidebarTrigger state="expanded" className="max-xl:flex xl:hidden h-8 w-8 p-0" />
        </div>
      </SidebarHeader>

      <SidebarContent className="px-2">
        {/* 添加内容 */}
        <SidebarGroup>
          <SidebarMenu>
            {/* Add Content Button */}
            <SidebarMenuItem className="mb-2">
              <SidebarMenuButton
                className="w-full justify-start text-muted-foreground hover:text-foreground border-2 border-dashed border-gray-300 hover:border-gray-400 py-3 px-4 mb-2 rounded-lg"
                onClick={() => {
                  // Handle add space action
                  console.log('Add content clicked');
                  router.push('/');
                  handleMobileClose();
                }}
              >
                <Plus className="w-4 h-4" />
                {t('sidebar.addContent')}
              </SidebarMenuButton>
            </SidebarMenuItem>
            {/* Main Navigation */}
            {navigationItems.map((item) => (
              <SidebarMenuItem key={item.id}>
                <SidebarMenuButton
                  className={cn(
                    'w-full justify-start transition-colors py-2 px-3 hover:bg-accent rounded-md',
                    isActiveRoute(item.href) && 'bg-accent text-accent-foreground font-medium'
                  )}
                  asChild
                >
                  <Link href={item.href} onClick={handleMobileClose}>
                    <item.icon className="w-3.5 h-3.5 md:w-4 md:h-4" />
                    {item.label}
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>

        {/* 近期活动 */}
        <Collapsible open={isRecentActivityOpen} onOpenChange={setIsRecentActivityOpen}>
          <SidebarGroup>
            <SidebarGroupLabel className="flex items-center justify-between rounded-md p-3 py-4 transition-colors text-base font-semibold">
              {!isInitialized ? (
                <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-24" />
              ) : (
                t('sidebar.recentActivity')
              )}
              {/* 隐藏展开按钮 */}
              {/* {isRecentActivityOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />} */}
            </SidebarGroupLabel>
            <CollapsibleContent>
              <SidebarGroupContent>
                <SidebarMenu>
                  {!isInitialized ? (
                    <RecentActivitySkeleton />
                  ) : recentRecords.length > 0 ? (
                    recentRecords.map((record) => {
                      const RecordIcon = getRecordIcon(record.type);
                      return (
                        <SidebarMenuItem key={record.id}>
                          <div className="flex items-center justify-between py-2 px-3 hover:bg-accent rounded-md transition-colors group">
                            {/* 左侧图标和内容 */}
                            <div
                              className="flex items-center gap-3 flex-1 min-w-0 cursor-pointer"
                              onClick={() => {
                                router.push(`/article/${record.id}`);
                                handleMobileClose();
                              }}
                            >
                              <RecordIcon className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                              {isCurrentArticle(record.id) && (
                                <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-green-500 rounded-full flex-shrink-0" />
                              )}
                              <span className="text-sm font-medium truncate">{record.title}</span>
                            </div>

                            {/* 右侧三点菜单 */}
                            {/* <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" side="right">
                                <DropdownMenuItem 
                                  onClick={() => handleActivityMenuAction(record.id, 'view')}
                                >
                                  查看
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handleActivityMenuAction(record.id, 'share')}
                                >
                                  分享
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => handleActivityMenuAction(record.id, 'delete')}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu> */}
                          </div>
                        </SidebarMenuItem>
                      );
                    })
                  ) : (
                    <SidebarMenuItem>
                      <div className="text-sm text-muted-foreground px-3 py-2">{t('sidebar.noHistory')}</div>
                    </SidebarMenuItem>
                  )}

                  {/* View All Link */}
                  {/* {recentRecords.length > 0 && (
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        className="w-full justify-start text-sm text-muted-foreground hover:text-foreground py-1"
                        asChild
                      >
                        <Link href="/history">
                          <span className="text-xs">查看全部历史记录</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )} */}
                </SidebarMenu>
              </SidebarGroupContent>
            </CollapsibleContent>
          </SidebarGroup>
        </Collapsible>

        {/* 空间列表 */}
        <Collapsible open={isSpacesOpen} onOpenChange={setIsSpacesOpen}>
          <SidebarGroup>
            <SidebarGroupLabel className="flex items-center justify-between rounded-md p-3 py-4 transition-colors text-base font-semibold">
              {!isInitialized ? (
                <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-16" />
              ) : (
                t('sidebar.spaces')
              )}
              {/* 隐藏展开按钮 */}
              {/* {isSpacesOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />} */}
            </SidebarGroupLabel>
            <CollapsibleContent>
              <SidebarGroupContent>
                <SidebarMenu>
                  {/* 创建空间 */}
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      className="w-full justify-start text-muted-foreground hover:text-foreground border-2 border-dashed border-gray-300 hover:border-gray-400 py-3 px-4 mb-2 rounded-lg mt-2"
                      onClick={handleCreateSpace}
                      disabled={
                        createSpaceMutation.isPending || AsyncExecutionGuard.isExecuting('create-space-sidebar')
                      }
                    >
                      <Plus className="w-4 h-4" />
                      {createSpaceMutation.isPending || AsyncExecutionGuard.isExecuting('create-space-sidebar')
                        ? t('sidebar.creating')
                        : t('sidebar.addSpace')}
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  {/* 显示骨架屏或空间列表 */}
                  {isLoadingSpaces && !isInitialized ? (
                    <SpaceSkeleton />
                  ) : (
                    <>
                      {/* Space Items - 根据showAllSpaces状态显示 */}
                      {displaySpaces.map((space) => (
                        <SidebarMenuItem key={space.id}>
                          <Collapsible
                            open={expandedSpaces.includes(space.id)}
                            onOpenChange={() => toggleSpace(space.id)}
                          >
                            <div className="flex items-center justify-between py-2 px-3 hover:bg-accent rounded-md transition-colors group">
                              {/* 左侧展开图标和空间信息 */}
                              <div className="flex items-center gap-2 flex-1 min-w-0">
                                {/* 展开/收起图标 */}
                                {space.items.length > 0 && (
                                  <CollapsibleTrigger asChild>
                                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0 hover:bg-transparent">
                                      {expandedSpaces.includes(space.id) ? (
                                        <ChevronDown className="w-3 h-3" />
                                      ) : (
                                        <ChevronRight className="w-3 h-3" />
                                      )}
                                    </Button>
                                  </CollapsibleTrigger>
                                )}

                                {/* 空间名称 - 点击跳转 */}
                                <div
                                  className="flex items-center gap-2 flex-1 min-w-0 cursor-pointer"
                                  onClick={() => {
                                    console.log('🚀 ~ Sidebar ~ space:', space);
                                    router.push(space.href);
                                    handleMobileClose();
                                  }}
                                >
                                  {isCurrentSpace(space.id) && (
                                    <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-green-500 rounded-full flex-shrink-0" />
                                  )}
                                  <span className="text-sm font-medium truncate">{space.name}</span>
                                </div>
                              </div>

                              {/* 右侧三点菜单 */}
                              {/* <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                  >
                                    <MoreHorizontal className="h-3 w-3" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" side="right">
                                  <DropdownMenuItem 
                                    onClick={() => router.push(space.href)}
                                  >
                                    打开空间
                                  </DropdownMenuItem>
                                  <DropdownMenuItem 
                                    onClick={() => console.log('Rename space:', space.id)}
                                  >
                                    重命名
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    onClick={() => console.log('Delete space:', space.id)}
                                    className="text-red-600 focus:text-red-600"
                                  >
                                    删除
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu> */}
                            </div>

                            {/* 展开后的空间内容 */}
                            {space.items.length > 0 && (
                              <CollapsibleContent className="ml-6 mt-1">
                                <div className="space-y-1">
                                  {space.items.map((item, index) => (
                                    <div
                                      key={index}
                                      className="flex items-center justify-between py-1.5 px-3 hover:bg-accent rounded-md transition-colors group cursor-pointer"
                                      onClick={() => {
                                        router.push(item.href);
                                        handleMobileClose();
                                      }}
                                    >
                                      <div className="flex items-center gap-2 flex-1 min-w-0">
                                        <FileText className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                                        {isCurrentArticle(item.id) && (
                                          <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-green-500 rounded-full flex-shrink-0" />
                                        )}
                                        <span className="text-sm text-gray-900 dark:text-white truncate hover:text-foreground transition-colors">
                                          {item.title}
                                        </span>
                                      </div>

                                      {/* 文章项的三点菜单 */}
                                      {/* <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                          <Button 
                                            variant="ghost" 
                                            size="icon" 
                                            className="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity"
                                            onClick={(e) => {
                                              e.stopPropagation(); // 阻止冒泡，避免触发父级点击
                                            }}
                                          >
                                            <MoreHorizontal className="h-2.5 w-2.5" />
                                          </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end" side="right">
                                          <DropdownMenuItem 
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              router.push(`/article/${space.id}-${index + 1}`);
                                            }}
                                          >
                                            查看文章
                                          </DropdownMenuItem>
                                          <DropdownMenuItem 
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              console.log('Share article:', item);
                                            }}
                                          >
                                            分享
                                          </DropdownMenuItem>
                                          <DropdownMenuSeparator />
                                          <DropdownMenuItem 
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              console.log('Remove article:', item);
                                            }}
                                            className="text-red-600 focus:text-red-600"
                                          >
                                            移除
                                          </DropdownMenuItem>
                                        </DropdownMenuContent>
                                      </DropdownMenu> */}
                                    </div>
                                  ))}
                                </div>
                              </CollapsibleContent>
                            )}
                          </Collapsible>
                        </SidebarMenuItem>
                      ))}

                      {/* 显示更多/收起按钮 - 当空间数量超过4个时显示 */}
                      {spaces.length > 4 && (
                        <SidebarMenuItem>
                          <SidebarMenuButton
                            className="w-full justify-start text-sm text-muted-foreground hover:text-foreground py-1"
                            onClick={() => {
                              setShowAllSpaces(!showAllSpaces);
                              // 注意：这里不需要关闭侧边栏，因为只是展开/收起列表
                            }}
                          >
                            <ChevronRight
                              className={cn('w-3 h-3 transition-transform', showAllSpaces && 'rotate-90')}
                            />
                            <span className="text-sm">
                              {showAllSpaces
                                ? `${t('sidebar.showLess')}`
                                : `${t('sidebar.showMore')} (${spaces.length - 4})`}
                            </span>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      )}
                    </>
                  )}
                </SidebarMenu>
              </SidebarGroupContent>
            </CollapsibleContent>
          </SidebarGroup>
        </Collapsible>

        {/* 帮助与工具 */}
        <Collapsible open={isToolsOpen} onOpenChange={setIsToolsOpen}>
          <SidebarGroup>
            <SidebarGroupLabel className="flex items-center justify-between rounded-md transition-colors ml-2 text-sm mb-2 font-semibold">
              {!isInitialized ? (
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-20" />
              ) : (
                t('sidebar.helpAndTools')
              )}
              {/* 隐藏展开按钮 */}
              {/* {isToolsOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />} */}
            </SidebarGroupLabel>
            <CollapsibleContent>
              <SidebarGroupContent>
                <SidebarMenu>
                  {tools.map((tool, index) => (
                    <SidebarMenuItem key={index}>
                      <SidebarMenuButton
                        className={cn(
                          'w-full justify-start transition-colors py-2 px-3 hover:bg-accent rounded-md',
                          tool.href && isActiveRoute(tool.href) && 'bg-accent text-accent-foreground font-medium'
                        )}
                        onClick={tool.onClick}
                        asChild={!!tool.href}
                      >
                        {tool.href ? (
                          <Link href={tool.href} onClick={handleMobileClose}>
                            <tool.icon className="w-4 h-4" />
                            {tool.label}
                          </Link>
                        ) : (
                          <>
                            <tool.icon className="w-4 h-4" />
                            {tool.label}
                          </>
                        )}
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </CollapsibleContent>
          </SidebarGroup>
        </Collapsible>
      </SidebarContent>

      {/* 用户账户 */}
      <div className="mt-auto border-t pt-4 px-2 pb-4 dark:bg-neutral-900">
        {/* Free Plan Badge */}
        <div className="mb-3">
          <Badge
            variant="secondary"
            className="w-full justify-center bg-green-100 text-green-700 hover:bg-green-200 cursor-pointer text-xs font-medium py-1.5 px-3"
            onClick={() => {
              // router.push('/pricing');
              onUpgradeClick();
              handleMobileClose();
            }}
          >
            {user?.subscription?.product === 'PRO' ? t('sidebar.proPlan') : t('sidebar.freePlan')}
          </Badge>
        </div>

        {/* User Email Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div className="w-full bg-white dark:bg-neutral-900 border rounded-lg p-3 hover:bg-gray-50 cursor-pointer transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1 text-center flex justify-center items-center">
                  <span className="text-sm text-gray-900 dark:text-white truncate max-w-[180px] inline-block">
                    {user?.email || user?.username || '未登录'}
                  </span>
                </div>
                <ChevronDown className="h-4 w-4 text-gray-400 flex-shrink-0 ml-2" />
              </div>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" side="top" className="w-56 mb-2" sideOffset={8}>
            <DropdownMenuItem
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => {
                router.push('/profile');
                handleMobileClose();
              }}
            >
              <User className="h-4 w-4" />
              <span>{t('sidebar.profile')}</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => {
                router.push('/pricing');
                handleMobileClose();
              }}
            >
              <Crown className="h-4 w-4" />
              <span>{t('sidebar.fees')}</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => {
                router.push('/history');
                handleMobileClose();
              }}
            >
              <History className="h-4 w-4" />
              <span>{t('sidebar.history')}</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="flex items-center gap-2 cursor-pointer text-red-600 focus:text-red-600"
              onClick={ async () => {
                await logout();
                toast.success(t('sidebar.logoutSuccess'));
                router.push('/');
                handleMobileClose();
              }}
            >
              <LogOut className="h-4 w-4" />
              <span>{t('sidebar.logout')}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </SidebarPrimitive>
  );
}

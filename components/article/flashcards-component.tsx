'use client';
import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, RotateCcw, Lightbulb, Loader2 } from 'lucide-react';
import type { Flashcard } from '@/types';
import { useTranslation } from '@/lib/translation-context';
import { useGenerateFreshcard } from '@/lib/api/hooks';
import { useArticleCacheStore } from '@/lib/stores/article-cache-store';
import { toast } from 'sonner';

interface FlashcardsComponentProps {
  /** 内容ID */
  contentId: number;
  /** 跳过自动加载 */
  skipAutoLoad?: boolean;
  /** 外部传入的加载状态 */
  isLoading?: boolean;
  /** 是否应该开始加载 */
  shouldLoad?: boolean;
  /** 加载完成回调 */
  onLoadComplete?: () => void;
}

export function FlashcardsComponent({
  contentId,
  skipAutoLoad = false,
  isLoading: externalIsLoading = false,
  shouldLoad = false,
  onLoadComplete
}: FlashcardsComponentProps) {
  // UI状态
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);

  // 防止重复请求的 ref
  const hasTriggeredLoad = useRef(false);

  const { t } = useTranslation();

  // 缓存store - 使用稳定的选择器
  const cachedFlashcards = useArticleCacheStore((state) => state.flashcards);
  const setCachedFlashcards = useArticleCacheStore((state) => state.setFlashcards);

  // 获取当前内容的抽认卡
  const flashcards = cachedFlashcards[contentId] || [];

  // 当 shouldLoad 状态重置时，也重置防重复标记
  useEffect(() => {
    if (!shouldLoad) {
      hasTriggeredLoad.current = false;
    }
  }, [shouldLoad]);

  // 生成抽认卡的 mutation
  const generateFlashcardMutation = useGenerateFreshcard({
    onSuccess: (data) => {
      console.log(`✅ FlashcardsComponent mutation onSuccess: contentId=${contentId}`, data);
      if (data?.length > 0) {
        const flashcards: Flashcard[] = data.map((card: any, index: number) => ({
          id: `fc${index + 1}`,
          question: card.question || '',
          answer: card.answer || '',
          hint: card.hint || '',
          explanation: card.explanation || ''
        }));
        setCachedFlashcards(contentId, flashcards);
      }
      // 通知父组件加载完成
      onLoadComplete?.();
    },
    onError: (error) => {
      console.error('Failed to generate flashcards:', error);
      toast.error(t('article.flashcardsGenerationFailed'));
      // 即使失败也要通知父组件加载完成
      onLoadComplete?.();
    }
  });

  // 响应外部触发的加载请求
  useEffect(() => {
    if (shouldLoad && contentId && !hasTriggeredLoad.current && !generateFlashcardMutation.isPending) {
      console.log('🚀 FlashcardsComponent: First time triggering generation for contentId:', contentId);
      hasTriggeredLoad.current = true;
      generateFlashcardMutation.mutate({ contentId, data: [], reset: false });
    }
  }, [shouldLoad, contentId, generateFlashcardMutation]);

  // 计算加载状态 - 如果已经有数据，则不显示加载状态
  const isLoading = useMemo(() => {
    // 如果已经有flashcards数据，则不显示加载状态
    if (flashcards.length > 0) {
      return false;
    }
    return externalIsLoading || generateFlashcardMutation.isPending;
  }, [externalIsLoading, generateFlashcardMutation.isPending, flashcards.length]);

  // 重置卡片状态的辅助函数
  const resetCardState = useCallback(() => {
    setIsFlipped(false);
    setShowHint(false);
    setShowExplanation(false);
  }, []);

  // 当前卡片
  const currentCard = flashcards[currentIndex];

  // 卡片导航
  const nextCard = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % flashcards.length);
    resetCardState();
  }, [flashcards.length, resetCardState]);

  const prevCard = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + flashcards.length) % flashcards.length);
    resetCardState();
  }, [flashcards.length, resetCardState]);

  // 重置抽认卡
  const resetCards = useCallback(() => {
    setCurrentIndex(0);
    resetCardState();
    setCachedFlashcards(contentId, []);
    // 清除防重复标记，允许重新加载
    hasTriggeredLoad.current = false;
    // 直接发起新的请求
    if (contentId) {
      generateFlashcardMutation.mutate({ contentId, data: [], reset: true });
    }
  }, [contentId, setCachedFlashcards, resetCardState, generateFlashcardMutation]);

  // 切换提示
  const toggleHint = useCallback(() => {
    setShowHint((prev) => !prev);
  }, []);

  // 切换解释
  const toggleExplanation = useCallback(() => {
    setShowExplanation((prev) => !prev);
  }, []);

  // 翻转卡片
  const toggleFlip = useCallback(() => {
    setIsFlipped((prev) => !prev);
  }, []);

  // Loading状态
  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-gray-500 dark:text-gray-400">{t('common.generating')}</p>
        </div>
      </div>
    );
  }

  // 无数据状态
  if (flashcards.length === 0) {
    return (
      <div className="h-full flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 dark:bg-neutral-800 rounded-lg flex items-center justify-center mx-auto mb-4">
            <div className="w-8 h-8 bg-gray-300 dark:bg-neutral-600 rounded"></div>
          </div>
          <p className="text-gray-500 dark:text-gray-400">{t('article.flashcards.noCards')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold">{t('article.flashcards.title')}</h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">
            {t('article.flashcards.card')} {currentIndex + 1} {t('article.flashcards.of')} {flashcards.length}
          </span>
          <Button variant="outline" size="sm" onClick={resetCards}>
            <RotateCcw className="h-4 w-4 mr-1" />
            {t('article.flashcards.reset')}
          </Button>
        </div>
      </div>

      {/* Flashcard */}
      <div className="flex-1 flex items-center justify-center">
        <div className="relative items-center justify-center flex w-[90%]">
          {/* Hint Button */}
          {currentCard.hint && !isFlipped && (
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 left-4 z-10 h-8 w-8 p-0 rounded-full bg-white dark:bg-neutral-900 border-2 shadow-md"
              onClick={(e) => {
                e.stopPropagation();
                toggleHint();
              }}
            >
              <Lightbulb className={`h-4 w-4 ${showHint ? 'text-yellow-500' : 'text-gray-400'}`} />
            </Button>
          )}

          <div
            className="rounded-lg border text-card-foreground shadow-sm w-full bg-background min-h-64 aspect-[4/3] cursor-pointer relative transition-all duration-500 preserve-3d"
            style={{
              transformStyle: 'preserve-3d',
              transform: isFlipped ? 'rotateX(180deg)' : 'rotateX(0deg)',
              transformOrigin: 'center center'
            }}
            onClick={toggleFlip}
          >
            {/* Front side (Question) */}
            <div className="absolute inset-0 flex flex-col h-full p-6 text-center backface-hidden">
              <div className="flex-grow flex flex-col items-center justify-center">
                <div className="px-0 lg:px-6 text-base lg:text-lg">
                  <div>{currentCard.question}</div>
                </div>
                {currentCard.hint && showHint && (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800 mt-4">
                    <p className="text-sm text-yellow-800 dark:text-yellow-200">
                      <strong>{t('article.flashcards.hint')}:</strong> {currentCard.hint}
                    </p>
                  </div>
                )}
              </div>
              <div className="text-xs text-gray-400 mt-4">{t('article.flashcards.clickToFlip')}</div>
            </div>

            {/* Back side (Answer) */}
            <div
              className="absolute inset-0 flex flex-col h-full p-6 text-center"
              style={{
                transform: 'rotateX(180deg)',
                backfaceVisibility: 'hidden'
              }}
            >
              <div className="flex-grow flex flex-col items-center justify-center">
                <div className="px-0 lg:px-6 text-base lg:text-lg mb-4">
                  <div>{currentCard.answer}</div>
                </div>
                {currentCard.explanation && (
                  <div className="mt-4">
                    <Button variant="ghost" size="sm" onClick={(e) => {
                      e.stopPropagation();
                      toggleExplanation();
                    }}>
                      {showExplanation ? t('article.flashcards.hideExplanation') : t('article.flashcards.showExplanation')}
                    </Button>
                    {showExplanation && (
                      <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800 mt-2">
                        <p className="text-sm text-blue-800 dark:text-blue-200">{currentCard.explanation}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="text-xs text-gray-400 mt-4">{t('article.flashcards.clickToFlip')}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center mt-6">
        <Button variant="outline" onClick={prevCard} disabled={flashcards.length <= 1}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          {t('article.flashcards.previous')}
        </Button>
        
        {/* Progress indicator - show dots for ≤10 cards, page number for >10 cards */}
        <div className="flex items-center gap-2">
          {flashcards.length <= 10 ? (
            // Show dots for 10 or fewer cards
            <div className="flex gap-1">
              {flashcards.map((_, index) => (
                <button
                  key={index}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentIndex ? 'bg-primary' : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                  onClick={() => {
                    setCurrentIndex(index);
                    resetCardState();
                  }}
                />
              ))}
            </div>
          ) : (
            // Show page number for more than 10 cards
            <div className="text-sm text-gray-500 dark:text-gray-400 px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded">
              {currentIndex + 1} / {flashcards.length}
            </div>
          )}
        </div>
        
        <Button variant="outline" onClick={nextCard} disabled={flashcards.length <= 1}>
          {t('article.flashcards.next')}
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </div>
  );
}

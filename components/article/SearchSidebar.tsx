import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Search, ChevronUp, ChevronDown, X } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';

interface SearchResult {
  pageIndex: number;
  pageText: string;
  startIndex: number;
  endIndex: number;
}

interface SearchSidebarProps {
  searchPluginInstance: any; // Using any for now to avoid type issues
}

const SearchSidebar: React.FC<SearchSidebarProps> = ({ searchPluginInstance }) => {
  const { t } = useTranslation();
  
  // 搜索关键词
  const [keyword, setKeyword] = useState('');
  // 当前匹配
  const [currentMatch, setCurrentMatch] = useState(0);
  // 总匹配
  const [totalMatches, setTotalMatches] = useState(0);
  // 搜索结果
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  // 是否正在搜索
  const [isSearching, setIsSearching] = useState(false);

  // Debug: 检查插件实例
  useEffect(() => {
    console.log('Search plugin instance:', searchPluginInstance);
    console.log('Available methods:', Object.keys(searchPluginInstance || {}));
  }, [searchPluginInstance]);

  // Get search methods from plugin - 使用安全的方式获取方法
  const highlight = searchPluginInstance?.highlight;
  const clearHighlights = searchPluginInstance?.clearHighlights;
  const jumpToMatch = searchPluginInstance?.jumpToMatch;
  const jumpToNextMatch = searchPluginInstance?.jumpToNextMatch;
  const jumpToPreviousMatch = searchPluginInstance?.jumpToPreviousMatch;

  // Handle search
  const handleSearch = async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      if (clearHighlights) {
        clearHighlights();
      }
      setSearchResults([]);
      setTotalMatches(0);
      setCurrentMatch(0);
      return;
    }

    if (!highlight) {
      console.error('Search plugin highlight method not available');
      return;
    }

    setIsSearching(true);
    try {
      // Perform search with highlight function
      const matches = await highlight(searchTerm);
      console.log('Search matches:', matches);

      if (matches && matches.length > 0) {
        setSearchResults(matches);
        setTotalMatches(matches.length);
        setCurrentMatch(0);
      } else {
        setSearchResults([]);
        setTotalMatches(0);
        setCurrentMatch(0);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
      setTotalMatches(0);
      setCurrentMatch(0);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKeyword(value);
    handleSearch(value);
  };

  // Navigate to previous match
  const goToPreviousMatch = () => {
    if (totalMatches > 0 && jumpToPreviousMatch) {
      const newIndex = currentMatch > 0 ? currentMatch - 1 : totalMatches - 1;
      setCurrentMatch(newIndex);
      jumpToPreviousMatch();
    }
  };

  // Navigate to next match
  const goToNextMatch = () => {
    if (totalMatches > 0 && jumpToNextMatch) {
      const newIndex = currentMatch < totalMatches - 1 ? currentMatch + 1 : 0;
      setCurrentMatch(newIndex);
      jumpToNextMatch();
    }
  };

  // Clear search
  const clearSearch = () => {
    setKeyword('');
    if (clearHighlights) {
      clearHighlights();
    }
    setSearchResults([]);
    setTotalMatches(0);
    setCurrentMatch(0);
  };

  // Handle Enter key
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        goToPreviousMatch();
      } else {
        goToNextMatch();
      }
    }
  };

  // Extract context around the keyword
  const getMatchContext = (result: SearchResult, keyword: string) => {
    const { pageText, startIndex, endIndex } = result;

    // 获取关键词前后的文本
    const beforeText = pageText.substring(Math.max(0, startIndex - 50), startIndex);
    const matchedText = pageText.substring(startIndex, endIndex);
    const afterText = pageText.substring(endIndex, Math.min(pageText.length, endIndex + 50));

    // 清理文本，移除多余的空格和换行
    const cleanText = (text: string) => text.replace(/\s+/g, ' ').trim();

    return {
      before: cleanText(beforeText),
      match: cleanText(matchedText),
      after: cleanText(afterText)
    };
  };

  // Handle click on search result
  const handleResultClick = (index: number) => {
    setCurrentMatch(index);
    // Jump to specific match
    if (jumpToMatch) {
      console.log('Jumping to match:', index + 1);
      jumpToMatch(index + 1); // react-pdf-viewer uses 1-based indexing for jumpToMatch
    } else {
      console.error('jumpToMatch method not available');
    }
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-neutral-900 overflow-hidden">
      {/* Search Input */}
      <div className="p-3 border-b dark:border-neutral-700 flex-shrink-0">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="text"
            placeholder={t('article.content.search.placeholder') || 'Search'}
            value={keyword}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            className="flex h-12 pl-10 pr-10 w-full rounded-lg border bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-md transition-all duration-200 border-primary/10"
          />
          {keyword && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-neutral-800"
              onClick={clearSearch}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Search Results */}
      <ScrollArea className="flex-1 overflow-hidden">
        <div className="p-3">
          {/* Loading State */}
          {isSearching && (
            <div className="text-center py-6">
              <div className="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600 dark:border-gray-300 mb-2"></div>
              <p className="text-xs text-gray-600 dark:text-gray-400">{t('article.content.search.searching') || 'Searching...'}</p>
            </div>
          )}

          {/* No Results */}
          {keyword && !isSearching && searchResults.length === 0 && (
            <div className="text-center py-6 text-gray-500 dark:text-gray-400">
              <Search className="h-8 w-8 mx-auto mb-3 opacity-50" />
              <p className="text-xs">
                {t('article.content.search.noMatches') 
                  ? t('article.content.search.noMatches').replace('{keyword}', `"${keyword}"`)
                  : `No matches found for "${keyword}"`
                }
              </p>
              <p className="text-xs mt-1 opacity-75">{t('article.content.search.tryDifferent') || 'Try different keywords'}</p>
            </div>
          )}

          {/* Search Results List */}
          <div className="space-y-2">
            {searchResults.map((result, index) => {
              const context = getMatchContext(result, keyword);

              return (
                <div
                  key={index}
                  className={`p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-neutral-800 ${
                    index === currentMatch
                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                      : 'bg-gray-50 dark:bg-neutral-800/50'
                  }`}
                  onClick={() => handleResultClick(index)}
                >
                  <div className="text-xs text-gray-700 dark:text-gray-300 leading-relaxed break-words overflow-hidden">
                    {context.before && <span className="opacity-75">{context.before}</span>}
                    <span className="bg-yellow-200 dark:bg-yellow-600 text-black dark:text-white px-1 py-0.5 rounded font-medium">
                      {context.match}
                    </span>
                    {context.after && <span className="opacity-75">{context.after}</span>}
                  </div>
                  <div className="flex justify-end mt-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-neutral-700 px-2 py-0.5 rounded text-xs">
                      {t('article.content.search.page') || 'Page'} {result.pageIndex + 1}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};

export default SearchSidebar;

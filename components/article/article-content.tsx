'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  ChevronLeft,
  ChevronRight,
  Menu,
  Search,
  Moon,
  Share,
  Download as DownloadIcon,
  Maximize,
  Minimize,
  ChevronDown,
  Pencil,
  Check,
  PanelLeft,
  RotateCcwSquare
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { useTranslation } from '@/lib/translation-context';

// PDF Viewer imports
import { Viewer, Worker, ScrollMode, SpecialZoomLevel, RotateDirection } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import { toolbarPlugin } from '@react-pdf-viewer/toolbar';
import { searchPlugin } from '@react-pdf-viewer/search';
import { rotatePlugin } from '@react-pdf-viewer/rotate';
import { getFilePlugin } from '@react-pdf-viewer/get-file';
import { fullScreenPlugin } from '@react-pdf-viewer/full-screen';
import { pageNavigationPlugin } from '@react-pdf-viewer/page-navigation';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/search/lib/styles/index.css';
import '@react-pdf-viewer/full-screen/lib/styles/index.css';
import '@react-pdf-viewer/page-navigation/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';

// Import SearchSidebar
import SearchSidebar from './SearchSidebar';

interface ArticleContentProps {
  title: string;
  pdfUrl: string;
  onTitleChange?: (newTitle: string) => void;
}

export function ArticleContent({ title, pdfUrl, onTitleChange }: ArticleContentProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [workerUrl, setWorkerUrl] = useState<string>('');
  const [showSearchSidebar, setShowSearchSidebar] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isViewerRerendering, setIsViewerRerendering] = useState(false);
  const { t } = useTranslation();

  // Set up worker URL
  useEffect(() => {
    const getWorkerUrl = async () => {
      try {
        // Try to use local worker file first
        const response = await fetch('/pdf.worker.min.js');
        if (response.ok) {
          setWorkerUrl('/pdf.worker.min.js');
        } else {
          // Fallback to CDN
          setWorkerUrl('https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js');
        }
      } catch {
        // Fallback to CDN if local file is not accessible
        setWorkerUrl('https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js');
      }
    };
    getWorkerUrl();
  }, []);

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const newFullscreenState = !!document.fullscreenElement;
      if (newFullscreenState !== isFullscreen) {
        setIsViewerRerendering(true);
        setIsFullscreen(newFullscreenState);
        
        // 延迟隐藏loading，等待Viewer重新渲染完成
        setTimeout(() => {
          setIsViewerRerendering(false);
        }, 500);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [isFullscreen]);

  // 添加全屏模式的样式
  useEffect(() => {
    const styleId = 'pdf-fullscreen-styles';
    if (isFullscreen && !document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .pdf-viewer-container.fullscreen-mode {
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          width: 100vw !important;
          height: 100vh !important;
          z-index: 9999 !important;
          background: white !important;
        }
        
        .dark .pdf-viewer-container.fullscreen-mode {
          background: rgb(23 23 23) !important;
        }
        
        .pdf-viewer-container.fullscreen-mode .rpv-core__viewer {
          height: calc(100vh - 64px) !important;
        }
      `;
      document.head.appendChild(style);
    } else if (!isFullscreen) {
      const existingStyle = document.getElementById(styleId);
      if (existingStyle) {
        existingStyle.remove();
      }
    }

    return () => {
      if (!isFullscreen) {
        const existingStyle = document.getElementById(styleId);
        if (existingStyle) {
          existingStyle.remove();
        }
      }
    };
  }, [isFullscreen]);

  // 自定义全屏切换函数
  const handleFullscreenToggle = useCallback(async () => {
    try {
      if (document.fullscreenElement) {
        // 退出全屏
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).mozCancelFullScreen) {
          await (document as any).mozCancelFullScreen();
        } else if ((document as any).msExitFullscreen) {
          await (document as any).msExitFullscreen();
        }
      } else {
        // 进入全屏 - 对整个PDF容器进行全屏，包括工具栏
        const element = document.querySelector('.pdf-viewer-container') as HTMLElement;
        if (element) {
          if (element.requestFullscreen) {
            await element.requestFullscreen();
          } else if ((element as any).webkitRequestFullscreen) {
            await (element as any).webkitRequestFullscreen();
          } else if ((element as any).mozRequestFullScreen) {
            await (element as any).mozRequestFullScreen();
          } else if ((element as any).msRequestFullscreen) {
            await (element as any).msRequestFullscreen();
          }
        }
      }
    } catch (error) {
      console.warn('全屏切换失败:', error);
      // 可以在这里添加用户提示
    }
  }, []);

  // 搜索
  const searchPluginInstance = searchPlugin();
  const { ShowSearchPopover } = searchPluginInstance;

  // 旋转
  const rotatePluginInstance = rotatePlugin();
  const { RotateForwardButton, Rotate } = rotatePluginInstance;

  // 下载
  const getFilePluginInstance = getFilePlugin();
  const { Download } = getFilePluginInstance;

  // 全屏
  const fullScreenPluginInstance = fullScreenPlugin({
    // 移除可能导致问题的配置
    // enableShortcuts: false,
    // getFullScreenTarget: (pagesContainer: HTMLElement) => pagesContainer,
    // renderExitFullScreenButton: (props: any) => (
    //   <Button
    //     variant="ghost"
    //     onClick={props.onClick}
    //     size="sm"
    //     className="h-6 w-6 sm:h-8 sm:w-8 p-0"
    //     title="Exit Full Screen"
    //   >
    //     <Maximize className="h-3 w-3 sm:h-4 sm:w-4" />
    //   </Button>
    // ),
    // 暂时移除事件处理器，避免加载问题
    // onEnterFullScreen: (zoom) => {
    //   zoom(SpecialZoomLevel.PageFit);
    // },
    // onExitFullScreen: (zoom) => {
    //   zoom(SpecialZoomLevel.PageFit);
    // }
  });
  const { EnterFullScreen } = fullScreenPluginInstance;

  // 翻页
  const pageNavigationPluginInstance = pageNavigationPlugin();
  const { GoToNextPage, GoToPreviousPage } = pageNavigationPluginInstance;

  const defaultLayoutPluginInstance = defaultLayoutPlugin({
    sidebarTabs: () => []
  });

  const handleDocumentLoad = useCallback((e: any) => {
    setTotalPages(e.doc.numPages);
  }, []);

  const handlePageChange = useCallback((e: any) => {
    setCurrentPage(e.currentPage + 1); // react-pdf-viewer uses 0-based indexing
  }, []);

  // 自定义 loader 渲染函数
  const renderCustomLoader = (percentages: number) => {
    return (
      <div className="absolute inset-0 flex items-center justify-center bg-white dark:bg-neutral-900">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600 dark:border-gray-300 mb-2"></div>
          <p className="text-sm text-gray-600 dark:text-gray-300">Loading PDF... {Math.round(percentages)}%</p>
        </div>
      </div>
    );
  };

  // 添加调试信息
  useEffect(() => {
    console.log('Fullscreen state:', isFullscreen);
    console.log('Search sidebar state:', showSearchSidebar);
    console.log('Should show sidebar in fullscreen:', showSearchSidebar && isFullscreen);
  }, [isFullscreen, showSearchSidebar]);

  return (
    <div className={`flex flex-col bg-white dark:bg-neutral-900 h-full dark:border-neutral-700 border rounded-lg pdf-viewer-container ${isFullscreen ? 'fullscreen-mode' : ''}`}>
      {/* Enhanced Toolbar - 始终在顶部，独立于侧边栏 */}
      <header className={`px-2 sm:px-3 md:px-4 py-2 md:py-3 border-b dark:border-neutral-700 bg-gray-50 dark:bg-neutral-800/50 flex-shrink-0  rounded-t-lg${
        isFullscreen ? `fixed top-0 ${showSearchSidebar ? 'left-80' : 'left-0'} right-0 z-40 bg-gray-50/95 dark:bg-neutral-800/95 backdrop-blur-sm` : ''
      }`}>
        <div className="relative flex items-center justify-between">
          {/* Left Controls */}
          <div className="flex items-center gap-1">
            {/* <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 sm:h-8 sm:w-8 p-0"
              title={t('article.content.toolbar.menu')}
            >
              <PanelLeft className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button> */}
            {/* 搜索 - 全屏模式下也可用 */}
            <Button
              variant={showSearchSidebar ? "default" : "ghost"}
              size="sm"
              className="h-6 w-6 sm:h-8 sm:w-8 p-0"
              title={t('article.content.toolbar.search')}
              onClick={() => {
                setIsViewerRerendering(true);
                setShowSearchSidebar(!showSearchSidebar);
                
                // 延迟隐藏loading，等待布局调整完成
                setTimeout(() => {
                  setIsViewerRerendering(false);
                }, 300);
              }}
            >
              <Search className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            {/* 主题切换 */}
            {/* <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 sm:h-8 sm:w-8 p-0"
              title={t('article.content.toolbar.darkMode')}
            >
              <Moon className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button> */}
          </div>

          {/* 翻页 - 使用绝对定位居中 */}
          <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-1">
            <GoToPreviousPage>
              {(props: any) => (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 sm:h-8 px-1 sm:px-2"
                  onClick={props.onClick}
                  disabled={props.isDisabled}
                >
                  <ChevronLeft className="h-2 w-2 sm:h-3 sm:w-3" />
                </Button>
              )}
            </GoToPreviousPage>
            <div className="bg-white dark:bg-neutral-700 px-1 sm:px-2 py-0.5 sm:py-1 rounded-md border text-xs font-medium">
              {currentPage} / {totalPages || '-'}
            </div>
            <GoToNextPage>
              {(props: any) => (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 sm:h-8 px-1 sm:px-2"
                  onClick={props.onClick}
                  disabled={props.isDisabled}
                >
                  <ChevronRight className="h-2 w-2 sm:h-3 sm:w-3" />
                </Button>
              )}
            </GoToNextPage>
          </div>

          {/* Right Controls */}
          <div className="flex items-center gap-1">
            {/* 旋转 */}
            <Rotate direction={RotateDirection.Backward}>
              {(props: any) => (
                <Button
                  variant="ghost"
                  onClick={props.onClick}
                  size="sm"
                  className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                  title="Rotate PDF"
                >
                  <RotateCcwSquare className="h-3 w-3 sm:h-4 sm:w-4" />
                </Button>
              )}
            </Rotate>
            {/* 下载 */}
            <Download>
              {(props: any) => (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                  title={t('article.content.toolbar.download')}
                  onClick={props.onClick}
                >
                  <DownloadIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                </Button>
              )}
            </Download>

            {/* 使用自定义全屏实现 */}
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 sm:h-8 sm:w-8 p-0"
              title={isFullscreen ? t('article.content.toolbar.exitFullscreen') || 'Exit Fullscreen' : t('article.content.toolbar.maximize')}
              onClick={handleFullscreenToggle}
            >
              {isFullscreen ? (
                <Minimize className="h-3 w-3 sm:h-4 sm:w-4" />
              ) : (
                <Maximize className="h-3 w-3 sm:h-4 sm:w-4" />
              )}
            </Button>

            {/* 原始的 EnterFullScreen 按钮 - 保留作为备选 */}
            {/* <EnterFullScreen>
              {(props: any) => (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                  title={t('article.content.toolbar.maximize')}
                  onClick={props.onClick}
                >
                  <Maximize className="h-3 w-3 sm:h-4 sm:w-4" />
                </Button>
              )}
            </EnterFullScreen> */}
          </div>
        </div>
      </header>

      {/* Content Area - 工具栏下方的内容区域 */}
      <div className={`flex-1 flex ${isFullscreen ? 'fullscreen-mode' : ''}`}>
        {/* Search Sidebar - 非全屏模式 */}
        {showSearchSidebar && !isFullscreen && (
          <div className="max-w-[60%] w-80 min-w-64 border-r dark:border-neutral-700 flex-shrink-0">
            <SearchSidebar searchPluginInstance={searchPluginInstance} />
          </div>
        )}

        {/* Search Sidebar - 全屏模式 */}
        {showSearchSidebar && isFullscreen && (
          <div className="fixed left-0 top-16 bottom-0 w-80 bg-white dark:bg-neutral-900 border-r dark:border-neutral-700 shadow-lg z-50">
            <SearchSidebar searchPluginInstance={searchPluginInstance} />
          </div>
        )}

        {/* PDF Viewer */}
        <div className={`flex-1 relative overflow-auto ${isFullscreen ? 'pt-16' : ''} ${isFullscreen && showSearchSidebar ? 'ml-80' : ''}`}>
          {/* Loading遮罩 - 在Viewer重新渲染时显示 */}
          {isViewerRerendering && pdfUrl && workerUrl && (
            <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm z-10">
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600 dark:border-gray-300 mb-2"></div>
                <p className="text-sm text-gray-600 dark:text-gray-300">Adjusting layout...</p>
              </div>
            </div>
          )}

          {pdfUrl && workerUrl ? (
            <Worker workerUrl={workerUrl}>
              <div className="h-full flex flex-col w-full">
                {/* PDF Viewer */}
                <div className="flex-1 relative w-full">
                  <div className={`w-full ${isFullscreen ? 'h-screen' : 'h-[calc(100vh-120px)] lg:h-[calc(100vh-140px)]'}`}>
                    <Viewer
                      fileUrl={pdfUrl}
                      defaultScale={SpecialZoomLevel.PageWidth}
                      plugins={[
                        searchPluginInstance,
                        rotatePluginInstance,
                        getFilePluginInstance,
                        pageNavigationPluginInstance,
                        fullScreenPluginInstance
                      ]}
                      onDocumentLoad={handleDocumentLoad}
                      onPageChange={handlePageChange}
                      scrollMode={ScrollMode.Vertical}
                      renderLoader={renderCustomLoader}
                    />
                  </div>
                </div>
              </div>
            </Worker>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600 dark:border-gray-300 mb-2"></div>
                <p className="text-sm text-gray-600 dark:text-gray-300">Loading PDF viewer...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
export default ArticleContent;
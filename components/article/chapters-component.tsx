"use client"
import { useState, useEffect, useRef } from 'react'
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Loader2 } from 'lucide-react'
import { useTranslation } from "@/lib/translation-context"
import { useGenerateChapter } from '@/lib/api/hooks'
import { useArticleCacheStore } from '@/lib/stores/article-cache-store'
import { toast } from 'sonner'

interface ChapterSection {
  id: string
  pageNumber: number
  title: string
  content: string
}

interface ChaptersComponentProps {
  contentId: number
  skipAutoLoad?: boolean;
  isLoading?: boolean;
  shouldLoad?: boolean;
  onLoadComplete?: () => void;
}

export function ChaptersComponent({ 
  contentId, 
  skipAutoLoad = false, 
  isLoading = false,
  shouldLoad = false,
  onLoadComplete
}: ChaptersComponentProps) {
  // 防止重复请求的 ref
  const hasTriggeredLoad = useRef(false);
  const { t } = useTranslation()

  // 使用缓存store
  const {
    chapters: cachedChapters,
    setChapters: setCachedChapters
  } = useArticleCacheStore()

  // 获取当前内容的章节
  const sections = cachedChapters[contentId] || []

  // 当 shouldLoad 状态重置时，也重置防重复标记
  useEffect(() => {
    if (!shouldLoad) {
      hasTriggeredLoad.current = false;
    }
  }, [shouldLoad]);

  // 生成章节的 mutation
  const generateChapterMutation = useGenerateChapter({
    onSuccess: (data) => {
      if (data?.data) {
        // 将章节转换为 ChaptersComponent 所需的 sections 格式
        const sections = data.data.map((chapter: any, index: number) => ({
          id: `s${chapter.id || index + 1}`,
          pageNumber: index + 1,
          title: chapter.heading || `Chapter ${index + 1}`,
          content: chapter.summary || '章节内容总结'
        }))
        // 保存到缓存store
        setCachedChapters(contentId, sections)
      }
      // 通知父组件加载完成
      onLoadComplete?.();
    },
    onError: (error) => {
      console.error('Failed to generate chapters:', error)
      toast.error(t('article.chaptersGenerationFailed'))
      // 即使失败也要通知父组件加载完成
      onLoadComplete?.();
    }
  })

  // 响应外部触发的加载请求
  useEffect(() => {
    if (shouldLoad && contentId && !hasTriggeredLoad.current && !generateChapterMutation.isPending) {
      console.log('🚀 ChaptersComponent: First time triggering generation for contentId:', contentId);
      hasTriggeredLoad.current = true;
      generateChapterMutation.mutate({ contentId });
    }
  }, [shouldLoad, contentId, generateChapterMutation]);

  // Loading状态 - 只使用外部传入的loading状态
  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-gray-500 dark:text-gray-400">{t('common.generating')}</p>
        </div>
      </div>
    )
  }

  if (sections.length === 0) {
    return (
      <div className="h-full flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400">{t('article.chapters.noChapters')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      <ScrollArea className="flex-1 px-4 py-6">
        <div className="space-y-8">
          {sections.map((section) => (
            <div key={section.id} className="space-y-4">
              {/* Page Number Badge */}
              <Badge variant="secondary" className="bg-gray-100 text-gray-600 text-xs px-2 py-1">
                {t('article.chapters.page')} {section.pageNumber}
              </Badge>

              {/* Section Title */}
              <h2 className="text-lg font-bold text-gray-900 dark:text-white leading-tight">{section.title}</h2>

              {/* Section Content */}
              <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">{section.content}</p>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}

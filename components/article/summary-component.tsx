/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-24 14:41:40
 * @FilePath: /tutoro-ai-front/components/article/summary-component.tsx
 */
'use client';
import { useState, useEffect, useRef } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { useGenerateSummary } from '@/lib/api/hooks';
import { useArticleCacheStore } from '@/lib/stores/article-cache-store';
import { toast } from 'sonner';
import MarkdownPreview from '@uiw/react-markdown-preview';
import { useTheme } from 'next-themes';
import { isDarkMode } from '@/lib/utils/theme';

interface SummaryComponentProps {
  contentId: number;
  skipAutoLoad?: boolean;
  isLoading?: boolean;
  shouldLoad?: boolean;
  onLoadComplete?: () => void;
}

export function SummaryComponent({
  contentId,
  skipAutoLoad = false,
  isLoading = false,
  shouldLoad = false,
  onLoadComplete
}: SummaryComponentProps) {
  // 防止重复请求的 ref
  const hasTriggeredLoad = useRef(false);
  const { t } = useTranslation();
  const { theme } = useTheme();

  // 使用缓存store
  const { summaries: cachedSummaries, setSummary: setCachedSummary } = useArticleCacheStore();

  // 获取当前内容的摘要
  const summaryHtml = cachedSummaries[contentId] || '';

  // 当 shouldLoad 或 contentId 变化时，重置防重复标记
  useEffect(() => {
    if (!shouldLoad) {
      hasTriggeredLoad.current = false;
    }
  }, [shouldLoad]);

  // 生成摘要的 mutation
  const generateSummaryMutation = useGenerateSummary({
    onSuccess: (data) => {
      console.log('Summary generated successfully:', data);
      if (data?.data?.content) {
        // 保存到缓存store
        setCachedSummary(contentId, data.data.content);
      }
      // 通知父组件加载完成
      onLoadComplete?.();
    },
    onError: (error) => {
      console.error('Failed to generate summary:', error);
      toast.error(t('article.summaryGenerationFailed'));
      // 即使失败也要通知父组件加载完成
      onLoadComplete?.();
    }
  });

  // 响应外部触发的加载请求
  useEffect(() => {
    if (shouldLoad && contentId && !hasTriggeredLoad.current && !generateSummaryMutation.isPending) {
      console.log('🚀 SummaryComponent: First time triggering generation for contentId:', contentId);
      hasTriggeredLoad.current = true;
      generateSummaryMutation.mutate({ contentId });
    }
  }, [shouldLoad, contentId, generateSummaryMutation]);

  return (
    <div className="h-full flex flex-col">
      {/* Header Controls */}
      <div className="flex justify-between items-center p-4 border-b dark:border-neutral-700">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">{t('article.summary.title')}:</span>
        </div>
      </div>

      {/* Summary Content */}
      {isLoading ? (
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-muted-foreground">{t('common.generating')}</p>
          </div>
        </div>
      ) : (
        <ScrollArea className="flex-1 p-4">
          {summaryHtml ? (
            <MarkdownPreview
              source={summaryHtml}
              style={{
                backgroundColor: 'transparent',
                fontSize: '14px'
              }}
              wrapperElement={{
                'data-color-mode': isDarkMode(theme) ? 'dark' : 'light'
              }}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-muted-foreground">{t('article.summary.noSummary')}</p>
            </div>
          )}
        </ScrollArea>
      )}
    </div>
  );
}

'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ChevronLeft, SlidersHorizontal, RotateCcw, Loader2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { QuizQuestionDisplay } from '@/components/quiz/quiz-question-display';
import { QuizFeedbackBox } from '@/components/quiz/quiz-feedback-box';
import { QuizNavigationFooter } from '@/components/quiz/quiz-navigation-footer';
import { useTranslation } from '@/lib/translation-context';
import type { GeneratedQuiz, QuizQuestion } from '@/types';

interface QuizTakingProps {
  activeQuiz: GeneratedQuiz;
  currentQuestionIndex: number;
  currentQuestion: QuizQuestion;
  userAnswers: Record<string, string>;
  questionResults: Record<string, boolean | null>;
  isAnswered: boolean;
  isCorrect: boolean;
  isAnswerSubmitting: boolean;
  onBackToList: () => void;
  onAnswerSelect: (answer: string) => void;
  onResetQuestion: () => void;
  onSkipQuestion: () => void;
  onNextQuestion: () => void;
  onResetAllQuizzes: () => void;
  onOpenSettings: () => void;
}

export function QuizTakingComponent({
  activeQuiz,
  currentQuestionIndex,
  currentQuestion,
  userAnswers,
  questionResults,
  isAnswered,
  isCorrect,
  isAnswerSubmitting,
  onBackToList,
  onAnswerSelect,
  onResetQuestion,
  onSkipQuestion,
  onNextQuestion,
  onResetAllQuizzes,
  onOpenSettings
}: QuizTakingProps) {
  const { t } = useTranslation();

  const progressPercentage =
    activeQuiz.questions.length > 0 ? ((currentQuestionIndex + 1) / activeQuiz.questions.length) * 100 : 0;

  return (
    <div className="flex flex-col h-full relative">
      {isAnswerSubmitting && (
        <div className="absolute inset-0 bg-black/5 dark:bg-black/10 backdrop-blur-[1px] z-10 flex items-center justify-center">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
        </div>
      )}
      
      <div className="p-3 md:p-4 border-b dark:border-neutral-800 flex items-center justify-between">
        <Button
          variant="ghost"
          size="sm"
          className="text-muted-foreground text-xs md:text-sm"
          onClick={onBackToList}
        >
          <ChevronLeft className="h-4 w-4 md:h-5 md:w-5 mr-1" />
          {t('article.quiz.taking.back')}
        </Button>
        
        <div className="flex-1 flex items-center justify-center px-2 md:px-4">
          <span className="text-xs md:text-sm text-muted-foreground mr-2">{currentQuestionIndex + 1}</span>
          <Progress
            value={progressPercentage}
            className="w-full max-w-[150px] md:max-w-[200px] h-1 md:h-1.5 bg-gray-200 dark:bg-neutral-700 [&>div]:bg-green-500"
          />
          <span className="text-xs md:text-sm text-muted-foreground ml-2">{activeQuiz.questions.length}</span>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8 md:h-9 md:w-9">
              <SlidersHorizontal className="h-4 w-4 md:h-5 md:w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 md:w-64 p-3 md:p-4 space-y-3 md:space-y-4">
            <DropdownMenuItem
              onSelect={onResetAllQuizzes}
              className="cursor-pointer p-2 hover:bg-accent rounded-md flex items-center gap-2 text-sm"
            >
              <RotateCcw className="w-3.5 h-3.5 md:w-4 md:h-4" /> {t('article.quiz.list.restartAll')}
            </DropdownMenuItem>
            <DropdownMenuItem
              onSelect={onOpenSettings}
              className="cursor-pointer p-2 hover:bg-accent rounded-md text-sm"
            >
              {t('article.quiz.preferences')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex-1 overflow-y-auto py-3 md:py-6 pl-3 md:pl-6 space-y-4 pr-2 md:space-y-6">
        <div className="flex justify-between items-start">
          <QuizQuestionDisplay
            question={currentQuestion}
            userAnswer={userAnswers[currentQuestion.id]}
            isAnswered={isAnswered}
            isCorrect={isCorrect}
            onAnswerSelect={onAnswerSelect}
            questionNumber={currentQuestionIndex + 1}
            totalQuestions={activeQuiz.questions.length}
          />
        </div>
        {isAnswered && (
          <QuizFeedbackBox
            isCorrect={isCorrect}
            isSkipped={questionResults[currentQuestion.id] === null}
            explanation={currentQuestion.explanation}
            correctAnswer={currentQuestion.correctAnswer}
            pageReference={currentQuestion.pageReference}
          />
        )}
      </div>

      <QuizNavigationFooter
        onResetQuestion={onResetQuestion}
        onSkipQuestion={onSkipQuestion}
        onNextQuestion={onNextQuestion}
        isAnswered={isAnswered}
      />
    </div>
  );
} 
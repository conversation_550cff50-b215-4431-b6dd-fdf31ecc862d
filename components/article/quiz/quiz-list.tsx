'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { RotateCcw, SlidersHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { useTranslation } from '@/lib/translation-context';
import type { GeneratedQuiz } from '@/types';

interface QuizListProps {
  quizzes: GeneratedQuiz[];
  onStartQuiz: (quiz: GeneratedQuiz) => void;
  onResetAllQuizzes: () => void;
  onResetSpecificQuiz: (quiz: GeneratedQuiz) => void;
  onOpenSettings: () => void;
}

export function QuizListComponent({
  quizzes,
  onStartQuiz,
  onResetAllQuizzes,
  onResetSpecificQuiz,
  onOpenSettings
}: QuizListProps) {
  const { t } = useTranslation();

  return (
    <div className="p-3 md:p-4 flex flex-col h-full">
      <div className="flex justify-between items-center mb-3 md:mb-4">
        <h2 className="text-base md:text-lg font-semibold">{t('article.quiz.list.title')}</h2>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8 md:h-9 md:w-9">
              <SlidersHorizontal className="h-4 w-4 md:h-5 md:w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 md:w-64 p-3 md:p-4 space-y-3 md:space-y-4">
            <DropdownMenuItem
              onSelect={onResetAllQuizzes}
              className="cursor-pointer p-2 hover:bg-accent rounded-md flex items-center gap-2 text-sm"
            >
              <RotateCcw className="w-3.5 h-3.5 md:w-4 md:h-4" /> {t('article.quiz.list.restartAll')}
            </DropdownMenuItem>
            <DropdownMenuItem
              onSelect={onOpenSettings}
              className="cursor-pointer p-2 hover:bg-accent rounded-md text-sm"
            >
              {t('article.quiz.preferences')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {quizzes.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <p className="text-muted-foreground text-sm md:text-base">{t('article.quiz.list.noQuizzes')}</p>
        </div>
      ) : (
        <div className="space-y-2 md:space-y-3 overflow-y-auto">
          {quizzes.map((quiz) => (
            <Card key={quiz.id} className="cursor-pointer hover:shadow-md" onClick={() => onStartQuiz(quiz)}>
              <CardContent className="p-3 md:p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-xs text-muted-foreground">{quiz.title}</p>
                    <h3 className="font-semibold text-sm">{quiz.description}</h3>
                    {quiz.questions.length === 0 && (
                      <p className="text-xs text-muted-foreground mt-1">{t('article.quiz.list.clickToStart')}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5 md:h-6 md:w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        onResetSpecificQuiz(quiz);
                      }}
                    >
                      <RotateCcw className="h-2.5 w-2.5 md:h-3 md:w-3" />
                    </Button>
                    <div
                      className={`w-2.5 h-2.5 md:w-3 md:h-3 rounded-full border-2 ${
                        quiz.progress === 100 ? 'bg-green-500 border-green-500' : 'border-muted-foreground'
                      }`}
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2 mt-2">
                  <Progress value={quiz.progress} className="h-1 md:h-1.5 flex-1" />
                  <span className="text-xs text-muted-foreground">{quiz.progress.toFixed(0)}%</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
} 
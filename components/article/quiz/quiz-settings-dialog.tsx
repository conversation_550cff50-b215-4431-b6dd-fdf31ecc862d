'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Star, Check, FileText, Loader2 } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
export type QuizType = 'multiple-choice' | 'free-response';
export type QuizDifficulty = 'easy' | 'medium' | 'hard';

export interface QuizPreferences {
  types: QuizType[];
  difficulty: QuizDifficulty;
}

interface QuizSettingsDialogProps {
  isOpen: boolean;
  preferences: QuizPreferences;
  onPreferencesChange: (preferences: QuizPreferences) => void;
  onClose: () => void;
  onRegenerate: () => void;
  isGenerating: boolean;
}

export function QuizSettingsDialog({
  isOpen,
  preferences,
  onPreferencesChange,
  onClose,
  onRegenerate,
  isGenerating
}: QuizSettingsDialogProps) {
  const { t } = useTranslation();

  const handleTypeToggle = (type: QuizType) => {
    const newTypes = preferences.types.includes(type) 
      ? preferences.types.filter((t) => t !== type) 
      : [...preferences.types, type];
    onPreferencesChange({ ...preferences, types: newTypes });
  };

  const handleDifficultyChange = (difficulty: QuizDifficulty) => {
    onPreferencesChange({ ...preferences, difficulty });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">{t('article.quiz.settings.title')}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* 题型选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('article.quiz.questionTypes')}</label>
            <div className="grid grid-cols-2 gap-2">
              {(
                [
                  { type: 'multiple-choice', label: t('article.quiz.multipleChoice'), icon: Check },
                  { type: 'free-response', label: t('article.quiz.freeResponse'), icon: FileText }
                ] as const
              ).map(({ type, label, icon: Icon }) => (
                <Button
                  key={type}
                  variant={preferences.types.includes(type) ? 'default' : 'outline'}
                  onClick={() => handleTypeToggle(type)}
                  className="h-10 text-xs md:text-sm flex items-center justify-center gap-2"
                >
                  <Icon className="w-3.5 h-3.5 md:w-4 md:h-4" />
                  {label}
                </Button>
              ))}
            </div>
          </div>

          {/* 难度选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('article.quiz.difficulty')}</label>
            <div className="grid grid-cols-3 gap-2">
              {(['easy', 'medium', 'hard'] as QuizDifficulty[]).map((diff, i) => (
                <Button
                  key={diff}
                  variant={preferences.difficulty === diff ? 'default' : 'outline'}
                  onClick={() => handleDifficultyChange(diff)}
                  className="h-10 text-xs md:text-sm flex items-center justify-center gap-1"
                >
                  {Array(i + 1)
                    .fill(0)
                    .map((_, starIdx) => (
                      <Star
                        key={starIdx}
                        className={`w-2.5 h-2.5 md:w-3 md:h-3 ${
                          preferences.difficulty === diff
                            ? 'text-yellow-400 fill-yellow-400'
                            : 'text-muted-foreground'
                        }`}
                      />
                    ))}
                  {t(`article.quiz.${diff}`)}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter className="flex-col space-y-2 sm:flex-col sm:space-y-2 sm:space-x-0">
          <Button
            onClick={onRegenerate}
            className="w-full bg-gray-800 hover:bg-gray-900 dark:bg-gray-100 dark:hover:bg-gray-200 dark:text-gray-800 text-white"
            disabled={isGenerating || preferences.types.length === 0}
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {t('article.quiz.generating')}
              </>
            ) : (
              t('article.quiz.settings.regenerate')
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
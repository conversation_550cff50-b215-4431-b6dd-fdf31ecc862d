'use client';
import { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChatMessageItem } from '@/components/chat/chat-message-item';
import { ChatInputArea } from '@/components/chat/chat-input-area';
import { ChatLoadingIndicator } from '@/components/chat/chat-loading-indicator';
import { CollectNoteModal } from '@/components/notes/collect-note-modal';
import { MessageCircle, HelpCircle, Brain, MicIcon, Layers, Search, Calendar, Plus } from 'lucide-react';
import { postLlmChatContentId } from '@/servers/api/wendangneirongliaotian';
import { postLlmChatContentIdStream } from '@/lib/api/streaming';
import { getContentNewSessionId } from '@/servers/api/wendangneirong';
import { useAppDataStore, type Model } from '@/lib/stores/app-data-store';
import type { ChatMessage } from '@/types';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import { saveChatToStorage, loadChatFromStorage, clearChatFromStorage } from '@/lib/utils/chat-storage';
import { useAppStore } from '@/lib/stores';
import { useArticleCacheStore } from '@/lib/stores/article-cache-store';

interface ChatComponentProps {
  contentId: number;
  initialInputValue?: string;
  initialSelectedModelId?: string;
  autoSend?: boolean;
  onAutoSendComplete?: () => void;
  contentData?: any; // 添加 contentData 用于判断内容类型
  onContentRefresh?: () => void; // 添加内容刷新回调
}

export function ChatComponent({
  contentId,
  initialInputValue,
  initialSelectedModelId,
  autoSend = false,
  onAutoSendComplete,
  contentData,
  onContentRefresh
}: ChatComponentProps) {
  const { t } = useTranslation();
  const { models } = useAppDataStore();
  const { clearAutoSendChatData } = useArticleCacheStore();

  // 直接订阅 store 中的 autoSendChatData 变化
  const autoSendChatData = useArticleCacheStore((state) => state.autoSendChatData[contentId]);

  // 备用方案：手动检查 store 数据
  const [manualCheckTrigger, setManualCheckTrigger] = useState(0);

  // 添加调试日志来跟踪订阅的数据变化
  useEffect(() => {
    console.log('📡 ChatComponent: autoSendChatData changed:', {
      contentId,
      autoSendChatData
    });
  }, [contentId, autoSendChatData]);

  // 定期检查 store 中的数据（备用方案）
  useEffect(() => {
    const interval = setInterval(() => {
      const storeData = useArticleCacheStore.getState().autoSendChatData[contentId];
      if (storeData && !autoSendChatData) {
        console.log('🔄 Manual check found data in store:', storeData);
        setManualCheckTrigger(prev => prev + 1);
      }
    }, 100);

    return () => clearInterval(interval);
  }, [contentId, autoSendChatData]);

  // 基础状态
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // UI 状态
  const [isCollectModalOpen, setIsCollectModalOpen] = useState(false);
  const [selectedText, setSelectedText] = useState<string>('');
  const [showGlobe, setShowGlobe] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<Array<{ id: number; name: string; size: number }>>([]);

  const { openUpgradeModal } = useAppStore();
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // 从本地存储加载聊天记录
  useEffect(() => {
    if (contentId) {
      const storedMessages = loadChatFromStorage('content', contentId);
      setMessages(storedMessages);
    }
  }, [contentId]);

  // 保存聊天记录到本地存储
  useEffect(() => {
    if (contentId && messages.length > 0) {
      saveChatToStorage('content', contentId, messages);
    }
  }, [contentId, messages]);

  // 创建新聊天 - 清空聊天记录和本地存储
  const handleNewChat = useCallback(async () => {
    try {
      // 先调用新建会话接口
      await getContentNewSessionId({ id: contentId });

      // 清空聊天记录和本地存储
      setMessages([]);
      setInputValue('');
      if (contentId) {
        clearChatFromStorage('content', contentId);
      }
    } catch (error: any) {
      console.error('New session creation failed:', error);
      toast.error(error?.msg || error?.message || t('common.error'));
    }
  }, [contentId, t]);

  // 移除了初始化逻辑，现在由 HybridAuthProvider 统一管理

  // 简化的模型设置逻辑
  useEffect(() => {
    if (models.length > 0 && !selectedModel) {
      // 优先检查 store 中的自动发送数据
      const modelIdToUse = autoSendChatData?.initialSelectedModelId || initialSelectedModelId;

      if (modelIdToUse) {
        const foundModel = models.find(
          (m) => m.id?.toString() === modelIdToUse || m.name === modelIdToUse
        );
        setSelectedModel(foundModel || models[0]);
      } else {
        setSelectedModel(models[0]);
      }
    }
  }, [models, selectedModel, initialSelectedModelId, autoSendChatData]);

  // 优化的自动发送逻辑 - 使用 store 中的统一配置
  useEffect(() => {
    // 使用订阅的 autoSendChatData，如果没有则手动检查 store
    let autoSendData = autoSendChatData;

    // 备用方案：如果订阅没有数据，直接从 store 获取
    if (!autoSendData) {
      autoSendData = useArticleCacheStore.getState().autoSendChatData[contentId];
    }

    console.log('🔍 Auto-send check:', {
      contentId,
      autoSendData,
      autoSendChatDataFromSubscription: autoSendChatData,
      selectedModel: selectedModel?.name,
      isLoading,
      messagesLength: messages.length,
      manualCheckTrigger
    });

    if (autoSendData?.autoSend && selectedModel && !isLoading && messages.length === 0) {
      console.log('🚀 Auto-send triggered with store data:', autoSendData);

      const timer = setTimeout(async () => {
        try {
          await handleSendMessage(
            autoSendData.initialInputValue,
            selectedModel,
            autoSendData
          );

          // 清除自动发送数据
          clearAutoSendChatData(contentId);
          onAutoSendComplete?.();
        } catch (error) {
          console.error('Auto-send failed:', error);
          clearAutoSendChatData(contentId);
          onAutoSendComplete?.();
        }
      }, 100);

      return () => clearTimeout(timer);
    }

    // 兼容旧的 props 方式（向后兼容）
    const shouldAutoSend = autoSend && initialInputValue && selectedModel && !isLoading && messages.length === 0;

    if (shouldAutoSend && !autoSendData) {
      console.log('🚀 Auto-send triggered with props (legacy mode)');

      const timer = setTimeout(async () => {
        try {
          await handleSendMessage(initialInputValue, selectedModel);
          onAutoSendComplete?.();
        } catch (error) {
          console.error('Auto-send failed:', error);
          onAutoSendComplete?.();
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [contentId, autoSendChatData, selectedModel, isLoading, messages.length, onAutoSendComplete, clearAutoSendChatData, manualCheckTrigger]);

  // 发送消息的核心逻辑 - 支持传入参数或使用当前状态
  const handleSendMessage = useCallback(
    async (messageText?: string, model?: Model, autoSendConfig?: any) => {
      const textToSend = messageText || inputValue.trim();
      const modelToUse = model || selectedModel;
      const isAutoSend = !!messageText; // 如果传入了 messageText，说明是自动发送

      // 优先使用 autoSendConfig 中的配置
      let endpoint: string | undefined;
      let includeUserMessage = true;

      if (autoSendConfig) {
        endpoint = autoSendConfig.endpoint || undefined;
        includeUserMessage = autoSendConfig.includeUserMessage !== false;
      } else {
        // 兼容旧逻辑 - 根据内容类型判断
        const isVideoDescribe = contentData?.type === 'video' && !contentData?.fileUrl && isAutoSend;
        const isAudioDescribe = contentData?.type === 'audio' && !contentData?.fileUrl && isAutoSend;

        if (isVideoDescribe) {
          endpoint = '/llm/video/describe';
          includeUserMessage = false;
        } else if (isAudioDescribe) {
          endpoint = '/llm/audio/describe';
          includeUserMessage = false;
        }
      }

      // 根据配置决定是否添加用户消息到聊天记录
      if (includeUserMessage) {
        const newMessage: ChatMessage = {
          id: String(Date.now()),
          sender: 'user',
          text: textToSend,
          timestamp: new Date()
        };

        setMessages((prev) => [...prev, newMessage]);
      }

      // 只有在非自动发送时才清空输入框
      if (!isAutoSend) {
        setInputValue('');
      }

      setIsLoading(true);

      // 创建AI响应消息
      const aiMessageId = String(Date.now() + 1);
      const aiResponse: ChatMessage = {
        id: aiMessageId,
        sender: 'ai',
        text: '',
        timestamp: new Date()
      };

      setMessages((prev) => [...prev, aiResponse]);

      try {
        await postLlmChatContentIdStream(
          {
            contentId,
            question: textToSend,
            modelId: modelToUse?.id,
            uploadId: uploadedFiles.map((file) => file.id)
          },
          (chunk: string) => {
            // 直接更新消息内容，保持加载状态直到完成
            setMessages((prev) =>
              prev.map((msg) => (msg.id === aiMessageId ? { ...msg, text: msg.text + chunk } : msg))
            );
          },
          (error: Error & { code?: string; msg?: string }) => {
            if (error?.code === '30501') {
              openUpgradeModal();
            }
            // 🚨 改进错误处理：显示具体的错误信息
            const errorMessage = error.message || error?.msg || t('common.error');

            setMessages((prev) =>
              prev.map((msg) => (msg.id === aiMessageId ? { ...msg, text: `❌ ${errorMessage}` } : msg))
            );
            // 显示具体的错误信息给用户
            toast.error(errorMessage);
            setIsLoading(false);
          },
          () => {
            // 确保加载状态被正确清除（防止没有数据的情况）
            setIsLoading(false);

            // 如果是 video/audio 类型的自动发送，完成后刷新内容数据
            if (autoSendConfig && (autoSendConfig.type === 'video' || autoSendConfig.type === 'audio')) {
              console.log('🔄 Refreshing content data after video/audio processing');
              onContentRefresh?.();
            }
          },
          undefined, // options
          undefined, // completionConfig
          endpoint // 传入 endpoint 参数
        );
      } catch (error: any) {
        console.error('Chat API error:', error);
        setMessages((prev) => prev.map((msg) => (msg.id === aiMessageId ? { ...msg, text: t('common.error') } : msg)));
        toast.error(t('common.error'));
        setIsLoading(false);
        throw error;
      }
    },
    [inputValue, selectedModel, contentId, isLoading, uploadedFiles, onContentRefresh]
  );

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      setTimeout(() => {
        const scrollElement = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement;
        if (scrollElement) {
          scrollElement.scrollTo({
            top: scrollElement.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
  }, []);

  // 当消息更新时自动滚动
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const copyMessage = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(t('chat.copySuccess'));
  };

  const handleCollectNote = (text: string) => {
    setSelectedText(text);
    setIsCollectModalOpen(true);
  };

  // Quick action buttons data
  const quickActions = [
    {
      icon: HelpCircle,
      label: t('article.chat.quickActions.quiz'),
      prompt: t('article.chat.quickActions.quizPrompt'),
      color: 'bg-blue-50 hover:bg-blue-100 text-blue-700'
    },
    {
      icon: Brain,
      label: t('article.chat.quickActions.mindMap'),
      prompt: t('article.chat.quickActions.mindMapPrompt'),
      color: 'bg-purple-50 hover:bg-purple-100 text-purple-700'
    },
    {
      icon: Layers,
      label: t('article.chat.quickActions.flashcards'),
      prompt: t('article.chat.quickActions.flashcardsPrompt'),
      color: 'bg-orange-50 hover:bg-orange-100 text-orange-700'
    },
    {
      icon: Calendar,
      label: t('article.chat.quickActions.schedule'),
      prompt: t('article.chat.quickActions.schedulePrompt'),
      color: 'bg-indigo-50 hover:bg-indigo-100 text-indigo-700'
    }
  ];

  const handleQuickActionClick = (prompt: string) => {
    setInputValue(prompt);
    setTimeout(() => {
      const inputElement = document.querySelector(
        'textarea[placeholder*="' + t('article.chat.inputPlaceholder') + '"]'
      ) as HTMLTextAreaElement;
      if (inputElement) {
        inputElement.focus();
      }
    }, 100);
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-neutral-900">
      {/* New Chat Button - 只在有聊天记录时显示 */}
      {messages.length > 0 && (
        <div className="flex-shrink-0 p-3 border-b dark:border-neutral-700">
          <Button
            onClick={handleNewChat}
            variant="outline"
            size="sm"
            className="w-full h-8 text-sm font-medium gap-2 hover:bg-gray-50 dark:hover:bg-neutral-800"
          >
            <Plus className="w-4 h-4" />
            {t('article.chat.newChat')}
          </Button>
        </div>
      )}

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4 overflow-hidden" ref={scrollAreaRef}>
        {messages.length === 0 ? (
          // Empty State
          <div className="flex flex-col items-center justify-center h-full py-8">
            <div className="mb-6">
              <div className="w-20 h-20 bg-gray-100 dark:bg-neutral-800 rounded-full flex items-center justify-center">
                <MessageCircle className="w-10 h-10 text-gray-400" strokeWidth={1.5} />
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-8 text-center">
              {t('article.chat.title')}
            </h3>

            <div className="grid grid-cols-2 gap-3 w-full max-w-xs">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  onClick={() => handleQuickActionClick(action.prompt)}
                  className={`h-12 flex items-center justify-center gap-2 text-sm font-medium border-gray-200 dark:border-neutral-700 ${action.color} dark:bg-neutral-800 dark:hover:bg-neutral-700 dark:text-gray-300`}
                >
                  <action.icon className="w-4 h-4" />
                  {action.label}
                </Button>
              ))}
            </div>
          </div>
        ) : (
          // Messages Display
          <div className="space-y-3 md:space-y-4">
            {messages.map((msg, index) => (
              <ChatMessageItem
                key={msg.id}
                message={msg}
                onCopyMessage={copyMessage}
                handleCollectNote={handleCollectNote}
                isLoading={isLoading && msg.sender === 'ai' && index === messages.length - 1}
                index={index}
              />
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Input Area */}
      <ChatInputArea
        inputValue={inputValue}
        showGlobe={showGlobe}
        setShowGlobe={setShowGlobe}
        placeholder={t('article.chat.inputPlaceholder')}
        onInputValueChange={setInputValue}
        onSendMessage={() => handleSendMessage()}
        selectedMode={selectedModel?.name || 'Default'}
        onModeChange={(modelName: string) => {
          const foundModel = models.find((m) => m.name === modelName);
          if (foundModel) {
            setSelectedModel(foundModel);
          }
        }}
        isLoading={isLoading}
        availableModels={models}
        uploadedFiles={uploadedFiles}
        onUploadedFilesChange={setUploadedFiles}
      />

      <CollectNoteModal
        isOpen={isCollectModalOpen}
        onClose={() => setIsCollectModalOpen(false)}
        selectedText={selectedText}
        contentId={contentId}
        mode="create-with-content"
      />
    </div>
  );
}

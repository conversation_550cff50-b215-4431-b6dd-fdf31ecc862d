'use client';

import { useEffect } from 'react';
import { Loader2, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/lib/translation-context';
import { useQuizState } from '@/hooks/use-quiz-state';
import { useQuizData } from '@/hooks/use-quiz-data';

// 子组件导入
import { QuizPreferencesComponent } from './quiz/quiz-preferences';
import { QuizListComponent } from './quiz/quiz-list';
import { QuizTakingComponent } from './quiz/quiz-taking';
import { QuizSettingsDialog } from './quiz/quiz-settings-dialog';

interface QuizComponentProps {
  contentId: number;
  skipAutoLoad?: boolean;
  isLoading?: boolean;
  shouldLoad?: boolean;
  onLoadComplete?: () => void;
}

export function QuizComponent({
  contentId,
  skipAutoLoad = false,
  isLoading = false,
  shouldLoad = false,
  onLoadComplete
}: QuizComponentProps) {
  const { t } = useTranslation();

  // 状态管理
  const {
    quizStage,
    quizPreferences,
    generatedQuizzes,
    activeQuiz,
    currentQuestionIndex,
    userAnswers,
    questionResults,
    isConfigDialogOpen,
    dialogQuizPreferences,
    shouldFetchQuizGroups,
    hasTriggeredLoad,
    isAnswerSubmitting,
    currentQuestion,
    isAnswered,
    isCorrect,
    // 状态更新函数
    setQuizStage,
    setQuizPreferences,
    setGeneratedQuizzes,
    setActiveQuiz,
    setCurrentQuestionIndex,
    setUserAnswers,
    setQuestionResults,
    setIsConfigDialogOpen,
    setDialogQuizPreferences,
    setShouldFetchQuizGroups,
    setIsAnswerSubmitting,
    setCachedQuizGroups,
    // 辅助函数
    resetQuizState,
    updateActiveQuiz,
    hasQuizGroups,
    initialQuizPreferences
  } = useQuizState(contentId);

  // 数据加载和API调用
  const {
    isLoadingQuizGroups,
    isGenerating,
    handleGenerateQuiz,
    handleAnswerSelection,
    fetchQuestionsForQuiz,
    generateQuizGroupMutation,
    updateQuizSettingsMutation
  } = useQuizData({
    contentId,
    shouldFetchQuizGroups,
    setShouldFetchQuizGroups,
    setCachedQuizGroups,
    onLoadComplete,
    setQuizStage,
    setGeneratedQuizzes,
    setActiveQuiz,
    setCurrentQuestionIndex,
    setUserAnswers,
    setQuestionResults,
    setIsAnswerSubmitting,
    updateActiveQuiz,
    activeQuiz,
    currentQuestionIndex,
    userAnswers,
    questionResults
  });

  // 响应外部触发的加载请求
  useEffect(() => {
    if (shouldLoad && contentId && !hasTriggeredLoad.current && !hasQuizGroups(contentId) && !shouldFetchQuizGroups) {
      console.log('🚀 QuizComponent: First time triggering quiz groups fetch for contentId:', contentId);
      hasTriggeredLoad.current = true;
      setShouldFetchQuizGroups(true);
    }
  }, [shouldLoad, contentId, shouldFetchQuizGroups, hasQuizGroups, hasTriggeredLoad, setShouldFetchQuizGroups]);

  // 当 shouldLoad 状态重置时，也重置防重复标记
  useEffect(() => {
    if (!shouldLoad) {
      hasTriggeredLoad.current = false;
    }
  }, [shouldLoad]);

  // === 业务逻辑处理函数 ===

  const startQuiz = (quiz: any) => {
    if (quiz.questions.length === 0 && quiz.groupId) {
      setQuizStage('generating');
      setActiveQuiz(quiz);
      fetchQuestionsForQuiz(quiz);
      return;
    }

    // 重新开始答题时清除之前的答题数据
    setActiveQuiz(quiz);
    setCurrentQuestionIndex(0); // 从第一题开始
    setUserAnswers({}); // 清除所有答案
    setQuestionResults({}); // 清除所有答题结果
    setQuizStage('taking');
  };

  const goToNextQuestion = () => {
    if (!activeQuiz) return;
    
    if (currentQuestionIndex < activeQuiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      // 完成测验
      const answeredCount = Object.keys(questionResults).filter((key) => questionResults[key] !== undefined).length;
      const finalProgress = activeQuiz.questions.length > 0 ? (answeredCount / activeQuiz.questions.length) * 100 : 0;
      const updatedQuiz = {
        ...activeQuiz,
        progress: finalProgress,
        currentQuestionIndex,
        userAnswers,
        questionResults
      };
      updateActiveQuiz(updatedQuiz);
      setQuizStage('results');
    }
  };

  const resetCurrentQuestion = () => {
    if (!activeQuiz || !currentQuestion) return;

    const newAnswers = { ...userAnswers };
    delete newAnswers[currentQuestion.id];
    setUserAnswers(newAnswers);

    const newResults = { ...questionResults };
    delete newResults[currentQuestion.id]; // 删除键而不是设置为null
    setQuestionResults(newResults);

    setActiveQuiz((prev) => prev ? { ...prev, userAnswers: newAnswers, questionResults: newResults } : null);
  };

  const handleSkipQuestion = () => {
    if (!activeQuiz || !currentQuestion) return;
    // 参考 onAnswerSelect 的实现，通过 handleAnswerSelection 传入空值来处理跳过
    handleAnswerSelection('');
  };

  const handleResetAllQuizzes = () => {
    if (!contentId) {
      return;
    }
    setQuizStage('generating');
    generateQuizGroupMutation.mutate({
      contentId,
      reset: 'true',
      data: {}
    });
  };

  const handleResetSpecificQuiz = (quiz: any) => {
    if (!quiz.groupId) {
      console.error('Quiz group ID not found');
      return;
    }
    
    setQuizStage('generating');
    setActiveQuiz(quiz);
    
    // 调用 fetchQuestionsForQuiz 并传入 reset=true
    fetchQuestionsForQuiz(quiz, true);
  };

  const openConfigDialog = () => {
    setDialogQuizPreferences(quizPreferences);
    setIsConfigDialogOpen(true);
  };

  const closeConfigDialog = () => {
    setIsConfigDialogOpen(false);
  };

  const handleDialogRegenerate = () => {
    if (!contentId) return;

    setQuizPreferences(dialogQuizPreferences);
    setQuizStage('generating');
    setIsConfigDialogOpen(false);

    const quizTypes = dialogQuizPreferences.types.map((type) =>
      type === 'multiple-choice' ? 'multiple_choice' : 'free_responses'
    );

    // 链式调用：先更新设置，再生成分组
    updateQuizSettingsMutation.mutate({
      contentId,
      data: {
        quizType: quizTypes,
        QuizDifficulty: [dialogQuizPreferences.difficulty]
      }
    }, {
      onSuccess: () => {
        generateQuizGroupMutation.mutate({
          contentId,
          reset: 'true',
          data: {
            quizTypes,
            difficulty: dialogQuizPreferences.difficulty
          }
        });
      }
    });
  };

  // === 渲染逻辑 ===

  // Loading状态
  if (isLoading || isLoadingQuizGroups) {
    return (
      <div className="h-full flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-gray-500 dark:text-gray-400">{t('common.generating')}</p>
        </div>
      </div>
    );
  }

  // 生成中状态
  if (quizStage === 'generating') {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">{t('article.quiz.generating')}</p>
        </div>
      </div>
    );
  }

  // 偏好设置阶段
  if (quizStage === 'preferences') {
    return (
      <>
        <QuizPreferencesComponent
          preferences={quizPreferences}
          onPreferencesChange={setQuizPreferences}
          onGenerate={() => handleGenerateQuiz(quizPreferences)}
          isGenerating={isGenerating}
        />
        <QuizSettingsDialog
          isOpen={isConfigDialogOpen}
          preferences={dialogQuizPreferences}
          onPreferencesChange={setDialogQuizPreferences}
          onClose={closeConfigDialog}
          onRegenerate={handleDialogRegenerate}
          isGenerating={isGenerating}
        />
      </>
    );
  }

  // 测验列表阶段
  if (quizStage === 'list') {
    return (
      <>
        <QuizListComponent
          quizzes={generatedQuizzes}
          onStartQuiz={startQuiz}
          onResetAllQuizzes={handleResetAllQuizzes}
          onResetSpecificQuiz={handleResetSpecificQuiz}
          onOpenSettings={openConfigDialog}
        />
        <QuizSettingsDialog
          isOpen={isConfigDialogOpen}
          preferences={dialogQuizPreferences}
          onPreferencesChange={setDialogQuizPreferences}
          onClose={closeConfigDialog}
          onRegenerate={handleDialogRegenerate}
          isGenerating={isGenerating}
        />
      </>
    );
  }

  // 答题阶段
  if (quizStage === 'taking' && activeQuiz && currentQuestion) {
    return (
      <>
        <QuizTakingComponent
          activeQuiz={activeQuiz}
          currentQuestionIndex={currentQuestionIndex}
          currentQuestion={currentQuestion}
          userAnswers={userAnswers}
          questionResults={questionResults}
          isAnswered={isAnswered}
          isCorrect={isCorrect}
          isAnswerSubmitting={isAnswerSubmitting}
          onBackToList={() => {
            setActiveQuiz(null);
            setQuizStage('list');
          }}
          onAnswerSelect={handleAnswerSelection}
          onResetQuestion={resetCurrentQuestion}
          onSkipQuestion={handleSkipQuestion}
          onNextQuestion={goToNextQuestion}
          onResetAllQuizzes={handleResetAllQuizzes}
          onOpenSettings={openConfigDialog}
        />
        <QuizSettingsDialog
          isOpen={isConfigDialogOpen}
          preferences={dialogQuizPreferences}
          onPreferencesChange={setDialogQuizPreferences}
          onClose={closeConfigDialog}
          onRegenerate={handleDialogRegenerate}
          isGenerating={isGenerating}
        />
      </>
    );
  }

  // 结果阶段
  if (quizStage === 'results') {
    return (
      <div className="p-4 md:p-6 flex flex-col items-center justify-center h-full text-center">
        <CheckCircle className="h-16 w-16 md:h-24 md:w-24 text-green-500 mb-4 md:mb-6" />
        <h2 className="text-lg md:text-xl font-semibold mb-2">
          {t('article.quiz.taking.completed')}: "{activeQuiz?.description || t('article.quiz.title')}"!
        </h2>
        <p className="text-muted-foreground mb-6 md:mb-8 text-sm md:text-base">
          {t('article.quiz.taking.congratulations')}
        </p>
        <div className="flex gap-3 md:gap-4">
          <Button
            variant="outline"
            onClick={() => {
              if (activeQuiz) {
                resetQuizState();
                const resetQuiz = {
                  ...activeQuiz,
                  currentQuestionIndex: 0,
                  userAnswers: {},
                  questionResults: {}
                };
                setActiveQuiz(resetQuiz);
                setQuizStage('taking');
              }
            }}
            className="text-sm md:text-base h-9 md:h-10 px-4"
          >
            {t('article.quiz.taking.restartQuiz')}
          </Button>
          <Button
            className="bg-gray-800 hover:bg-gray-900 dark:bg-gray-100 dark:hover:bg-gray-200 dark:text-gray-800 text-white text-sm md:text-base h-9 md:h-10 px-4"
            onClick={() => {
              if (activeQuiz) {
                resetQuizState();
                const resetQuiz = {
                  ...activeQuiz,
                  currentQuestionIndex: 0,
                  userAnswers: {},
                  questionResults: {},
                  progress: 0
                };
                setGeneratedQuizzes((prev) => prev.map((q) => (q.id === resetQuiz.id ? resetQuiz : q)));
              }
              setActiveQuiz(null);
              setQuizStage('list');
            }}
          >
            {t('article.quiz.taking.backToList')}
          </Button>
        </div>
      </div>
    );
  }

  // 默认返回偏好设置
  return (
    <QuizPreferencesComponent
      preferences={quizPreferences}
      onPreferencesChange={setQuizPreferences}
      onGenerate={() => handleGenerateQuiz(quizPreferences)}
      isGenerating={isGenerating}
    />
  );
} 
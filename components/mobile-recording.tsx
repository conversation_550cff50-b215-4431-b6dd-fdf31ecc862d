'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Mic, Square, Play, Pause, X, Upload, Loader2 } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import { postInfraFilesUpload } from '@/servers/api/wendangshangchuan';
import { useAppDataStore } from '@/lib/stores/app-data-store';
import { useRouter } from 'next/navigation';

interface MobileRecordingProps {
  isOpen: boolean;
  onClose: () => void;
  spaceId?: number;
  onCreateSpace?: (title: string, description?: string) => Promise<number | null>;
}

type RecordingState = 'idle' | 'recording' | 'paused' | 'stopped';

export function MobileRecording({ isOpen, onClose, spaceId, onCreateSpace }: MobileRecordingProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { createContent } = useAppDataStore();

  const [recordingState, setRecordingState] = useState<RecordingState>('idle');
  const [recordingTime, setRecordingTime] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 清理函数
  const cleanup = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    setRecordingTime(0);
    setRecordingState('idle');
    setAudioBlob(null);
    setAudioUrl(null);
    setIsPlaying(false);
    audioChunksRef.current = [];
  };

  // 关闭时清理
  useEffect(() => {
    if (!isOpen) {
      cleanup();
    }
  }, [isOpen]);

  // 确保有 spaceId，如果没有则创建新空间
  const ensureSpaceId = async (title: string, description?: string): Promise<number> => {
    let currentSpaceId = spaceId;

    if (!currentSpaceId) {
      if (onCreateSpace) {
        const newSpaceId = await onCreateSpace(title, description || title);
        if (!newSpaceId) {
          throw new Error('Failed to create space');
        }
        currentSpaceId = newSpaceId;
      } else {
        throw new Error('No space ID provided and no create space method available');
      }
    }

    return currentSpaceId;
  };

  // 开始录音
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      });

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: MediaRecorder.isTypeSupported('audio/webm;codecs=opus') ? 'audio/webm;codecs=opus' : 'audio/webm'
      });

      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(audioBlob);
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);

        // 停止所有音轨
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorder.start(100);
      setRecordingState('recording');

      // 开始计时
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Error starting recording:', error);
      toast.error(t('record.microphoneAccessDenied') || 'Microphone access denied');
    }
  };

  // 暂停录音
  const pauseRecording = () => {
    if (mediaRecorderRef.current && recordingState === 'recording') {
      mediaRecorderRef.current.pause();
      setRecordingState('paused');
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // 恢复录音
  const resumeRecording = () => {
    if (mediaRecorderRef.current && recordingState === 'paused') {
      mediaRecorderRef.current.resume();
      setRecordingState('recording');
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && (recordingState === 'recording' || recordingState === 'paused')) {
      mediaRecorderRef.current.stop();
      setRecordingState('stopped');
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // 播放/暂停音频
  const togglePlayback = () => {
    if (!audioUrl) return;

    if (!audioRef.current) {
      audioRef.current = new Audio(audioUrl);
      audioRef.current.onended = () => setIsPlaying(false);
    }

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  // 重新录音
  const retryRecording = () => {
    cleanup();
  };

  // 上传并保存录音
  const handleSave = async () => {
    if (!audioBlob) return;

    setIsUploading(true);
    try {
      const fileName = `mobile_recording_${new Date().getTime()}.webm`;
      const audioFile = new File([audioBlob], fileName, { type: 'audio/webm' });

      const formData = new FormData();
      formData.append('file', audioFile);

      const uploadResponse = await postInfraFilesUpload({
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (uploadResponse?.code !== '0' || !uploadResponse?.data?.id) {
        throw new Error(uploadResponse?.msg || 'File upload failed');
      }

      const fileId = uploadResponse.data.id;
      const title = `${t('record.recordingTitle')} ${new Date().toLocaleString()}`;

      const currentSpaceId = await ensureSpaceId(title, `Mobile audio recording: ${fileName}`);
      console.log('🚀 ~ handleSave ~ title:', title);

      const contentResponse = await createContent({
        uploadId: fileId,
        spaceId: currentSpaceId,
        title: title
      });

      if (contentResponse?.code !== '0' || !contentResponse?.data?.id) {
        throw new Error(contentResponse?.msg || 'Content creation failed');
      }

      const contentId = contentResponse.data.id;

      router.push(`/article/${contentId}`);
      onClose();
    } catch (error) {
      console.error('Save recording error:', error);
      toast.error(error instanceof Error ? error.message : t('record.saveError') || 'Failed to save recording');
    } finally {
      setIsUploading(false);
    }
  };

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm">
      <div className="flex flex-col h-full">
        {/* 顶部关闭按钮 */}
        <div className="flex justify-end p-4">
          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/20 h-10 w-10 p-0 rounded-full"
          >
            <X className="h-6 w-6" />
          </Button>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 flex flex-col items-center justify-center px-6">
          {/* 录音时间显示 */}
          <div className="text-6xl font-mono font-bold text-white mb-8">{formatTime(recordingTime)}</div>

          {/* 录音状态提示 */}
          <div className="text-center text-lg text-white/80 mb-12">
            {recordingState === 'idle' && (t('record.clickToStart') || 'Tap to start recording')}
            {recordingState === 'recording' && (
              <span className="flex items-center justify-center space-x-3">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <span>{t('record.recording') || 'Recording...'}</span>
              </span>
            )}
            {recordingState === 'paused' && (t('record.paused') || 'Recording paused')}
            {recordingState === 'stopped' && (t('record.completed') || 'Recording completed')}
          </div>

          {/* 录音控制按钮 */}
          <div className="flex items-center justify-center space-x-8 mb-8">
            {recordingState === 'idle' && (
              <Button
                onClick={startRecording}
                className="h-24 w-24 rounded-full bg-red-500 hover:bg-red-600 text-white shadow-lg"
                size="lg"
              >
                <Mic className="h-12 w-12" />
              </Button>
            )}

            {recordingState === 'recording' && (
              <>
                <Button
                  onClick={pauseRecording}
                  className="h-16 w-16 rounded-full bg-white/20 hover:bg-white/30 text-white"
                  size="lg"
                >
                  <Pause className="h-8 w-8" />
                </Button>
                <Button
                  onClick={stopRecording}
                  className="h-24 w-24 rounded-full bg-red-500 hover:bg-red-600 text-white shadow-lg"
                  size="lg"
                >
                  <Square className="h-12 w-12" />
                </Button>
              </>
            )}

            {recordingState === 'paused' && (
              <>
                <Button
                  onClick={resumeRecording}
                  className="h-16 w-16 rounded-full bg-green-500 hover:bg-green-600 text-white"
                  size="lg"
                >
                  <Mic className="h-8 w-8" />
                </Button>
                <Button
                  onClick={stopRecording}
                  className="h-24 w-24 rounded-full bg-red-500 hover:bg-red-600 text-white shadow-lg"
                  size="lg"
                >
                  <Square className="h-12 w-12" />
                </Button>
              </>
            )}

            {recordingState === 'stopped' && audioUrl && (
              <Button
                onClick={togglePlayback}
                className="h-16 w-16 rounded-full bg-blue-500 hover:bg-blue-600 text-white"
                size="lg"
              >
                {isPlaying ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
              </Button>
            )}
          </div>
        </div>

        {/* 底部操作按钮 */}
        {recordingState === 'stopped' && (
          <div className="p-6 space-y-4">
            <Button
              onClick={handleSave}
              disabled={isUploading}
              className="w-full h-14 bg-green-500 hover:bg-green-600 text-white text-lg font-medium rounded-xl"
            >
              {isUploading ? (
                <>
                  <Loader2 className="w-6 h-6 mr-3 animate-spin" />
                  {t('record.saving') || 'Saving...'}
                </>
              ) : (
                <>
                  <Upload className="w-6 h-6 mr-3" />
                  {t('record.save') || 'Save Recording'}
                </>
              )}
            </Button>
            <Button
              onClick={retryRecording}
              variant="outline"
              className="w-full h-14 border-white/30 text-white hover:bg-white/10 text-lg font-medium rounded-xl"
            >
              {t('record.retry') || 'Record Again'}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

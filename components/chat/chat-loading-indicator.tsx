import { Avatar, AvatarFallback } from "@/components/ui/avatar"

export function ChatLoadingIndicator() {
  return (
    <div className="flex items-start gap-2 md:gap-3">
      <Avatar className="h-7 w-7 md:h-8 md:w-8 mt-1">
        <AvatarFallback className="bg-blue-500 text-white text-xs font-medium">AI</AvatarFallback>
      </Avatar>
      <div className="bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-2xl rounded-bl-md p-3 shadow-sm">
        <div className="flex space-x-1">
          <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"></div>
          <div
            className="w-1.5 h-1.5 md:w-2 md:h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
            style={{ animationDelay: "0.1s" }}
          ></div>
          <div
            className="w-1.5 h-1.5 md:w-2 md:h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
            style={{ animationDelay: "0.2s" }}
          ></div>
        </div>
      </div>
    </div>
  )
}

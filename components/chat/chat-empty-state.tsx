"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { MessageCircle } from "lucide-react"
import { useTranslation } from "@/lib/translation-context"
import type React from "react"

interface QuickAction {
  icon: React.ElementType
  label: string
  color?: string // Tailwind classes for bg, hover, text
  onClick?: () => void
}

interface ChatEmptyStateProps {
  title?: string
  quickActions?: QuickAction[]
}

export function ChatEmptyState({ title, quickActions = [] }: ChatEmptyStateProps) {
  const { t } = useTranslation()
  const defaultTitle = title || t("chat.emptyStateTitle")

  return (
    <div className="flex flex-col items-center justify-center h-full py-8 px-4 text-center">
      <div className="mb-4 md:mb-6">
        <div className="w-16 h-16 md:w-20 md:h-20 bg-gray-100 dark:bg-neutral-800 rounded-full flex items-center justify-center">
          <MessageCircle className="w-8 h-8 md:w-10 md:h-10 text-gray-400 dark:text-gray-500" strokeWidth={1.5} />
        </div>
      </div>

      <h3 className="text-base md:text-lg font-medium text-gray-700 dark:text-gray-300 mb-6 md:mb-8">{defaultTitle}</h3>

      {quickActions.length > 0 && (
        <div className="grid grid-cols-2 gap-2 md:gap-3 w-full max-w-xs">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              onClick={action.onClick}
              className={`h-10 md:h-12 flex items-center justify-center gap-2 text-xs md:text-sm font-medium border-gray-200 dark:border-neutral-700 
                          ${action.color || "bg-gray-50 hover:bg-gray-100 text-gray-700 dark:bg-neutral-800 dark:hover:bg-neutral-700 dark:text-gray-300"}`}
            >
              <action.icon className="w-3.5 h-3.5 md:w-4 md:h-4" />
              {action.label}
            </Button>
          ))}
        </div>
      )}
    </div>
  )
}

// Default props for Next.js
ChatEmptyState.defaultProps = {
  quickActions: [],
}

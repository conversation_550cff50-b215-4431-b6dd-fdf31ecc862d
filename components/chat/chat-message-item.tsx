/*
 * @Date: 2025-06-04 19:49:49
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-24 22:25:37
 * @FilePath: /tutoro-ai-front/components/chat/chat-message-item.tsx
 */
'use client';

import type { ChatMessage } from '@/types';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { BookmarkPlus, Copy } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import MarkdownPreview from '@uiw/react-markdown-preview';
import { useTheme } from 'next-themes';
import { isDarkMode } from '@/lib/utils/theme';

interface ChatMessageItemProps {
  message: ChatMessage;
  onCopyMessage: (text: string) => void;
  handleCollectNote: (text: string) => void;
  isLoading?: boolean;
  index: number;
}

export function ChatMessageItem({
  message,
  onCopyMessage,
  handleCollectNote,
  isLoading = false,
  index
}: ChatMessageItemProps) {
  const isUser = message.sender === 'user';
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <div className={cn('flex items-start gap-2 md:gap-3 w-full group min-w-0 overflow-hidden', isUser ? 'justify-end' : '')}>
      {!isUser && (
        <Avatar className="h-7 w-7 md:h-8 md:w-8 mt-1 flex-shrink-0">
          <AvatarFallback className="bg-blue-500 text-white text-xs font-medium">AI</AvatarFallback>
        </Avatar>
      )}

      <div
        className={cn(
          'flex flex-col min-w-0 flex-shrink overflow-hidden', // 添加 overflow-hidden 到父容器
          isUser
            ? 'items-end max-w-[75%]' // 用户消息稍微小一点
            : 'items-start max-w-[85%]' // AI消息可以稍大，但不要太大
        )}
        style={{ maxWidth: isUser ? '75%' : '85%' }} // 内联样式强制限制
      >
        <div
          className={cn(
            'w-full p-2.5 md:p-3 rounded-2xl text-sm md:text-base break-words overflow-hidden max-w-full', // 添加 max-w-full 确保严格限制
            isUser
              ? 'bg-gray-200 dark:bg-neutral-700 text-gray-900 dark:text-gray-100 rounded-br-md'
              : 'bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 text-gray-900 dark:text-gray-100 rounded-bl-md shadow-sm'
          )}
          style={{ 
            maxWidth: '100%', 
            width: '100%',
            overflow: 'hidden',
            boxSizing: 'border-box',
            wordWrap: 'break-word',
            wordBreak: 'break-word',
            minWidth: '0'
          }} // 内联样式强制宽度限制
        >
          {/* AI消息加载中但没有内容时显示加载动画 */}
          {!isUser && isLoading && (!message.text || message.text.trim() === '') ? (
            <div className="flex items-center py-1">
              <div className="flex space-x-1">
                <div className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"></div>
                <div
                  className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                  style={{ animationDelay: '0.1s' }}
                ></div>
                <div
                  className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                  style={{ animationDelay: '0.2s' }}
                ></div>
              </div>
            </div>
          ) : (
            message.text && (
              <>
                                 {isUser ? (
                   // 用户消息使用普通文本渲染，增强文本换行处理
                   <p className="leading-relaxed whitespace-pre-wrap chat-content-wrapper">{message.text}</p>
                 ) : (
                   // AI消息使用Markdown渲染
                   <div 
                     className="markdown-content overflow-hidden max-w-full w-full" 
                     style={{ 
                       maxWidth: '100%', 
                       width: '100%',
                       overflow: 'hidden',
                       boxSizing: 'border-box',
                       wordBreak: 'break-word',
                       overflowWrap: 'anywhere'
                     }}
                   >
                     <MarkdownPreview
                      source={message.text}
                      style={{
                        backgroundColor: 'transparent',
                        color: 'inherit',
                        fontSize: 'inherit',
                        lineHeight: 'inherit',
                        wordBreak: 'break-word',
                        overflowWrap: 'anywhere',
                        maxWidth: '100%',
                        width: '100%',
                        overflow: 'hidden',
                        boxSizing: 'border-box',
                        minWidth: '0'
                      }}
                      wrapperElement={{
                        'data-color-mode': isDarkMode(theme) ? 'dark' : 'light',
                        style: {
                          wordBreak: 'break-word',
                          overflowWrap: 'anywhere',
                          maxWidth: '100%',
                          width: '100%',
                          overflow: 'hidden',
                          boxSizing: 'border-box',
                          minWidth: '0',
                          contain: 'layout style'
                        }
                      }}
                    />
                    {/* AI消息加载时在内容后显示打字指示器 */}
                    {isLoading && (
                      <span className="inline-flex items-center ml-1">
                        <div className="flex space-x-1">
                          <div className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"></div>
                          <div
                            className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                            style={{ animationDelay: '0.1s' }}
                          ></div>
                          <div
                            className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                            style={{ animationDelay: '0.2s' }}
                          ></div>
                        </div>
                      </span>
                    )}
                  </div>
                )}
              </>
            )
          )}
        </div>

        {/* 只有在AI消息且不在加载状态且有内容时才显示按钮 */}
        {index !== 0 && !isUser && !isLoading && message.text && message.text.trim() && (
          <div className="flex items-center gap-2 mt-1 transition-opacity duration-200">
            <Button
              variant="ghost"
              size="sm"
              className="h-10 px-2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
              onClick={() => onCopyMessage(message.text)}
              aria-label="Copy message"
            >
              <Copy className="h-5 w-5" />
              <span className="text-xs">{t('chat.copy')}</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
              onClick={() => handleCollectNote(message.text)}
              aria-label="Collect note"
            >
              <BookmarkPlus className="h-5 w-5" />
              <span className="text-xs">{t('chat.collectNote')}</span>
            </Button>
          </div>
        )}
      </div>

      {isUser && (
        <Avatar className="h-7 w-7 md:h-8 md:w-8 mt-1 flex-shrink-0">
          <AvatarFallback className="bg-gray-500 text-white text-xs font-medium">{t('chat.you')}</AvatarFallback>
        </Avatar>
      )}
    </div>
  );
}

'use client';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Send,
  ChevronDown,
  Globe,
  AtSign,
  Paperclip,
  MicIcon,
  Camera,
  Search,
  TestTube,
  CreditCard,
  Clock,
  BarChart,
  TrendingUp,
  PieChart,
  Circle,
  GitBranch,
  Brain,
  FileText,
  X,
  Loader2
} from 'lucide-react';
import type React from 'react';
import { useRef, useState } from 'react';
import { useTranslation } from '@/lib/translation-context';
import type { Model } from '@/lib/stores/app-data-store';
import { postInfraFilesUpload } from '@/servers/api/wendangshangchuan';
import { toast } from 'sonner';

interface ChatInputAreaProps {
  showGlobe?: boolean;
  setShowGlobe?: (show: boolean) => void;
  inputValue: string;
  onInputValueChange: (value: string) => void;
  onSendMessage: () => void;
  selectedMode: string;
  onModeChange: (mode: string) => void;
  isLoading: boolean;
  placeholder?: string;
  showPaperclip?: boolean;
  sendButtonIcon?: React.ReactNode;
  availableModels?: Model[];
  uploadedFiles?: Array<{ id: number; name: string; size: number }>;
  onUploadedFilesChange?: (files: Array<{ id: number; name: string; size: number }>) => void;
}

export function ChatInputArea({
  inputValue,
  onInputValueChange,
  onSendMessage,
  selectedMode,
  onModeChange,
  isLoading,
  placeholder,
  showPaperclip = true,
  availableModels = [],
  uploadedFiles = [],
  onUploadedFilesChange
}: ChatInputAreaProps) {
  const { t } = useTranslation();
  const defaultPlaceholder = placeholder || t('chat.placeholder');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isLoading && !isUploading && inputValue.trim()) {
      e.preventDefault();
      onSendMessage();
    }
  };

  // 处理文件上传点击
  const handleUploadClick = () => {
    if (uploadedFiles.length >= 5) {
      toast.error('最多只能上传5个文件');
      return;
    }
    fileInputRef.current?.click();
  };

  // 处理文件选择和上传
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (uploadedFiles.length >= 5) {
      toast.error('最多只能上传5个文件');
      return;
    }

    setIsUploading(true);

    try {
      // 上传文件
      const formData = new FormData();
      formData.append('file', file);

      const uploadResponse = await postInfraFilesUpload({
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (uploadResponse?.code !== '0' || !uploadResponse?.data?.id) {
        throw new Error(uploadResponse?.msg || 'File upload failed');
      }

      // 添加上传的文件到列表
      const newFile = {
        id: uploadResponse.data.id,
        name: file.name,
        size: file.size
      };
      
      const updatedFiles = [...uploadedFiles, newFile];
      onUploadedFilesChange?.(updatedFiles);

      toast.success(t('upload.success') || 'File uploaded successfully');
    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error instanceof Error ? error.message : (t('upload.error') || 'Upload failed'));
    } finally {
      setIsUploading(false);
      // 重置文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // 移除指定的文件
  const handleRemoveFile = (fileId: number) => {
    const updatedFiles = uploadedFiles.filter(file => file.id !== fileId);
    onUploadedFilesChange?.(updatedFiles);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
  };

  // 获取文件图标
  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return <FileText className="w-4 h-4 text-blue-500" />;
  };

  // 功能模板列表
  const functionTemplates = [
    {
      key: 'quiz',
      label: '小测验',
      icon: TestTube,
      prompt: '帮我创建一个关于以下主题的小测验，包含5-10个选择题，每题提供4个选项和正确答案：'
    },
    {
      key: 'flashcard',
      label: '抽认卡',
      icon: CreditCard,
      prompt: '帮我制作关于以下内容的抽认卡，正面是问题或概念，背面是答案或解释：'
    },
    {
      key: 'schedule',
      label: '时间表',
      icon: Clock,
      prompt: '帮我制定一个关于以下内容的学习时间表，包括具体的时间安排和学习目标：'
    },
    {
      key: 'barchart',
      label: '柱状图',
      icon: BarChart,
      prompt: '帮我创建一个柱状图来展示以下数据，并提供详细的数据分析：'
    },
    {
      key: 'linechart',
      label: '折线图',
      icon: TrendingUp,
      prompt: '帮我制作一个折线图来显示以下数据的趋势变化：'
    },
    {
      key: 'piechart',
      label: '饼图',
      icon: PieChart,
      prompt: '帮我创建一个饼图来展示以下数据的比例分布：'
    },
    {
      key: 'venndiagram',
      label: '维恩图',
      icon: Circle,
      prompt: '帮我制作一个维恩图来比较和分析以下概念或事物的关系：'
    },
    {
      key: 'flowchart',
      label: '流程图',
      icon: GitBranch,
      prompt: '帮我创建一个流程图来展示以下过程或步骤：'
    },
    {
      key: 'mindmap',
      label: '思维导图',
      icon: Brain,
      prompt: '帮我制作一个思维导图来整理和展示以下主题的相关概念：'
    }
  ];

  const handleFunctionSelect = (template: (typeof functionTemplates)[0]) => {
    onInputValueChange(template.prompt);
  };

  // 使用模型列表或默认模式列表
  const displayModes =
    availableModels.length > 0
      ? availableModels.map((model) => ({ key: model.name || 'Unknown', label: model.name || 'Unknown Model' }))
      : [];

  const currentModeLabel =
    availableModels.length > 0
      ? selectedMode || 'Default'
      : t(`chat.mode.${selectedMode.toLowerCase()}`) || selectedMode;

  return (
    <div className="p-4 border-t dark:border-neutral-700 bg-white dark:bg-neutral-900">
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        onChange={handleFileChange}
        accept=".pdf,.ppt,.pptx,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.bmp,.webp"
      />

      {/* 上传文件列表 - 正方形小卡片 */}
      {(uploadedFiles.length > 0 || isUploading) && (
        <div className="mb-3">
          <div className="flex flex-wrap gap-2">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="relative w-16 h-16 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 flex flex-col items-center justify-center p-1 group"
              >
                <div className="flex flex-col items-center justify-center h-full">
                  {getFileIcon(file.name)}
                  <span className="text-xs text-gray-600 dark:text-gray-400 truncate w-full text-center mt-1">
                    {file.name.length > 8 ? file.name.slice(0, 8) + '...' : file.name}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveFile(file.id)}
                  className="absolute -top-1 -right-1 h-4 w-4 p-0 bg-gray-600 hover:bg-gray-700 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            ))}
            
            {/* 上传中的状态 */}
            {isUploading && (
              <div className="w-16 h-16 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 flex flex-col items-center justify-center">
                <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
                <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  上传中
                </span>
              </div>
            )}
          </div>
          
          {/* 文件数量提示 */}
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            已上传 {uploadedFiles.length}/5 个文件
          </div>
        </div>
      )}

      <div className="border rounded-2xl space-y-1 p-2">
        <div className="flex items-center gap-3 mb-4">
          <div className="flex-1 relative">
            <Input
              type="text"
              placeholder={isUploading ? '文件上传中，请稍候...' : defaultPlaceholder}
              value={inputValue}
              onChange={(e) => onInputValueChange(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isUploading}
              className="h-12 text-base border-gray-200 bg-white dark:bg-neutral-900 rounded-2xl px-0 pr-12 focus:ring-3 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                border: 'none',
                boxShadow: 'none'
              }}
            />
          </div>
        </div>

        <div className="flex items-center gap-3">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-9 px-4 max-w-32 text-sm text-gray-700 dark:text-gray-300 border-gray-200 dark:border-neutral-600 rounded-full hover:bg-gray-50 dark:hover:bg-neutral-800"
              >
                <span className="truncate">{currentModeLabel}</span>
                <ChevronDown className="w-4 h-4 ml-1 flex-shrink-0" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {displayModes.map((mode) => (
                <DropdownMenuItem key={mode.key} onClick={() => onModeChange(mode.key)}>
                  {mode.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* {showLearnPlus && (
          <Button
            size="sm"
            className="h-9 px-4 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-full transition-colors"
          >
            <span className="mr-2">🍀</span>
            {t('chat.learnPlus')}
          </Button>
        )} */}
          {/* 搜索 */}
          {/* <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowGlobe?.(!showGlobe)}
            className={`h-9 px-2 text-xs rounded-md transition-all duration-200 ${
              showGlobe
                ? 'text-blue-600 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-400 border border-blue-600 dark:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 border border-gray-200 dark:border-gray-200 hover:bg-gray-100 dark:hover:bg-neutral-700'
            }`}
          >
            <Globe className={`w-3 h-3 mr-1 ${showGlobe ? 'text-blue-600 dark:text-blue-400' : ''}`} />
            {t('mainContent.search')}
          </Button> */}

          {/* 功能模板 */}
          {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-9 w-9 p-0 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-neutral-600 rounded-full hover:bg-gray-50 dark:hover:bg-neutral-800"
              >
                <AtSign className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              {functionTemplates.map((template) => {
                const IconComponent = template.icon;
                return (
                  <DropdownMenuItem 
                    key={template.key} 
                    onClick={() => handleFunctionSelect(template)}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    <IconComponent className="h-4 w-4 text-gray-500" />
                    <span>{template.label}</span>
                  </DropdownMenuItem>
                );
              })}
            </DropdownMenuContent>
          </DropdownMenu> */}

          <div className="flex-1" />

          <div className="flex items-center gap-2">
            {/* 上传图片 */}
            {/* {showCamera && (
              <Button
                variant="ghost"
                size="sm"#
                className="h-9 w-9 p-0 text-gray-500 dark:text-gray-400 rounded-full hover:bg-gray-100 dark:hover:bg-neutral-700"
              >
                <Camera className="h-4 w-4" />
              </Button>
            )} */}
            {/* 上传文件 */}
            {showPaperclip && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleUploadClick}
                disabled={isUploading || uploadedFiles.length >= 5}
                className="h-9 w-9 p-0 text-gray-500 dark:text-gray-400 rounded-full hover:bg-gray-100 dark:hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title={uploadedFiles.length >= 5 ? '已达到最大文件数量限制(5个)' : '上传文件'}
              >
                {isUploading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Paperclip className="h-4 w-4" />
                )}
              </Button>
            )}
            {/* 语音 */}
            {/* {showMic && (
            <Button
              className="h-9 px-4 bg-gray-800 hover:bg-gray-900 dark:bg-gray-200 dark:hover:bg-gray-100 dark:text-black text-white text-sm font-medium rounded-full transition-colors"
              disabled={isLoading}
            >
              <MicIcon className="h-4 w-4 mr-2" />
              {t('chat.voice')}
            </Button>
          )} */}
            <button
              className="h-9 w-9  items-center justify-center  flex bg-gray-800 hover:bg-gray-900 dark:bg-gray-200 dark:hover:bg-gray-100 dark:text-black text-white text-sm font-medium rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={onSendMessage}
              disabled={isLoading || isUploading || !inputValue.trim()}
              title={isUploading ? '文件上传中，请稍候...' : '发送消息'}
            >
              {isUploading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Default props for Next.js
ChatInputArea.defaultProps = {
  inputValue: '',
  onInputValueChange: () => {},
  onSendMessage: () => {},
  selectedMode: 'Default',
  onModeChange: () => {},
  isLoading: false
};

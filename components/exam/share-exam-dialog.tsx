/*
 * @Date: 2025-06-04 19:49:49
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-04 20:02:39
 * @FilePath: /tutoro-ai-front/components/exam/share-exam-dialog.tsx
 */
"use client"

import { useState, useEffect } from "react"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  // DialogDescription, // No longer needed as per new design
  // DialogFooter, // No longer needed
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Copy, Check, Upload } from "lucide-react" // Added Upload icon
import { useTranslation } from "@/lib/translation-context"

interface ShareExamDialogProps {
  isOpen: boolean
  onClose: () => void
  examLink: string
}

export function ShareExamDialog({ isOpen, onClose, examLink }: ShareExamDialogProps) {
  const [hasCopied, setHasCopied] = useState(false)
  const { t } = useTranslation()

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(examLink)
      setHasCopied(true)
    } catch (err) {
      console.error("Failed to copy text: ", err)
      alert(t("exam.share.copyError"))
    }
  }

  useEffect(() => {
    if (hasCopied) {
      const timer = setTimeout(() => {
        setHasCopied(false)
      }, 2000) // Reset icon after 2 seconds
      return () => clearTimeout(timer)
    }
  }, [hasCopied])

  // Close dialog when isOpen becomes false (e.g. Escape key or overlay click)
  // Also reset hasCopied state if dialog is closed externally
  useEffect(() => {
    if (!isOpen) {
      setHasCopied(false)
    }
  }, [isOpen])

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg p-6">
        <DialogHeader className="mb-4">
          <DialogTitle className="flex items-center text-lg font-semibold">
            <Upload className="mr-2 h-5 w-5 text-muted-foreground" />
            {t("exam.share.title")}
          </DialogTitle>
          {/* DialogDescription is removed as per the new screenshot's simpler design */}
        </DialogHeader>
        <div className="flex items-center space-x-0 bg-muted/50 border rounded-md overflow-hidden pr-2">
          <Input
            id="exam-link"
            value={examLink}
            readOnly
            className="flex-1 h-10 px-3 py-2 text-sm bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            aria-label="考试链接"
          />
          <Button
            type="button"
            variant="default" // Changed to default to match screenshot's dark button
            size="sm" // Adjusted size for better fit
            onClick={copyToClipboard}
            className="shrink-0 h-8" // Ensure button doesn't shrink too much
            aria-label={t("exam.share.copyLink")}
          >
            {hasCopied ? <Check className="h-4 w-4 mr-1.5" /> : <Copy className="h-4 w-4 mr-1.5" />}
            {hasCopied ? t("exam.share.copied") : t("exam.share.copyLink")}
          </Button>
        </div>
        {/* DialogFooter is removed as per the new screenshot, relying on X to close */}
      </DialogContent>
    </Dialog>
  )
}

/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-20 21:08:11
 * @FilePath: /tutoro-ai-front/components/header.tsx
 */
'use client';

import { useState } from 'react';
import { usePathname } from 'next/navigation';
import { Search, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { useTranslation } from '@/lib/translation-context';
import { localeNames, getLocaleFlag } from '@/i18n.config';
import { SearchModal } from '@/components/search-modal';
import Link from 'next/link';
import { useHybridAuth, useHybridIsAuthenticated, useHybridUser } from '@/lib/stores/hybrid-auth-store';
import { useAppStore } from '@/lib/stores/app-store';
import appConfig from '@/config/app';
import Image from 'next/image';
import { useTheme } from 'next-themes';
import { isDarkMode } from '@/lib/utils/theme';

interface HeaderProps {
  onLanguageClick: () => void;
  onUpgradeClick: () => void;
}

// 选择器函数 - 避免内联函数导致的无限循环
const selectArticleTitle = (state: any) => state.articleTitle;

export function Header({ onLanguageClick, onUpgradeClick }: HeaderProps) {
  const { locale, t } = useTranslation();
  const isAuthenticated = useHybridIsAuthenticated();
  const pathname = usePathname();
  const articleTitle = useAppStore(selectArticleTitle);
  const { theme } = useTheme();
  const user = useHybridUser();

  // 检查是否在文章页面
  const isArticlePage = pathname?.includes('/article/');

  // 是否打开搜索模态框
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);

  return (
    <>
      <header className="sticky top-0 z-50 bg-background shadow-sm transition-all duration-300">
        <div className="mx-auto px-4 lg:px-6 py-2 lg:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 h-10 :gap-4">
              <SidebarTrigger state="collapsed" className="max-xl:flex xl:hidden p-0 items-center">
                <Menu className="w-6 h-6" size={32} />
                <span className="sr-only">{t('sidebar.expand')}</span>
              </SidebarTrigger>
              <Link
                href="/"
                className="flex items-center gap-2  max-lg:flex lg:hidden  hover:opacity-80 transition-opacity"
              >
                <Image
                  src={isDarkMode(theme) ? appConfig.logo_dark : appConfig.logo}
                  alt={appConfig.name}
                  width={117}
                  height={31.3333}
                />
              </Link>
              {/* 根据页面类型显示不同内容 */}
              {isArticlePage && articleTitle ? (
                // 文章页面显示标题
                <div className="flex items-center min-w-0 flex-1 lg:flex hidden">
                  <h1 className="text-lg md:text-xl font-semibold truncate text-foreground">{articleTitle}</h1>
                </div>
              ) : (
                <></>
              )}
            </div>
            <div className="flex items-center gap-2 h-10 :gap-4">
              <div className="">
                {isAuthenticated && (
                  <div className="relative hidden md:flex items-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="pl-2 pr-3 flex items-center gap-2 text-muted-foreground bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700"
                      onClick={() => setIsSearchModalOpen(true)}
                    >
                      <Search className="w-4 h-4" />
                      {t('header.search')}
                    </Button>
                  </div>
                )}
                {isAuthenticated && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="md:hidden h-8 w-8 p-0"
                    onClick={() => setIsSearchModalOpen(true)}
                  >
                    <Search className="w-4 h-4" />
                  </Button>
                )}
              </div>
              {isAuthenticated && user?.subscription?.product !== 'PRO' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onUpgradeClick}
                  className="flex items-center gap-1.5 md:gap-2 h-8 px-2 text-xs md:text-sm md:h-9 md:px-3 border-green-500 text-green-600 hover:bg-green-50 hover:text-green-700 hover:border-green-600"
                >
                  {t('header.upgrade')}
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={onLanguageClick}
                className="flex items-center gap-1.5 md:gap-2 h-8 px-2 text-xs md:text-sm md:h-9 md:px-3"
              >
                <span className="w-3.5 h-2.5 md:w-4 md:h-3 flex items-center justify-center">
                  {getLocaleFlag(locale)}
                </span>
                {localeNames[locale]}
              </Button>
              {!isAuthenticated && (
                <Link href="/login">
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium h-8 px-2 text-xs md:text-sm md:h-9 md:px-3"
                  >
                    {t('header.login')}
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      <SearchModal isOpen={isSearchModalOpen} onClose={() => setIsSearchModalOpen(false)} />
    </>
  );
}

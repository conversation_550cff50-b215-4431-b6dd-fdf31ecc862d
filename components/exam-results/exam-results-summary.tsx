/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-10 16:08:36
 * @FilePath: /tutoro-ai-front/components/exam-results/exam-results-summary.tsx
 */
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { CheckCircle2, Clock, XCircle } from "lucide-react"
import { useTranslation } from "@/lib/translation-context"

interface ExamResultsSummaryProps {
  examResult: any
}

export function ExamResultsSummary({ examResult }: ExamResultsSummaryProps) {
  const { t } = useTranslation()

  return (
    <div className="space-y-6">
      {/* Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4 flex flex-col items-center justify-center text-center">
          <div className="rounded-full bg-green-100 dark:bg-green-900/30 p-3 mb-2">
            <CheckCircle2 className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <p className="text-2xl font-bold">{examResult.correctAnswers}</p>
          <p className="text-sm text-muted-foreground">{t('exam.results.correctAnswers')}</p>
        </Card>

        <Card className="p-4 flex flex-col items-center justify-center text-center">
          <div className="rounded-full bg-red-100 dark:bg-red-900/30 p-3 mb-2">
            <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <p className="text-2xl font-bold">{examResult.incorrectAnswers}</p>
          <p className="text-sm text-muted-foreground">{t('exam.results.incorrectAnswers')}</p>
        </Card>

        <Card className="p-4 flex flex-col items-center justify-center text-center">
          <div className="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3 mb-2">
            <Clock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <p className="text-2xl font-bold">{examResult.timeSpent}</p>
          <p className="text-sm text-muted-foreground">{t('exam.results.timeSpent')}</p>
        </Card>
      </div>

      {/* Section Breakdown */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">{t('exam.results.sectionBreakdown')}</h3>
        {examResult.sections.map((section: any) => (
          <div key={section.id} className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <span className="font-medium">{section.title}</span>
                <Badge variant="outline" className="text-xs">
                  {section.score}/{section.totalScore}
                </Badge>
              </div>
              <span className="text-sm text-muted-foreground">
                {((section.score / section.totalScore) * 100).toFixed(0)}%
              </span>
            </div>
            <Progress value={(section.score / section.totalScore) * 100} className="h-2" />
          </div>
        ))}
      </div>

      {/* Recommendations */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">{t('exam.results.recommendations.title')}</h3>
        <div className="space-y-2 text-sm">
          <p>
            • {t('exam.results.recommendations.focusOn')} <span className="font-medium">React Hooks</span> {t('exam.results.recommendations.and')} <span className="font-medium">{t('exam.results.recommendations.routing')}</span>
          </p>
          <p>
            • {t('exam.results.recommendations.suggestReading')} <span className="font-medium">{t('exam.results.recommendations.reactDocs')}</span> {t('exam.results.recommendations.lifecycle')}
          </p>
          <p>
            • {t('exam.results.recommendations.tryMore')} <span className="font-medium">React Router</span> {t('exam.results.recommendations.exercises')}
          </p>
        </div>
      </div>
    </div>
  )
}

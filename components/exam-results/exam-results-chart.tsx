"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>tesianG<PERSON>,
  Legend,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Sector,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
} from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { type ChartConfig, ChartContainer, ChartTooltipContent } from "@/components/ui/chart"
import { useState, useCallback } from "react"
import { useTranslation } from "@/lib/translation-context"

interface TimeDataItem {
  name: string
  value: number
  fill: string
}

interface ExamResultsChartProps {
  examResult: {
    categoryPerformance: Array<{
      category: string
      correct: number
      total: number
      percentage: number
    }>
    timeDistribution?: TimeDataItem[]
  }
}

// Sample time data (replace with actual data from examResult if available)
const defaultTimeData: TimeDataItem[] = [
  { name: "阅读题目", value: 30, fill: "hsl(var(--chart-1))" },
  { name: "思考答案", value: 45, fill: "hsl(var(--chart-2))" },
  { name: "检查答案", value: 25, fill: "hsl(var(--chart-3))" },
]

const categoryChartConfig = {
  percentage: {
    label: "正确率 (%)",
  },
} satisfies ChartConfig

const timeChartConfig = {
  value: {
    label: "时间占比 (%)",
  },
} satisfies ChartConfig

// Helper for active pie chart segment
const renderActiveShape = (props: any) => {
  const RADIAN = Math.PI / 180
  const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props
  const sin = Math.sin(-RADIAN * midAngle)
  const cos = Math.cos(-RADIAN * midAngle)
  const sx = cx + (outerRadius + 10) * cos
  const sy = cy + (outerRadius + 10) * sin
  const mx = cx + (outerRadius + 30) * cos
  const my = cy + (outerRadius + 30) * sin
  const ex = mx + (cos >= 0 ? 1 : -1) * 22
  const ey = my
  const textAnchor = cos >= 0 ? "start" : "end"

  return (
    <g>
      <text x={cx} y={cy} dy={8} textAnchor="middle" fill={fill} className="text-sm font-semibold">
        {payload.name}
      </text>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
      />
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={outerRadius + 6}
        outerRadius={outerRadius + 10}
        fill={fill}
      />
      <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
      <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
      <text
        x={ex + (cos >= 0 ? 1 : -1) * 12}
        y={ey}
        textAnchor={textAnchor}
        fill="#333"
        className="text-xs"
      >{`${value}%`}</text>
      <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={18} textAnchor={textAnchor} fill="#999" className="text-xs">
        {`(占比 ${(percent * 100).toFixed(2)}%)`}
      </text>
    </g>
  )
}

export function ExamResultsChart({ examResult }: ExamResultsChartProps) {
  const [activeIndex, setActiveIndex] = useState(0)
  const { t } = useTranslation()

  const onPieEnter = useCallback(
    (_: any, index: number) => {
      setActiveIndex(index)
    },
    [setActiveIndex],
  )

  // Prepare time data and config dynamically
  const timeData = examResult.timeDistribution || defaultTimeData
  const dynamicTimeChartConfig = timeData.reduce(
    (acc: ChartConfig, item: TimeDataItem) => {
      acc[item.name] = { label: item.name, color: item.fill }
      return acc
    },
    { ...timeChartConfig },
  ) as ChartConfig

  return (
    <div className="space-y-6">
      <Tabs defaultValue="categories">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="categories">{t('exam.results.chart.categoryPerformance')}</TabsTrigger>
          <TabsTrigger value="time">{t('exam.results.chart.timeDistribution')}</TabsTrigger>
        </TabsList>
        <TabsContent value="categories" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('exam.results.chart.categoryPerformanceTitle')}</CardTitle>
            </CardHeader>
            <CardContent>
              <ChartContainer config={categoryChartConfig} className="h-[350px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={examResult.categoryPerformance}
                    layout="vertical"
                    margin={{ top: 5, right: 30, left: 50, bottom: 5 }}
                  >
                    <CartesianGrid horizontal={false} strokeDasharray="3 3" />
                    <XAxis type="number" domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
                    <YAxis
                      dataKey="category"
                      type="category"
                      tickLine={false}
                      axisLine={false}
                      width={120}
                      tick={{ fontSize: 12 }}
                      className="truncate"
                    />
                    <Tooltip
                      cursor={{ fill: "hsl(var(--muted))" }}
                      content={
                        <ChartTooltipContent
                          formatter={(value, name, props) => {
                            if (name === "percentage" && props.payload) {
                              const { correct, total } = props.payload
                              return `${Number(value).toFixed(1)}% (${correct}/${total})`
                            }
                            return String(value)
                          }}
                        />
                      }
                    />
                    <Legend />
                    <Bar dataKey="percentage" name={t('exam.results.chart.accuracy')} radius={4}>
                      {examResult.categoryPerformance.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={entry.percentage >= 60 ? "hsl(var(--chart-2))" : "hsl(var(--chart-5))"}
                        />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="time" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('exam.results.chart.timeDistributionTitle')}</CardTitle>
            </CardHeader>
            <CardContent className="flex justify-center">
              <ChartContainer config={dynamicTimeChartConfig} className="h-[350px] w-full max-w-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Tooltip cursor={{ fill: "hsl(var(--muted))" }} content={<ChartTooltipContent hideLabel />} />
                    <Pie
                      activeIndex={activeIndex}
                      activeShape={renderActiveShape}
                      data={timeData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      dataKey="value"
                      onMouseEnter={onPieEnter}
                    >
                      {timeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Pie>
                    <Legend
                      content={({ payload }) => {
                        return (
                          <ul className="flex flex-wrap justify-center gap-x-4 gap-y-2 mt-4">
                            {payload?.map((entry, index) => (
                              <li key={`item-${index}`} className="flex items-center text-sm">
                                <span
                                  className="w-2.5 h-2.5 rounded-full mr-1.5"
                                  style={{ backgroundColor: entry.color }}
                                />
                                {entry.value}
                              </li>
                            ))}
                          </ul>
                        )
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
          <p className="text-xs text-muted-foreground text-center mt-2">
            {t('exam.results.chart.timeDistributionNote')}
          </p>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{t('exam.results.chart.performanceAnalysis')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-sm">
          <p>
            • {t('exam.results.chart.bestPerformance')} <span className="font-medium">{t('exam.results.chart.stateManagement')}</span> {t('exam.results.chart.withAccuracy')} 50%
          </p>
          <p>
            • {t('exam.results.chart.needImprovement')} <span className="font-medium">{t('exam.results.chart.routing')}</span> {t('exam.results.chart.withAccuracy')} 0%
          </p>
          <p>
            • {t('exam.results.chart.averageTime')} <span className="font-medium">1分40秒</span> {t('exam.results.chart.slowerThanAverage')} 20%
          </p>
          <p>
            • {t('exam.results.chart.suggestFocus')} <span className="font-medium">React Router</span> {t('exam.results.chart.and')} <span className="font-medium">React Hooks</span> {t('exam.results.chart.relatedKnowledge')}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

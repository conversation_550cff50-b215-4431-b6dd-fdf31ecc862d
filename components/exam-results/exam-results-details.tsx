"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { CheckCircle2, ChevronDown, ChevronUp, XCircle } from "lucide-react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { useTranslation } from "@/lib/translation-context"

interface ExamResultsDetailsProps {
  examResult: any
}

export function ExamResultsDetails({ examResult }: ExamResultsDetailsProps) {
  const [filter, setFilter] = useState<"all" | "correct" | "incorrect">("all")
  const [expandedQuestions, setExpandedQuestions] = useState<string[]>([])
  const { t } = useTranslation()

  const toggleQuestion = (questionId: string) => {
    setExpandedQuestions((prev) =>
      prev.includes(questionId) ? prev.filter((id) => id !== questionId) : [...prev, questionId],
    )
  }

  const filteredQuestions = examResult.sections.flatMap((section: any) =>
    section.questions.filter((q: any) => {
      if (filter === "all") return true
      if (filter === "correct") return q.isCorrect
      if (filter === "incorrect") return !q.isCorrect
      return true
    }),
  )

  return (
    <div className="space-y-4">
      {/* Filter Buttons */}
      <div className="flex gap-2">
        <Button variant={filter === "all" ? "default" : "outline"} size="sm" onClick={() => setFilter("all")}>
          {t('exam.results.details.all')} ({examResult.totalQuestions})
        </Button>
        <Button
          variant={filter === "correct" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilter("correct")}
          className="text-green-600"
        >
          {t('exam.results.details.correct')} ({examResult.correctAnswers})
        </Button>
        <Button
          variant={filter === "incorrect" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilter("incorrect")}
          className="text-red-600"
        >
          {t('exam.results.details.incorrect')} ({examResult.incorrectAnswers})
        </Button>
      </div>

      {/* Questions List */}
      <ScrollArea className="h-[500px] pr-4">
        <div className="space-y-4">
          {filteredQuestions.map((question: any) => (
            <Collapsible
              key={question.id}
              open={expandedQuestions.includes(question.id)}
              onOpenChange={() => toggleQuestion(question.id)}
              className="border rounded-lg overflow-hidden"
            >
              <div className="flex items-start p-4 gap-3">
                <div className="flex-shrink-0 mt-1">
                  {question.isCorrect ? (
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-base">
                      {question.number}. {question.title}
                    </h3>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {question.score}/{question.maxScore}
                      </span>
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          {expandedQuestions.includes(question.id) ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">{question.pageReference}</p>
                </div>
              </div>

              <CollapsibleContent>
                <Separator />
                <div className="p-4 space-y-3 bg-muted/30">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{t('exam.results.details.yourAnswer')}:</p>
                    <div
                      className={`text-sm p-3 rounded-md ${question.isCorrect ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800" : "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800"}`}
                    >
                      {question.userAnswer}
                    </div>
                  </div>

                  <div className="space-y-1">
                    <p className="text-sm font-medium">{t('exam.results.details.correctAnswer')}:</p>
                    <div className="text-sm p-3 rounded-md bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
                      {question.correctAnswer}
                    </div>
                  </div>

                  <div className="pt-2">
                    <Button variant="outline" size="sm">
                      {t('exam.results.details.viewExplanation')}
                    </Button>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}

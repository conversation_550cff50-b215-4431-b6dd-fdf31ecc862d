"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useTranslation } from "@/lib/translation-context"

interface ExamFeedbackModalProps {
  isOpen: boolean
  onClose: () => void
  examId: string
}

export function ExamFeedbackModal({ isOpen, onClose, examId }: ExamFeedbackModalProps) {
  const [difficulty, setDifficulty] = useState<string>("just-right")
  const [feedback, setFeedback] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const { t } = useTranslation()

  const handleSubmit = async () => {
    setIsSubmitting(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    console.log("Feedback submitted:", { examId, difficulty, feedback })

    setIsSubmitting(false)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t('exam.results.feedback.title')}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label>{t('exam.results.feedback.difficultyQuestion')}</Label>
            <RadioGroup value={difficulty} onValueChange={setDifficulty}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="too-easy" id="too-easy" />
                <Label htmlFor="too-easy">{t('exam.results.feedback.tooEasy')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="just-right" id="just-right" />
                <Label htmlFor="just-right">{t('exam.results.feedback.justRight')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="too-difficult" id="too-difficult" />
                <Label htmlFor="too-difficult">{t('exam.results.feedback.tooDifficult')}</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label htmlFor="feedback">{t('exam.results.feedback.feedbackQuestion')}</Label>
            <Textarea
              id="feedback"
              placeholder={t('exam.results.feedback.feedbackPlaceholder')}
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              rows={4}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            {t('exam.results.feedback.cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? t('exam.results.feedback.submitting') : t('exam.results.feedback.submit')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

/*
 * @Date: 2025-01-27
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-04 16:29:48
 * @FilePath: /tutoro-ai-front/components/delete-confirm-modal.tsx
 */
'use client';

import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  spaceName: string;
}

export function DeleteConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  spaceName,
}: DeleteConfirmModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 transition-all duration-300">
      <div className="bg-white dark:bg-neutral-900 rounded-2xl max-w-md w-full p-6 shadow-xl transform transition-all duration-300 scale-100">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            您确定要删除这个空间吗？
          </h2>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-gray-400 hover:text-gray-600"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="mb-8">
          <p className="text-gray-600">
            "{spaceName}" 将被永久删除。
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={onClose}
            className="px-6 py-2 text-gray-600 border-gray-300 hover:bg-gray-50"
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            className="px-6 py-2 bg-red-500 hover:bg-red-600 text-white"
          >
            删除
          </Button>
        </div>
      </div>
    </div>
  );
} 
/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-12 13:48:29
 * @FilePath: /tutoro-ai-front/components/quiz/quiz-ai-interaction-bar.tsx
 */
"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { RotateCcw, Send, Mic, Paperclip, Camera, ChevronDown } from "lucide-react"
import { useTranslation } from "@/lib/translation-context"
import type React from "react"

interface QuizAIInteractionBarProps {
  chatInputValue: string
  onChatInputValueChange: (value: string) => void
  onSendChatMessage: () => void
  onHintRequest?: () => void
  onWalkthroughRequest?: () => void
  onSimplifyRequest?: () => void
  // Add other props like mode selection if needed
}

export function QuizAIInteractionBar({
  chatInputValue,
  onChatInputValueChange,
  onSendChatMessage,
  onHintRequest = () => {},
  onWalkthroughRequest = () => {},
  onSimplifyRequest = () => {},
}: QuizAIInteractionBarProps) {
  const { t } = useTranslation()

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      onSendChatMessage()
    }
  }

  return (
    <div className="p-3 md:p-4 border-t dark:border-neutral-700 bg-white dark:bg-neutral-900 space-y-2 md:space-y-3">
      <div className="flex flex-wrap gap-2">
        <Button variant="outline" size="sm" className="flex-1 text-xs min-w-[100px]" onClick={onHintRequest}>
          <RotateCcw className="w-3 h-3 mr-1" />
          {t("article.quiz.aiInteraction.giveHint")}
        </Button>
        <Button variant="outline" size="sm" className="flex-1 text-xs min-w-[100px]" onClick={onWalkthroughRequest}>
          <RotateCcw className="w-3 h-3 mr-1" />
          {t("article.quiz.aiInteraction.walkThrough")}
        </Button>
        <Button variant="outline" size="sm" className="flex-1 text-xs min-w-[100px]" onClick={onSimplifyRequest}>
          <RotateCcw className="w-3 h-3 mr-1" />
          {t("article.quiz.aiInteraction.keepSimple")}
        </Button>
      </div>
      {/* <div className="flex items-center gap-2">
        <Input
          type="text"
          placeholder={t("article.quiz.aiInteraction.placeholder")}
          value={chatInputValue}
          onChange={(e) => onChatInputValueChange(e.target.value)}
          onKeyPress={handleKeyPress}
          className="h-9 md:h-10 text-sm flex-1"
        />
        <Button size="icon" className="h-9 w-9 md:h-10 md:w-10" onClick={onSendChatMessage} aria-label="Send message">
          <Send className="w-4 h-4" />
        </Button>
      </div>
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <span>
          {t("article.quiz.aiInteraction.mode")} <ChevronDown className="w-3 h-3 inline-block" />
        </span>
        <div className="flex gap-2">
          <Mic className="w-3.5 h-3.5 md:w-4 md:h-4 cursor-pointer hover:text-foreground" />
          <Paperclip className="w-3.5 h-3.5 md:w-4 md:h-4 cursor-pointer hover:text-foreground" />
          <Camera className="w-3.5 h-3.5 md:w-4 md:h-4 cursor-pointer hover:text-foreground" />
        </div>
      </div> */}
    </div>
  )
}

// Default props for Next.js
QuizAIInteractionBar.defaultProps = {
  chatInputValue: "",
  onChatInputValueChange: () => {},
  onSendChatMessage: () => {},
}

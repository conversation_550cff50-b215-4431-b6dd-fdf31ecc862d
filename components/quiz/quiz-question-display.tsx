'use client';

import { useState, useEffect } from 'react';
import type { QuizQuestion, MultipleChoiceQuestion } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { useTranslation } from '@/lib/translation-context';
import { Trash2 } from 'lucide-react';

interface QuizQuestionDisplayProps {
  question: QuizQuestion;
  userAnswer?: string;
  isAnswered: boolean;
  isCorrect?: boolean;
  onAnswerSelect: (answer: string) => void;
  questionNumber: number;
  totalQuestions: number;
}

export function QuizQuestionDisplay({
  question,
  userAnswer,
  isAnswered,
  isCorrect,
  onAnswerSelect
}: QuizQuestionDisplayProps) {
  const { t } = useTranslation();
  const [freeResponseValue, setFreeResponseValue] = useState(userAnswer || '');

  // 当问题改变时，重置 freeResponseValue
  useEffect(() => {
    setFreeResponseValue(userAnswer || '');
  }, [question.id, userAnswer]);

  // 当 userAnswer 改变时，更新本地状态（用于已回答的题目）
  useEffect(() => {
    if (isAnswered && userAnswer) {
      setFreeResponseValue(userAnswer);
    }
  }, [isAnswered, userAnswer]);

  if (!question) return null;

  return (
    <div className="w-full">
      <div className="flex justify-between items-center gap-2 pb-4">
        <h2 className="text-base md:text-lg font-semibold text-gray-800 dark:text-gray-100">{question.question}</h2>
        <Button
          variant="ghost"
          size="icon"
          className="text-muted-foreground hover:text-destructive -mt-1 h-7 w-7 md:h-8 md:w-8"
        >
          <Trash2 className="h-3.5 w-3.5 md:h-4 md:w-4" />
        </Button>
      </div>

      {question.type === 'multiple-choice' && (
        <div className="space-y-2 md:space-y-3">
          {(question as MultipleChoiceQuestion).options.map((option, index) => {
            const letter = String.fromCharCode(65 + index);
            const isSelected = userAnswer === option;
            const isCorrectOption = option === (question as MultipleChoiceQuestion).correctAnswer;

            let optionStyle =
              'border-gray-300 dark:border-neutral-600 hover:border-gray-400 dark:hover:border-neutral-500 bg-white dark:bg-neutral-800 text-gray-800 dark:text-gray-100';
            if (isAnswered) {
              if (isCorrectOption) {
                optionStyle = 'border-green-500 bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300';
              } else if (isSelected && !isCorrectOption) {
                optionStyle = 'border-red-500 bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300';
              } else {
                optionStyle =
                  'text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-neutral-800/50 border-gray-200 dark:border-neutral-700';
              }
            } else if (isSelected) {
              optionStyle = 'border-primary bg-primary/10 text-primary dark:text-primary-foreground';
            }

            return (
              <Button
                key={option}
                variant="outline"
                className={cn(
                  'w-full justify-start h-auto py-2.5 md:py-3 px-3 md:px-4 text-left text-sm md:text-base font-normal whitespace-normal',
                  optionStyle
                )}
                onClick={() => onAnswerSelect(option)}
                disabled={isAnswered}
              >
                <span className="mr-2 md:mr-3 text-muted-foreground">{letter}.</span>
                {option}
              </Button>
            );
          })}
        </div>
      )}

      {(question.type === 'fill-in-the-blank' || question.type === 'free-response') &&
        (question.type === 'free-response' ? (
          <div className="space-y-3">
            <Textarea
              placeholder={t('article.quiz.enterYourAnswer')}
              value={isAnswered ? userAnswer || '' : freeResponseValue}
              onChange={(e) => setFreeResponseValue(e.target.value)}
              disabled={isAnswered}
              rows={6}
              className={cn(
                'w-full text-sm md:text-base resize-none',
                isAnswered &&
                  (isCorrect
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/30'
                    : 'border-red-500 bg-red-50 dark:bg-red-900/30')
              )}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && e.ctrlKey && !isAnswered && freeResponseValue.trim()) {
                  onAnswerSelect(freeResponseValue.trim());
                }
              }}
            />
            {!isAnswered && (
              <div className="flex justify-end">
                <Button
                  size="sm"
                  onClick={() => {
                    if (freeResponseValue.trim()) {
                      onAnswerSelect(freeResponseValue.trim());
                    }
                  }}
                  disabled={!freeResponseValue.trim()}
                  className="bg-gray-800 hover:bg-gray-900 dark:bg-gray-100 dark:hover:bg-gray-200 dark:text-gray-800 text-white"
                >
                  {t('article.quiz.submit')}
                </Button>
              </div>
            )}
          </div>
        ) : (
          <Input
            placeholder={t('article.quiz.enterYourAnswer')}
            value={userAnswer || ''}
            onChange={(e) => onAnswerSelect(e.target.value)}
            disabled={isAnswered}
            className={cn(
              'h-10 md:h-12 text-sm md:text-base',
              isAnswered &&
                (isCorrect
                  ? 'border-green-500 bg-green-50 dark:bg-green-900/30'
                  : 'border-red-500 bg-red-50 dark:bg-red-900/30')
            )}
          />
        ))}
    </div>
  );
}

// Default props for Next.js
QuizQuestionDisplay.defaultProps = {
  question: {
    id: 'q1',
    type: 'multiple-choice',
    question: 'Default question?',
    options: ['A', 'B'],
    correctAnswer: 'A'
  },
  isAnswered: false,
  onAnswerSelect: () => {},
  questionNumber: 1,
  totalQuestions: 1
};

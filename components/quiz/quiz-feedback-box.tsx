import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>ir<PERSON>, XCircle, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"
import { useTranslation } from "@/lib/translation-context"

interface QuizFeedbackBoxProps {
  isCorrect: boolean
  isSkipped?: boolean // 新增跳过状态
  explanation?: string
  correctAnswer?: string // For fill-in-the-blank or when user is wrong
  pageReference?: string
}

export function QuizFeedbackBox({ isCorrect, isSkipped = false, explanation, correctAnswer, pageReference }: QuizFeedbackBoxProps) {
  const { t } = useTranslation()
  
  // 跳过状态的处理逻辑优先级最高
  const message = isSkipped 
    ? explanation || t("article.quiz.feedback.skippedDefault")
    : isCorrect 
      ? explanation || t("article.quiz.feedback.correctDefault")
      : explanation || `${t("article.quiz.feedback.incorrectDefault")} ${correctAnswer}`

  // 根据状态确定样式
  const containerStyle = isSkipped
    ? "bg-yellow-50 dark:bg-yellow-900/30 border-yellow-200 dark:border-yellow-700"
    : isCorrect
      ? "bg-green-50 dark:bg-green-900/30 border-green-200 dark:border-green-700"
      : "bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-700";

  const iconTextStyle = isSkipped
    ? "text-yellow-600 dark:text-yellow-300"
    : isCorrect 
      ? "text-green-600 dark:text-green-300" 
      : "text-red-600 dark:text-red-300";

  const messageTextStyle = isSkipped
    ? "text-yellow-700 dark:text-yellow-400"
    : isCorrect 
      ? "text-green-700 dark:text-green-400" 
      : "text-red-700 dark:text-red-400";

  const buttonStyle = isSkipped
    ? "text-yellow-600 dark:text-yellow-400 hover:text-yellow-700"
    : isCorrect
      ? "text-green-600 dark:text-green-400 hover:text-green-700"
      : "text-red-600 dark:text-red-400 hover:text-red-700";

  return (
    <div
      className={cn(
        "p-3 md:p-4 rounded-lg border",
        containerStyle
      )}
    >
      <div
        className={cn(
          "flex items-center mb-1 md:mb-2",
          iconTextStyle
        )}
      >
        {isSkipped ? (
          <AlertTriangle className="h-4 w-4 md:h-5 md:w-5 mr-2 shrink-0" />
        ) : isCorrect ? (
          <CheckCircle className="h-4 w-4 md:h-5 md:w-5 mr-2 shrink-0" />
        ) : (
          <XCircle className="h-4 w-4 md:h-5 md:w-5 mr-2 shrink-0" />
        )}
        <span className="font-medium text-sm md:text-base">
          {isSkipped 
            ? t("article.quiz.feedback.skipped")
            : isCorrect 
              ? t("article.quiz.feedback.correct") 
              : t("article.quiz.feedback.incorrect")
          }
        </span>
      </div>
      <p
        className={cn(
          "text-xs md:text-sm leading-relaxed",
          messageTextStyle
        )}
      >
        {message}
      </p>
      {pageReference && (
        <Button
          variant="link"
          size="sm"
          className={cn(
            "p-0 h-auto mt-1 text-xs",
            buttonStyle
          )}
        >
          {t("article.quiz.feedback.pageReference")} {pageReference}
        </Button>
      )}
    </div>
  )
}

// Default props for Next.js
QuizFeedbackBox.defaultProps = {
  isCorrect: true,
  explanation: "This is why.",
}

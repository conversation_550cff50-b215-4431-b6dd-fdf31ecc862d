/*
 * @Date: 2025-06-11 15:27:19
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-12 13:33:07
 * @FilePath: /tutoro-ai-front/components/quiz/quiz-navigation-footer.tsx
 */
/*
 * @Date: 2025-06-11 15:27:19
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-12 13:32:55
 * @FilePath: /tutoro-ai-front/components/quiz/quiz-navigation-footer.tsx
 */
"use client"

import { Button } from "@/components/ui/button"
import { RotateCcw, CheckIcon } from "lucide-react" // Renamed to avoid conflict
import { useTranslation } from "@/lib/translation-context"

interface QuizNavigationFooterProps {
  onResetQuestion: () => void
  onMarkAsKnown?: () => void // Optional: if you implement "mark as known"
  onSkipQuestion: () => void
  onNextQuestion: () => void
  isAnswered: boolean
  isNextDisabled?: boolean // Can be used if next is disabled for other reasons too
}

export function QuizNavigationFooter({
  onResetQuestion,
  onMarkAsKnown,
  onSkipQuestion,
  onNextQuestion,
  isAnswered,
  isNextDisabled,
}: QuizNavigationFooterProps) {
  const { t } = useTranslation()

  return (
    <div className="p-3 md:p-4 border-t dark:border-neutral-800 flex flex-wrap items-center justify-between gap-2">
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="icon"
          onClick={onResetQuestion}
          disabled={!isAnswered}
          aria-label="Reset question"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
        {onMarkAsKnown && (
          <Button
            variant="outline"
            size="icon"
            onClick={onMarkAsKnown}
            disabled={isAnswered}
            aria-label="Mark as known"
          >
            <CheckIcon className="h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex gap-2">
        <Button
          variant="outline"
          className="px-4 md:px-6 text-xs md:text-sm"
          onClick={onSkipQuestion}
          disabled={isAnswered}
        >
          {t('article.quiz.dontKnow')}
        </Button>
        <Button
          className="px-4 md:px-6 bg-gray-800 hover:bg-gray-900 dark:bg-gray-100 dark:hover:bg-gray-200 dark:text-gray-800 text-white text-xs md:text-sm"
          onClick={onNextQuestion}
          disabled={!isAnswered || isNextDisabled}
        >
          {t('article.quiz.nextPage')}
        </Button>
      </div>
    </div>
  )
}

// Default props for Next.js
QuizNavigationFooter.defaultProps = {
  onResetQuestion: () => {},
  onSkipQuestion: () => {},
  onNextQuestion: () => {},
  isAnswered: false,
}

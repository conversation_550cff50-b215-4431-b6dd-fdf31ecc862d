/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-04 19:37:31
 * @FilePath: /tutoro-ai-front/components/exam-creation/preferences-step.tsx
 */
"use client"

import { useState } from "react"
import type { ExamPreferences } from "@/types/exam-creation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowRight } from "lucide-react"
import { useTranslation } from "@/lib/translation-context"

interface PreferencesStepProps {
  initialPreferences: ExamPreferences
  onPreferencesSet: (preferences: ExamPreferences) => void
  onSkip: () => void
}

export function PreferencesStep({ initialPreferences, onPreferencesSet, onSkip }: PreferencesStepProps) {
  const [prefs, setPrefs] = useState<ExamPreferences>(initialPreferences)
  const { t } = useTranslation()

  const handleChange = (field: keyof ExamPreferences, value: any) => {
    setPrefs((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = () => {
    onPreferencesSet(prefs)
  }

  return (
    <div className="space-y-8 max-w-md mx-auto">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="numberOfQuestions">
            {t('spacePage.createExam.numberOfQuestions')} <span className="text-red-500">*</span>
          </Label>
          <Input
            id="numberOfQuestions"
            type="number"
            value={prefs.numberOfQuestions}
            onChange={(e) => handleChange("numberOfQuestions", Number.parseInt(e.target.value, 10) || 0)}
            min="1"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="questionTypes">
            {t('spacePage.createExam.questionTypes')} <span className="text-red-500">*</span>
          </Label>
          <Select
            value={prefs.questionTypes.includes("all") ? "all" : prefs.questionTypes[0]}
            onValueChange={(value) => handleChange("questionTypes", value === "all" ? ["all"] : [value])}
          >
            <SelectTrigger id="questionTypes">
              <SelectValue placeholder={t('spacePage.createExam.selectType')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('spacePage.createExam.both')}</SelectItem>
              <SelectItem value="multiple-choice">{t('spacePage.createExam.multipleChoice')}</SelectItem>
              <SelectItem value="fill-in-the-blank">{t('spacePage.createExam.fillInTheBlank')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="durationMinutes">{t('spacePage.createExam.durationMinutes')}</Label>
        <Input
          id="durationMinutes"
          type="number"
          placeholder={t('spacePage.createExam.durationMinutesPlaceholder')}
          value={prefs.durationMinutes || ""}
          onChange={(e) => handleChange("durationMinutes", Number.parseInt(e.target.value, 10) || undefined)}
          min="0"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="difficulty">{t('spacePage.createExam.difficulty')}</Label>
        <Select value={prefs.difficulty} onValueChange={(value) => handleChange("difficulty", value)}>
          <SelectTrigger id="difficulty">
            <SelectValue placeholder={t('spacePage.createExam.selectDifficulty')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="easy">{t('spacePage.createExam.easy')}</SelectItem>
            <SelectItem value="medium">{t('spacePage.createExam.medium')}</SelectItem>
            <SelectItem value="hard">{t('spacePage.createExam.hard')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex flex-col sm:flex-row justify-center items-center gap-4 pt-6">
        <Button variant="outline" onClick={onSkip} className="w-full sm:w-auto">
          {t('spacePage.createExam.skip')}
        </Button>
        <Button onClick={handleSubmit} className="w-full sm:w-auto">
          {t('spacePage.createExam.start')} <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

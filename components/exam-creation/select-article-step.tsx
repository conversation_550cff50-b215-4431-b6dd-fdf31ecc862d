/*
 * @Date: 2025-06-02 08:10:34
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-16 19:19:37
 * @FilePath: /tutoro-ai-front/components/exam-creation/select-article-step.tsx
 */
"use client"

import { useState } from "react"
import type { ArticleStub } from "@/types/exam-creation"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { ListChecks, ArrowRight } from "lucide-react"
import { useTranslation } from "@/lib/translation-context"

interface SelectArticleStepProps {
  articles: ArticleStub[]
  onArticlesSelected: (articleIds: string[]) => void
}

export function SelectArticleStep({ articles, onArticlesSelected }: SelectArticleStepProps) {
  const [selected, setSelected] = useState<string[]>([])
  const { t } = useTranslation()

  const toggleArticleSelection = (articleId: string) => {
    setSelected((prev) => (prev.includes(articleId) ? prev.filter((id) => id !== articleId) : [...prev, articleId]))
  }

  const toggleSelectAll = () => {
    if (selected.length === articles.length) {
      setSelected([])
    } else {
      setSelected(articles.map((a) => a.id))
    }
  }

  const handleSubmit = () => {
    if (selected.length > 0) {
      onArticlesSelected(selected)
    }
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {articles.map((article) => (
          <Card
            key={article.id}
            className={`cursor-pointer transition-all ${selected.includes(article.id) ? "ring-2 ring-primary shadow-lg" : "hover:shadow-md"}`}
            onClick={() => toggleArticleSelection(article.id)}
          >
            <CardContent className="p-4 space-y-2 relative">
              {article.thumbnailUrl && (
                <div className="aspect-video bg-muted rounded overflow-hidden mb-2">
                  <Image
                    src={article.thumbnailUrl || "/images/placeholder.svg"}
                    alt={article.title}
                    width={200}
                    height={120}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <h3 className="font-medium text-sm leading-tight">{article.title}</h3>
              {selected.includes(article.id) && (
                <div className="absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1">
                  <ListChecks className="h-3 w-3" />
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-4 border-t">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="select-all"
            checked={selected.length === articles.length && articles.length > 0}
            onCheckedChange={toggleSelectAll}
            disabled={articles.length === 0}
          />
          <label htmlFor="select-all" className="text-sm font-medium">
            {t('spacePage.createExam.selectAll')} ({selected.length}/{articles.length})
          </label>
        </div>
        <Button onClick={handleSubmit} disabled={selected.length === 0} className="w-full sm:w-auto">
          {t('spacePage.createExam.continue')} <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

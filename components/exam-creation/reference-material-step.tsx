"use client"

import { useState } from "react"
import type { ReferenceMaterial, ReferenceMaterialType } from "@/types/exam-creation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Upload, LinkIcon, ArrowRight } from "lucide-react"
import { useTranslation } from "@/lib/translation-context"

interface ReferenceMaterialStepProps {
  onMaterialSet: (material: ReferenceMaterial | null) => void
  onSkip: () => void
}

export function ReferenceMaterialStep({ onMaterialSet, onSkip }: ReferenceMaterialStepProps) {
  const [selection, setSelection] = useState<ReferenceMaterialType>(null)
  const [pastedContent, setPastedContent] = useState("")
  const [file, setFile] = useState<File | null>(null)
  const { t } = useTranslation()

  const handleContinue = () => {
    if (selection === "upload" && file) {
      onMaterialSet({ type: "upload", file })
    } else if (selection === "paste" && pastedContent.trim()) {
      onMaterialSet({ type: "paste", content: pastedContent.trim() })
    } else {
      onMaterialSet(null) // Or handle error: material type selected but no content
    }
  }

  return (
    <div className="space-y-6 text-center">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card
          className={`cursor-pointer transition-all p-6 ${selection === "upload" ? "ring-2 ring-primary shadow-lg" : "hover:shadow-md"}`}
          onClick={() => setSelection("upload")}
        >
          <CardContent className="flex flex-col items-center justify-center space-y-2">
            <Upload className="h-10 w-10 text-muted-foreground mb-2" />
            <h3 className="font-semibold">{t('spacePage.createExam.upload')}</h3>
            <p className="text-sm text-muted-foreground">{t('spacePage.createExam.uploadDesc')}</p>
            {selection === "upload" && (
              <Input
                type="file"
                className="mt-4"
                onChange={(e) => setFile(e.target.files ? e.target.files[0] : null)}
              />
            )}
          </CardContent>
        </Card>
        <Card
          className={`cursor-pointer transition-all p-6 ${selection === "paste" ? "ring-2 ring-primary shadow-lg" : "hover:shadow-md"}`}
          onClick={() => setSelection("paste")}
        >
          <CardContent className="flex flex-col items-center justify-center space-y-2">
            <LinkIcon className="h-10 w-10 text-muted-foreground mb-2" />
            <h3 className="font-semibold">{t('spacePage.createExam.paste')}</h3>
            <p className="text-sm text-muted-foreground">{t('spacePage.createExam.pasteDesc')}</p>
            {selection === "paste" && (
              <Textarea
                placeholder={t('pasteModal.pasteTextPlaceholder')}
                className="mt-4 h-24"
                value={pastedContent}
                onChange={(e) => setPastedContent(e.target.value)}
              />
            )}
          </CardContent>
        </Card>
      </div>
      <div className="flex flex-col sm:flex-row justify-center items-center gap-4 pt-6">
        <Button variant="outline" onClick={onSkip} className="w-full sm:w-auto">
          {t('spacePage.createExam.skip')}
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!selection && !file && !pastedContent.trim()}
          className="w-full sm:w-auto"
        >
          {t('spacePage.createExam.continue')} <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

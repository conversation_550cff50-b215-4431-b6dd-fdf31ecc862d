/*
 * @Date: 2025-01-27
 * @LastEditors: yosan
 * @LastEditTime: 2025-07-07 23:59:42
 * @FilePath: /tutoro-ai-front/components/learning-hub.tsx
 *
 */
'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ExpandableInput } from '@/components/ui/expandable-input';
import { Upload, Link, Mic, ArrowRight, FileText, Loader2 } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { postInfraFilesUpload } from '@/servers/api/wendangshangchuan';
import { postSpaceCreateSpace } from '@/servers/api/kongjian';
import { toast } from 'sonner';
import { useAppDataStore } from '@/lib/stores/app-data-store';
import { useArticleCacheStore } from '@/lib/stores/article-cache-store';
import { useHybridIsAuthenticated } from '@/lib/stores';
import { useAppStore } from '@/lib/stores';
import { PasteContentModal } from '@/components/paste-content-modal';
import { RecordModal } from '@/components/record-modal';
import { MobileRecording } from '@/components/mobile-recording';
import { useIsMobile } from '@/components/ui/use-mobile';

interface LearningHubProps {
  className?: string;
  spaceId?: number;
  onCreateSpace?: (title: string, description?: string) => Promise<number | null>;
}

export function LearningHub({ className = '', spaceId, onCreateSpace }: LearningHubProps) {
  const [aiQuery, setAiQuery] = useState('');
  const [selectedMode, setSelectedMode] = useState('default');
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPasteModalOpen, setIsPasteModalOpen] = useState(false);
  const [isRecordModalOpen, setIsRecordModalOpen] = useState(false);
  const [isMobileRecordingOpen, setIsMobileRecordingOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isMobile = useIsMobile();
  const { t } = useTranslation();
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const { openUpgradeModal } = useAppStore();

  const { models } = useAppDataStore();
  const { createContent } = useAppDataStore();
  const { setNewArticleChat } = useArticleCacheStore();
  const isAuthenticated = useHybridIsAuthenticated();

  // 当 models 加载完成后，设置默认选中第一个模型
  useEffect(() => {
    if (models.length > 0 && selectedMode === 'default') {
      const firstModelName = models[0].name || 'default';
      setSelectedMode(firstModelName);
    }
  }, [models, selectedMode]);

  // 当 searchParams.search 有值时，设置到 aiQuery
  useEffect(() => {
    const searchValue = searchParams.get('search');
    if (searchValue) {
      setAiQuery(decodeURIComponent(searchValue));
    }
  }, [searchParams]);

  // 公共方法：确保有 spaceId，如果没有则创建新空间
  const ensureSpaceId = async (title: string, description?: string): Promise<number> => {
    let currentSpaceId = spaceId;

    // 如果没有传入的 spaceId，尝试从 params 获取
    if (!currentSpaceId && params?.id) {
      currentSpaceId = Number(params.id);
    }

    // 如果仍然没有 spaceId，创建新空间
    if (!currentSpaceId) {
      // 优先使用传入的 onCreateSpace 方法
      if (onCreateSpace) {
        const newSpaceId = await onCreateSpace(title, description || title);
        if (!newSpaceId) {
          throw new Error('Failed to create space');
        }
        currentSpaceId = newSpaceId;
      } else {
        // 回退到直接调用 API 的方式
        const spaceResponse = await postSpaceCreateSpace({
          title,
          description: description || title
        });

        if (spaceResponse?.code !== '0' || !spaceResponse?.data?.id) {
          throw new Error(spaceResponse?.msg || 'Failed to create space');
        }

        currentSpaceId = spaceResponse.data.id;
      }
    }

    return currentSpaceId;
  };

  // 处理 AI 查询
  const handleAiQuery = async () => {
    if (!aiQuery.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const title = aiQuery.slice(0, 50) + (aiQuery.length > 50 ? '...' : '');
      
      // 确保有 spaceId
      const currentSpaceId = await ensureSpaceId(
        title,
        `AI Query: ${aiQuery}`
      );
      console.log('🚀 ~ handleAiQuery ~ title:', title);
      // 使用 store 中的 createContent 方法
      const contentResponse = await createContent({
        spaceId: currentSpaceId,
        uploadId: 0,
        title: title
      });

      if (contentResponse?.code === '30500') {
        openUpgradeModal();
        return;
      }

      if (contentResponse?.code !== '0' || !contentResponse?.data?.id) {
        throw new Error(contentResponse?.msg || 'Failed to create content');
      }

      const contentId = contentResponse.data.id;

      // 使用 article-cache-store 记录新文章聊天信息
      setNewArticleChat(contentId, {
        modelId: selectedMode,
        aiQuery: aiQuery
      });

      // 跳转到文章内容页
      router.push(`/article/${contentId}`);
      // 清空输入
      setAiQuery('');
    } catch (error) {
      console.error('AI Query error:', error);
      toast.error(error instanceof Error ? error.message : t('common.error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModeChange = (mode: string) => {
    setSelectedMode(mode);
  };

  const handleUploadClick = async () => {
    if (!isAuthenticated) {
      router.push('/login?redirect=/');
      return;
    }

    // Trigger file input
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);

    try {
      // Step 1: Upload file to get file ID
      const formData = new FormData();
      formData.append('file', file);

      const uploadResponse = await postInfraFilesUpload({
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (uploadResponse?.code !== '0' || !uploadResponse?.data?.id) {
        throw new Error(uploadResponse?.msg || 'File upload failed');
      }

      const fileId = uploadResponse.data.id;
      const title = file.name.replace(/\.[^/.]+$/, ''); // 去掉文件扩展名作为标题

      // Step 2: 确保有 spaceId
      const currentSpaceId = await ensureSpaceId(
        title,
        `File upload: ${file.name}`
      );
      console.log('🚀 ~ handleFileChange ~ title:', title);

      // Step 3: 使用 store 中的 createContent 方法
      const contentResponse = await createContent({
        uploadId: fileId,
        spaceId: currentSpaceId,
        title: title
      });

      if (contentResponse?.code !== '0' || !contentResponse?.data?.id) {
        throw new Error(contentResponse?.msg || 'Content creation failed');
      }

      const contentId = contentResponse.data.id;

      // Step 4: Navigate to article page
      router.push(`/article/${contentId}`);
    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error instanceof Error ? error.message : t('upload.error'));
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handlePasteClick = () => {
    if (!isAuthenticated) {
      router.push('/login?redirect=/');
      return;
    }
    setIsPasteModalOpen(true);
  };

  const handleRecordClick = async () => {
    if (!isAuthenticated) {
      router.push('/login?redirect=/');
      return;
    }

    if (isMobile) {
      // 移动端：打开全屏录音界面
      setIsMobileRecordingOpen(true);
    } else {
      // PC端：打开录音弹窗
      setIsRecordModalOpen(true);
    }
  };

  return (
    <div className={`bg-white dark:bg-neutral-900 ${className}`}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        onChange={handleFileChange}
        accept=".pdf,.ppt,.pptx,.doc,.docx,.txt,.mp4,.avi,.mov,.wmv,.flv,.webm,.mkv,.m4v,.mp3,.wav,.flac,.aac,.ogg,.wma,.m4a"
      />

      <div className="w-full flex flex-col items-center gap-6 sm:gap-6 mt-12 sm:px-10 lg:px-24 mb-4 sm:mt-16">
        <div className="container mx-auto px-2">
          {/* New Practice Button */}
          {/* {showPracticeButton && (
            <div className="flex justify-center mb-8">
              <Button
                variant="outline"
                className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-full px-6 transition-all duration-300"
                onClick={handlePracticeClick}
              >
                新 考试练习 <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )} */}

          {/* Main Heading */}
          <div className="text-center flex flex-col items-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-8">
              {t('mainContent.welcomeTitle')}
            </h1>

            {/* 上传、粘贴、录音 */}
            <div className="flex flex-col text-center 2xl:max-w-[672px] xl:max-w-[576px] md:max-w-[512px] w-full z-30">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-6">
                <Card
                  className={`w-full md:w-auto cursor-pointer hover:shadow-md transition-all duration-300 border-gray-200 dark:border-neutral-800 dark:bg-neutral-800/50 hover:bg-white dark:hover:bg-neutral-800 ${
                    isUploading ? 'opacity-50 pointer-events-none' : ''
                  }`}
                  onClick={handleUploadClick}
                >
                  <CardContent className="flex flex-row items-center md:flex-col md:items-center p-3 md:p-4 text-left md:text-center">
                    {isUploading ? (
                      <Loader2 className="h-7 w-7 mr-3 md:mr-0 mb-0 md:mb-2 text-gray-600 dark:text-gray-400 animate-spin" />
                    ) : (
                      <Upload className="h-7 w-7 mr-3 md:mr-0 mb-0 md:mb-2 text-gray-600 dark:text-gray-400 transition-transform duration-200 hover:scale-105" />
                    )}
                    <div className="flex flex-col items-start md:items-center">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-0.5 text-base md:text-lg">
                        {isUploading ? t('upload.uploading') : t('mainContent.uploadTitle')}
                      </h3>
                      <p className="text-xs md:text-sm text-gray-500 dark:text-gray-400">
                        {t('mainContent.uploadDesc')}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* 粘贴 */}
                <Card
                  className="w-full md:w-auto cursor-pointer hover:shadow-md transition-all duration-300 border-gray-200 dark:border-neutral-800 dark:bg-neutral-800/50 hover:bg-white dark:hover:bg-neutral-800"
                  onClick={handlePasteClick}
                >
                  <CardContent className="flex flex-row items-center md:flex-col md:items-center p-3 md:p-4 text-left md:text-center">
                    <Link className="h-7 w-7 mr-3 md:mr-0 mb-0 md:mb-2 text-gray-600 dark:text-gray-400 transition-transform duration-200 hover:scale-105" />
                    <div className="flex flex-col items-start md:items-center">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-0.5 text-base md:text-lg">
                        {t('mainContent.pasteTitle')}
                      </h3>
                      <p className="text-xs md:text-sm text-gray-500 dark:text-gray-400">
                        {t('mainContent.pasteDesc')}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* 录音 */}
                <Card
                  className="w-full md:w-auto cursor-pointer hover:shadow-md transition-all duration-300 border-gray-200 dark:border-neutral-800 dark:bg-neutral-800/50 hover:bg-white dark:hover:bg-neutral-800"
                  onClick={handleRecordClick}
                >
                  <CardContent className="flex flex-row items-center md:flex-col md:items-center p-3 md:p-4 text-left md:text-center">
                    <Mic className="h-7 w-7 mr-3 md:mr-0 mb-0 md:mb-2 text-gray-600 dark:text-gray-400 transition-transform duration-200 hover:scale-105" />
                    <div className="flex flex-col items-start md:items-center">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-0.5 text-base md:text-lg">
                        {t('mainContent.recordTitle')}
                      </h3>
                      <p className="text-xs md:text-sm text-gray-500 dark:text-gray-400">
                        {t('mainContent.recordDesc')}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
            {/* 输入框 */}
            <div className="2xl:max-w-[672px] xl:max-w-[576px] md:max-w-[512px] w-full mt-[-12]">
              <ExpandableInput
                placeholder={t('mainContent.askPlaceholder')}
                value={aiQuery}
                onChange={setAiQuery}
                onSubmit={handleAiQuery}
                selectedMode={selectedMode}
                onModeChange={handleModeChange}
                useModels={true}
                isSubmitting={isSubmitting || isUploading}
                isAuthenticated={isAuthenticated}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Paste Content Modal */}
      <PasteContentModal
        isOpen={isPasteModalOpen}
        onClose={() => setIsPasteModalOpen(false)}
        spaceId={spaceId}
        onCreateSpace={onCreateSpace}
      />
      
      {/* Record Modal */}
      <RecordModal
        isOpen={isRecordModalOpen}
        onClose={() => setIsRecordModalOpen(false)}
        spaceId={spaceId}
        onCreateSpace={onCreateSpace}
      />
      
      {/* Mobile Recording */}
      <MobileRecording
        isOpen={isMobileRecordingOpen}
        onClose={() => setIsMobileRecordingOpen(false)}
        spaceId={spaceId}
        onCreateSpace={onCreateSpace}
      />
    </div>
  );
}

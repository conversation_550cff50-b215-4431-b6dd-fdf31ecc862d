/*
 * @Date: 2025-06-04 17:25:03
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-09 23:18:19
 * @FilePath: /tutoro-ai-front/components/feedback-modal.tsx
 */
'use client';

import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import { postFeedbackCreateFeedback } from '@/servers/api/fankui';
import { useHybridUser } from '@/lib/stores/hybrid-auth-store';

interface FeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function FeedbackModal({ isOpen, onClose }: FeedbackModalProps) {
  const { t } = useTranslation();
  const user = useHybridUser();
  
  const [feedback, setFeedback] = useState('');
  const [email, setEmail] = useState('');
  const [category, setCategory] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{
    name?: string;
    email?: string;
    category?: string;
    feedback?: string;
    general?: string;
  }>({});

  // 预填用户信息
  useEffect(() => {
    if (user) {
      setName(user.nickname || user.username || '');
      setEmail(user.email || '');
    }
  }, [user]);

  const validateForm = () => {
    const newErrors: typeof errors = {};

    if (!name.trim()) {
      newErrors.name = String(t('feedbackModal.errors.nameRequired')) || 'Name is required';
    }

    if (!email.trim()) {
      newErrors.email = String(t('feedbackModal.errors.emailRequired')) || 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = String(t('feedbackModal.errors.emailInvalid')) || 'Invalid email format';
    }

    if (!category) {
      newErrors.category = String(t('feedbackModal.errors.categoryRequired')) || 'Feedback type is required';
    }

    if (!feedback.trim()) {
      newErrors.feedback = String(t('feedbackModal.errors.feedbackRequired')) || 'Feedback is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const response = await postFeedbackCreateFeedback({
        name: name.trim(),
        email: email.trim(),
        category,
        message: feedback.trim()
      });

      if (response.code === '0') {
        toast.success(String(t('feedbackModal.submitSuccess')) || 'Feedback submitted successfully!');
        
        // Reset form (keep user info but clear feedback content)
        setFeedback('');
        setCategory('');
        setErrors({});
        onClose();
      } else {
        throw new Error(response.msg || 'Failed to submit feedback');
      }
    } catch (error: any) {
      console.error('Submit feedback error:', error);
      setErrors({
        general: error?.response?.data?.msg || 
                error?.message || 
                String(t('feedbackModal.submitError')) || 
                'Failed to submit feedback. Please try again.'
      });
      toast.error(String(t('feedbackModal.submitError')) || 'Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    switch (field) {
      case 'name':
        setName(value);
        break;
      case 'email':
        setEmail(value);
        break;
      case 'category':
        setCategory(value);
        break;
      case 'feedback':
        setFeedback(value);
        break;
    }
    
    // Clear error when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[90%] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('feedbackModal.title')}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {/* General Error Message */}
          {errors.general && (
            <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-700 dark:text-red-300">{errors.general}</p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="name">{t('feedbackModal.name')}</Label>
            <Input
              id="name"
              placeholder={String(t('feedbackModal.namePlaceholder')) || 'Enter your name'}
              value={name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={errors.name ? 'border-red-500 focus-visible:ring-red-500' : ''}
            />
            {errors.name && <p className="text-sm text-red-600 dark:text-red-400">{errors.name}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">{t('feedbackModal.feedbackType')}</Label>
            <Select value={category} onValueChange={(value) => handleInputChange('category', value)}>
              <SelectTrigger className={errors.category ? 'border-red-500 focus-visible:ring-red-500' : ''}>
                <SelectValue placeholder={t('feedbackModal.selectType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bug">{t('feedbackModal.bugReport')}</SelectItem>
                <SelectItem value="feature">{t('feedbackModal.featureSuggestion')}</SelectItem>
                <SelectItem value="improvement">{t('feedbackModal.improvementSuggestion')}</SelectItem>
                <SelectItem value="other">{t('feedbackModal.other')}</SelectItem>
              </SelectContent>
            </Select>
            {errors.category && <p className="text-sm text-red-600 dark:text-red-400">{errors.category}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">{t('feedbackModal.email')}</Label>
            <Input
              id="email"
              type="email"
              placeholder={t('feedbackModal.emailPlaceholder')}
              value={email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={errors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}
            />
            {errors.email && <p className="text-sm text-red-600 dark:text-red-400">{errors.email}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="feedback">{t('feedbackModal.yourFeedback')}</Label>
            <Textarea
              id="feedback"
              placeholder={t('feedbackModal.feedbackPlaceholder')}
              value={feedback}
              onChange={(e) => handleInputChange('feedback', e.target.value)}
              rows={4}
              className={errors.feedback ? 'border-red-500 focus-visible:ring-red-500' : ''}
            />
            {errors.feedback && <p className="text-sm text-red-600 dark:text-red-400">{errors.feedback}</p>}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            {t('feedbackModal.cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                {String(t('feedbackModal.submitting')) || 'Submitting...'}
              </div>
            ) : (
              t('feedbackModal.submit')
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

/*
 * @Date: 2025-01-03 10:00:00
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-12 18:26:39
 * @FilePath: /tutoro-ai-front/components/header2.tsx
 */
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Settings, Upload, UserCircle, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/lib/translation-context';
import { localeNames, getLocaleFlag } from '@/i18n.config';
import Link from 'next/link';
import { useHybridIsAuthenticated } from '@/lib/stores/hybrid-auth-store';
import { useAppStore } from '@/lib/stores/app-store';
import appConfig from '@/config/app';
import Image from 'next/image';
import { Progress } from '@/components/ui/progress';
import { useTheme } from 'next-themes';
import { isDarkMode } from '@/lib/utils/theme';

interface Header2Props {
  setIsShareDialogOpen?: (isOpen: boolean) => void;
  showProgress?: boolean;
  isOutline?: boolean;
}

export function Header2({ showProgress = false, setIsShareDialogOpen, isOutline = true }: Header2Props) {
  const { locale, t } = useTranslation();
  const isAuthenticated = useHybridIsAuthenticated();
  const router = useRouter();
  const { examProgress } = useAppStore();
  const { theme } = useTheme();

  const handleBack = () => {
    router.back();
  };

  return (
    <header className="sticky top-0 w-full bg-background border-b border-border z-50">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Left side */}
          <div className="flex items-center gap-4">
            <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
              <Image
                src={isDarkMode(theme) ? appConfig.logo_dark : appConfig.logo}
                alt={appConfig.name}
                width={117}
                height={31.3333}
                className="h-6 w-auto"
              />
            </Link>
          </div>
          {showProgress && (
            <div className="flex-1 flex items-center justify-center px-4 max-w-md mx-auto">
              <div className="flex items-center gap-2 w-full max-w-xs">
                <div className="text-xs text-muted-foreground min-w-[12px] text-center">
                  {examProgress.answeredQuestions}
                </div>
                <div className="flex-1 space-y-1">
                  <Progress value={examProgress.currentProgress} className="w-full h-4" />
                </div>
                <div className="text-xs text-muted-foreground min-w-[12px] text-center">
                  {examProgress.totalQuestions}
                </div>
              </div>
            </div>
          )}

          {/* Right side */}
          <div className="flex items-center gap-2">
            {isOutline && (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 h-8 px-3 text-xs md:text-sm"
                onClick={() => {
                  setIsShareDialogOpen && setIsShareDialogOpen(true);
                }}
              >
                <Upload className="w-4 h-3 flex items-center justify-center" />
                <span className="hidden sm:inline">{t('exam.share.button')}</span>
              </Button>
            )}

            <Button variant="ghost" size="icon" onClick={handleBack} className="h-8 w-8" aria-label={t('common.back')}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}

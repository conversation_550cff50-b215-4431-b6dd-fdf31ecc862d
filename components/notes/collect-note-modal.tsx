'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { X, Plus, Edit, Eye } from 'lucide-react';
import { useRouter } from 'next/navigation';
import type { ChatMessage } from '@/types';
import { useTranslation } from '@/lib/translation-context';
import { useCreateNote, useUpdateNote, useGetNoteDetail } from '@/lib/api/hooks';
import { toast } from 'sonner';
import MarkdownPreview from '@uiw/react-markdown-preview';
import { useTheme } from 'next-themes';
import { isDarkMode } from '@/lib/utils/theme';

interface EditingNote {
  id: number;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  source: 'chat' | 'manual' | 'article';
  contentTitle?: string;
  spaceTitle?: string;
}

// 定义模式类型
type ModalMode = 'create-with-content' | 'create-empty' | 'edit';

interface CollectNoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedText?: string;
  contentId?: number;
  spaceId?: number;
  editingNote?: EditingNote | null;
  // 新增：指定调用来源，用于确定默认的Tab状态
  mode?: ModalMode;
  // 新增：编辑模式下的笔记ID，用于获取笔记详情
  noteId?: number | null;
}

export function CollectNoteModal({
  isOpen,
  onClose,
  selectedText,
  contentId,
  spaceId,
  editingNote,
  mode,
  noteId
}: CollectNoteModalProps) {
  const router = useRouter();
  const [title, setTitle] = useState('');
  const [tagInput, setTagInput] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [content, setContent] = useState('');
  const [isEditingContent, setIsEditingContent] = useState(false);
  const { t } = useTranslation();
  const { theme } = useTheme();

  // 获取笔记详情（仅在编辑模式且有noteId时）
  const { data: noteDetailData, isLoading: isLoadingNoteDetail } = useGetNoteDetail(
    mode === 'edit' && noteId ? noteId : null
  );

  // 确定当前模式
  const currentMode: ModalMode = mode || (editingNote ? 'edit' : selectedText ? 'create-with-content' : 'create-empty');
  const isEditMode = currentMode === 'edit';

  // 根据模式设置默认的Tab状态
  const getDefaultTabState = (mode: ModalMode) => {
    switch (mode) {
      case 'create-with-content': // SpaceChatWindow和ChatComponent传入内容
        return false; // 默认preview
      case 'create-empty': // NotesPage新建空白笔记
        return true; // 默认edit
      case 'edit': // NotesPage编辑笔记
        return true; // 默认edit
      default:
        return false;
    }
  };

  // 当模态框打开时，初始化表单数据和Tab状态
  useEffect(() => {
    if (isOpen) {
      // 设置默认Tab状态
      setIsEditingContent(getDefaultTabState(currentMode));
      console.log('🚀 ~ useEffect ~ editingNote:', editingNote);

      if (currentMode === 'edit') {
        // 编辑模式：优先使用API获取的最新数据
        if (noteDetailData?.data) {
          // 如果从API获取到数据，解析并填充
          const noteData = noteDetailData.data;
          let parsedContent: any = {};
          try {
            parsedContent = JSON.parse(noteData.context || '{}');
          } catch (e) {
            console.error('Parse note context error:', e);
            parsedContent = { title: noteData.context || '', content: '' };
          }

          setTitle(parsedContent.title || t('notes.noTitle'));
          setContent(parsedContent.content || noteData.context || '');
          setTags(noteData.tags?.map((tag: any) => tag.tagName || '') || []);
        } else if (editingNote) {
          // 如果API数据还未加载，临时使用传入的editingNote数据
          setTitle(editingNote.title);
          setContent(editingNote.content);
          setTags(editingNote.tags || []);
        }
      } else if (currentMode === 'create-with-content') {
        // 新建模式：使用选中的文本
        setContent(selectedText || '');
        setTitle('');
        setTags([]);
      } else {
        // 新建空白笔记
        setTitle('');
        setContent('');
        setTags([]);
      }
      setTagInput('');
    }
  }, [isOpen, currentMode, editingNote, selectedText, noteDetailData, t]);

  const createNote = useCreateNote({
    onSuccess: () => {
      toast.success(t('chat.noteSaved'));
      resetForm();
      onClose();
    },
    onError: (error: any) => {
      console.error('Save note error:', error);
      toast.error(error.message || t('chat.noteSaveError'));
      setIsSaving(false);
    }
  });

  const updateNote = useUpdateNote({
    onSuccess: () => {
      toast.success(t('chat.noteUpdated'));
      resetForm();
      onClose();
    },
    onError: (error: any) => {
      console.error('Update note error:', error);
      toast.error(error.message || t('chat.noteUpdateError'));
      setIsSaving(false);
    }
  });

  const resetForm = () => {
    setTitle('');
    setTags([]);
    setTagInput('');
    setContent('');
    setIsEditingContent(false);
    setIsSaving(false);
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter((t) => t !== tag));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleSave = async () => {
    if (!title.trim()) return;

    setIsSaving(true);

    // 构造笔记内容，包含标题和文本
    const noteContent = {
      title: title.trim(),
      content: content || '',
      createdAt: editingNote?.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      source: editingNote?.source || (contentId ? 'article' : spaceId ? 'chat' : 'manual')
    };

    if (isEditMode) {
      // 编辑模式：使用更新API
      const noteIdToUpdate = noteId || editingNote?.id;
      if (noteIdToUpdate) {
        updateNote.mutate({
          id: noteIdToUpdate,
          data: {
            contentId,
            spaceId,
            context: JSON.stringify(noteContent),
            tags: tags
          }
        });
      }
    } else {
      // 新建模式：使用创建API
      createNote.mutate({
        contentId,
        spaceId,
        context: JSON.stringify(noteContent),
        tags: tags,
        title: title.trim()
      });
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 如果是编辑模式且正在加载笔记详情，显示加载状态
  if (isEditMode && isLoadingNoteDetail) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-3xl max-w-[90%] max-h-[95vh] min-h-[600px] overflow-y-auto">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p>{t('notes.loading') || 'Loading...'}</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-3xl  max-w-[90%]   max-h-[95vh] min-h-[600px] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? t('chat.editNote') : contentId ? t('chat.collectNote') : t('notes.newNote')}
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="title">{t('chat.title')}</Label>
            <Input
              id="title"
              placeholder={t('chat.titlePlaceholder')}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="tags">{t('chat.tags')}</Label>
            <div className="flex items-center gap-2">
              <Input
                id="tags"
                placeholder={t('chat.addTagPlaceholder')}
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={handleKeyDown}
                className="flex-1"
              />
              <Button type="button" size="icon" onClick={handleAddTag}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="px-2 py-1 text-xs">
                    {tag}
                    <button
                      type="button"
                      className="ml-1 text-muted-foreground hover:text-foreground"
                      onClick={() => handleRemoveTag(tag)}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <div className="grid gap-2">
            <div className="flex items-center justify-between">
              <Label>{t('chat.preview')}</Label>
              <div className="text-xs text-muted-foreground">
                {isEditMode
                  ? `${t('chat.editing') || '编辑中'} • ${
                      editingNote?.updatedAt ? new Date(editingNote.updatedAt).toLocaleDateString() : ''
                    }`
                  : contentId
                  ? `${t('chat.selectedContent')} • ${new Date().toLocaleDateString()}`
                  : ''}
              </div>
            </div>
            <Tabs value={isEditingContent ? 'edit' : 'preview'} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="edit" onClick={() => setIsEditingContent(true)} className="flex items-center gap-2">
                  <Edit className="h-4 w-4" />
                  {t('chat.edit')}
                </TabsTrigger>
                <TabsTrigger
                  value="preview"
                  onClick={() => setIsEditingContent(false)}
                  className="flex items-center gap-2"
                >
                  <Eye className="h-4 w-4" />
                  {t('chat.preview')}
                </TabsTrigger>
              </TabsList>
              <TabsContent value="preview" className="mt-2">
                <div className="border rounded-md p-3 bg-background dark:bg-background min-h-48 max-h-80 sm:min-h-40 sm:max-h-64 overflow-y-auto">
                  {content ? (
                    <MarkdownPreview
                      source={content}
                      style={{
                        backgroundColor: 'transparent',
                        fontSize: '14px'
                      }}
                      wrapperElement={{
                        'data-color-mode': isDarkMode(theme) ? 'dark' : 'light'
                      }}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      {isEditMode ? t('chat.noContent') || '暂无内容' : t('chat.noContentSelected')}
                    </p>
                  )}
                </div>
              </TabsContent>
              <TabsContent value="edit" className="mt-2">
                <Textarea
                  placeholder={t('chat.contentPlaceholder')}
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  className="min-h-48 max-h-80 sm:min-h-40 sm:max-h-64 resize-none"
                  rows={12}
                />
              </TabsContent>
            </Tabs>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            {t('chat.cancel')}
          </Button>
          <Button onClick={handleSave} disabled={!title.trim() || isSaving}>
            {isSaving
              ? isEditMode
                ? t('chat.updating') || '更新中...'
                : t('chat.saving')
              : isEditMode
              ? t('chat.updateNote') || '更新笔记'
              : t('chat.saveNote')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

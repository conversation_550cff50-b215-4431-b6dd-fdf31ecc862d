"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Check, Copy, Loader2 } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { useTranslation } from "@/lib/translation-context"
import { postStripeCreateCheckoutSession } from "@/servers/api/zhifu"
import { useHybridIsAuthenticated } from "@/lib/stores"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

interface UpgradeModalProps {
  isOpen: boolean
  onClose: () => void
  showPromotion?: boolean
}

// 定义计划生成函数
const createPlans = (t: any) => {
  // 基础计划模板
  const basePlans = [
    {
      id: 'free',
      name: t('upgradeModal.plans.free.name'),
      price: '$0',
      billingCycle: t('upgradeModal.plans.free.billingCycle'),
      lookupKey: '',
      features: [
        { text: t('upgradeModal.plans.free.features.uploads'), included: true },
        { text: t('upgradeModal.plans.free.features.aiChat'), included: true },
        { text: t('upgradeModal.plans.free.features.quizAnswers'), included: true },
        { text: t('upgradeModal.plans.free.features.exams'), included: true },
        { text: t('upgradeModal.plans.free.features.voiceChat'), included: true },
        { text: t('upgradeModal.plans.free.features.fileSize'), included: true }
      ],
      buttonText: t('upgradeModal.startButton'),
      variant: 'outline' as const
    },
    {
      id: 'pro',
      name: t('upgradeModal.plans.pro.name'),
      features: [
        { text: t('upgradeModal.plans.pro.features.uploads'), included: true },
        { text: t('upgradeModal.plans.pro.features.aiChat'), included: true },
        { text: t('upgradeModal.plans.pro.features.quizzes'), included: true },
        { text: t('upgradeModal.plans.pro.features.exams'), included: true },
        { text: t('upgradeModal.plans.pro.features.voiceMode'), included: true },
        { text: t('upgradeModal.plans.pro.features.fileSize'), included: true },
        { text: t('upgradeModal.plans.pro.features.support'), included: true }
      ],
      buttonText: t('upgradeModal.selectPlan'),
      variant: 'default' as const,
      popular: true
    }
  ];

  // Pro 计划的差异配置
  const proPlanConfig = {
    monthly: {
      price: '$9.99',
      billingCycle: t('upgradeModal.plans.pro.billingCycleMonthly'),
      lookupKey: 'tutoro-ai-monthly'
    },
    annually: {
      price: '$5.99',
      billingCycle: t('upgradeModal.plans.pro.billingCycleAnnually'),
      lookupKey: 'tutoro-ai-annually'
    }
  };

  // 生成具体计划
  const generatePlans = (isAnnual: boolean) => {
    const configKey = isAnnual ? 'annually' : 'monthly';
    return basePlans.map(plan => {
      if (plan.id === 'pro') {
        return {
          ...plan,
          ...proPlanConfig[configKey]
        };
      }
      return plan;
    });
  };

  return {
    monthly: generatePlans(false),
    annually: generatePlans(true)
  };
};

export function UpgradeModal({ isOpen, onClose, showPromotion = true }: UpgradeModalProps) {
  const { t } = useTranslation()
  const [isAnnual, setIsAnnual] = useState(true)
  const [loading, setLoading] = useState<string | null>(null)
  const isAuthenticated = useHybridIsAuthenticated()
  const router = useRouter()
  
  const plans = createPlans(t)
  const currentPlans = isAnnual ? plans.annually : plans.monthly
  
  // 在模态框中只显示 Pro 计划
  const modalPlans = currentPlans.filter(plan => plan.id === 'pro')

  const handleCopyCode = () => {
    navigator.clipboard.writeText("SUMMER25")
  }

  const handleSubscribe = async (lookupKey: string, planId: string) => {
    // Pro 计划需要认证
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    // 创建支付会话
    setLoading(lookupKey);
    try {
      const response = await postStripeCreateCheckoutSession({ lookupKey });
      if (response.data) {
        window.location.href = response.data;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error(t('upgradeModal.errors.checkoutFailed'));
    } finally {
      setLoading(null);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[90%] sm:max-w-md p-0 gap-0 max-h-[90vh] overflow-y-auto">
        {/* 促销横幅 */}
        {/* {showPromotion && (
          <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-t-lg">
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <span className="text-green-600 dark:text-green-400">🎉</span>
                <span className="text-sm font-semibold text-green-700 dark:text-green-300">
                  {t("upgradeModal.discount")}
                </span>
              </div>
              <div className="inline-flex items-center gap-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg px-3 py-1">
                <span className="font-mono text-sm text-green-700 dark:text-green-300">SUMMER25</span>
                <Button variant="ghost" size="icon" className="h-4 w-4 p-0" onClick={handleCopyCode}>
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                {t("upgradeModal.useCode")}
              </p>
            </div>
          </div>
        )} */}

        <div className="p-6">
          <DialogHeader className="mb-4">
            <DialogTitle className="text-xl font-bold text-center">
              {t("upgradeModal.upgradeTitle")}
            </DialogTitle>
            <p className="text-muted-foreground text-center text-sm">
              {t("upgradeModal.choosePlan")}
            </p>
          </DialogHeader>

          {/* 计费周期切换 */}
          <div className="flex items-center justify-center gap-3 mb-6">
            <Label className={`text-sm ${!isAnnual ? "font-semibold" : "text-muted-foreground"}`}>
              {t("upgradeModal.monthly")}
            </Label>
            <Switch checked={isAnnual} onCheckedChange={setIsAnnual} />
            <Label className={`text-sm ${isAnnual ? "font-semibold" : "text-muted-foreground"}`}>
              {t("upgradeModal.annual")}
            </Label>
            {isAnnual && (
              <Badge variant="secondary" className="bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300 text-xs">
                {t("upgradeModal.save")}
              </Badge>
            )}
          </div>

          {/* 计划卡片 */}
          <div className="space-y-4 mb-6">
            {modalPlans.map((plan) => (
              <Card key={plan.id} className="border-primary ring-2 ring-primary shadow-xl dark:bg-slate-800">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{plan.name}</CardTitle>
                    <span className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded">
                      {t('upgradeModal.mostPopular')}
                    </span>
                  </div>
                  
                  <div className="flex items-baseline">
                    <span className="text-2xl font-extrabold tracking-tight">{plan.price}</span>
                    {plan.billingCycle && (
                      <span className="ml-1 text-sm font-semibold text-gray-500 dark:text-gray-400">
                        {plan.billingCycle}
                      </span>
                    )}
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-2">
                  <ul className="space-y-2 text-xs">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="h-3 w-3 text-green-500 mr-2 shrink-0 mt-0.5" />
                        <span className="text-gray-900 dark:text-gray-100">
                          {feature.text}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                
                <CardFooter>
                  <Button
                    className="w-full"
                    variant={plan.variant}
                    disabled={loading === plan.lookupKey}
                    onClick={() => handleSubscribe(plan.lookupKey || '', plan.id)}
                  >
                    {loading === plan.lookupKey && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {plan.buttonText}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* 底部信息 */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-2">
              <div className="flex -space-x-1">
                <div className="w-5 h-5 bg-blue-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                <div className="w-5 h-5 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                <div className="w-5 h-5 bg-yellow-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                <div className="w-5 h-5 bg-purple-500 rounded-full border-2 border-white dark:border-gray-800"></div>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              {t("upgradeModal.joinCommunity")}
            </p>
            <p className="text-xs text-muted-foreground mt-2">
              {t("upgradeModal.needTeamPlan")}{" "}
              <Button variant="link" className="p-0 h-auto text-xs" onClick={() => window.open('/contact', '_blank')}>
                {t("upgradeModal.contactUs")}
              </Button>
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

/*
 * @Date: 2025-01-27
 * @LastEditors: yosan
 * @LastEditTime: 2025-06-17 18:59:52
 * @FilePath: /tutoro-ai-front/components/space-grid.tsx
 */
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Trash2, Plus, Box, MoreVertical, ArrowRight } from 'lucide-react';
import { DeleteConfirmModal } from './delete-confirm-modal';
import { useTranslation } from '@/lib/translation-context';
import { useHybridIsAuthenticated } from '@/lib/stores';
import { useRouter } from 'next/navigation';

interface Space {
  id: string;
  name: string;
  itemCount?: number;
  description?: string;
  createdAt: string;
}

interface SpaceGridProps {
  spaces: Space[];
  onDelete: (spaceId: string) => void;
  onAdd: () => Promise<void> | void;
  onSpaceClick: (spaceId: string) => void;
  className?: string;
}

export function SpaceGrid({ spaces, onDelete, onAdd, onSpaceClick, className = '' }: SpaceGridProps) {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [spaceToDelete, setSpaceToDelete] = useState<Space | null>(null);
  const [isAddingSpace, setIsAddingSpace] = useState(false);
  const { t } = useTranslation();
  const isAuthenticated = useHybridIsAuthenticated();
  const router = useRouter();

  const handleDeleteClick = (e: React.MouseEvent, space: Space) => {
    e.stopPropagation();
    console.log('🚀 ~ handleDeleteClick ~ space:', space);
    setSpaceToDelete(space);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = () => {
    console.log('🚀 ~ handleConfirmDelete ~ spaceToDelete:', spaceToDelete);

    if (spaceToDelete) {
      onDelete(spaceToDelete.id);
      setDeleteModalOpen(false);
      setSpaceToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setSpaceToDelete(null);
  };

  const handleAddSpace = async () => {
    if (!isAuthenticated) {
      router.push('/login?redirect=/');
      return;
    }
    if (isAddingSpace) return;

    setIsAddingSpace(true);
    try {
      await Promise.resolve(onAdd());
    } catch (error) {
      console.error('Error adding space:', error);
    } finally {
      setIsAddingSpace(false);
    }
  };

  return (
    <div className={className}>
      <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-6">{t('mainContent.mySpaces')}</h2>

      {/* Desktop Grid Layout */}
      <div className="hidden sm:grid grid-cols-2 gap-4">
        {spaces.map((space) => (
          <Card
            key={space.id}
            className="group cursor-pointer hover:shadow-md transition-all duration-300 border-gray-200 dark:border-neutral-800 dark:bg-neutral-800/50 hover:bg-white dark:hover:bg-neutral-800"
            onClick={() => onSpaceClick(space.id)}
          >
            <CardContent className="p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3 min-w-0 flex-1">
                <Box className="h-5 w-5 text-gray-600 dark:text-gray-400 flex-shrink-0" />
                <span className="font-medium text-gray-900 dark:text-white truncate">{space.name}</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400"
                onClick={(e) => handleDeleteClick(e, space)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </CardContent>
          </Card>
        ))}

        {/* Add Space Button - Desktop */}
        <Card
          className="cursor-pointer hover:shadow-md transition-all duration-300 border-gray-200 dark:border-neutral-800 border-dashed hover:border-gray-400 dark:hover:border-neutral-600 dark:bg-neutral-800/50 hover:bg-white dark:hover:bg-neutral-800"
          onClick={handleAddSpace}
        >
          <CardContent className="p-4 flex items-center space-x-3 h-full min-h-[60px]">
            <Plus className={`h-5 w-5 text-gray-400 dark:text-gray-500 ${isAddingSpace ? 'animate-spin' : ''}`} />
            <span className="font-medium text-gray-600 dark:text-gray-400">
              {isAddingSpace ? t('mainContent.addingSpace') : t('mainContent.addSpace')}
            </span>
          </CardContent>
        </Card>
      </div>

      {/* Mobile List Layout */}
      <div className="sm:hidden space-y-3">
        {spaces.map((space) => (
          <Card
            key={space.id}
            className="group cursor-pointer hover:shadow-md transition-all duration-300 border-gray-200 dark:border-neutral-800 dark:bg-neutral-800/50 hover:bg-white dark:hover:bg-neutral-800"
            onClick={() => onSpaceClick(space.id)}
          >
            <CardContent className="p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3 min-w-0 flex-1">
                <Box className="h-5 w-5 text-gray-600 dark:text-gray-400 flex-shrink-0" />
                <span className="font-medium text-gray-900 dark:text-white truncate">
                  {space.name}
                  {/* {space.itemCount !== undefined && (
                    <span className="text-gray-500 dark:text-gray-400"> ({space.itemCount})</span>
                  )} */}
                </span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400"
                onClick={(e) => handleDeleteClick(e, space)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </CardContent>
          </Card>
        ))}

        {/* Add Space Button - Mobile */}
        <Card
          className="cursor-pointer hover:shadow-md transition-all duration-300 border-gray-200 dark:border-neutral-800 border-dashed hover:border-gray-400 dark:hover:border-neutral-600 dark:bg-neutral-800/50 hover:bg-white dark:hover:bg-neutral-800"
          onClick={handleAddSpace}
        >
          <CardContent className="p-5 flex items-center space-x-4">
            <Plus className={`h-6 w-6 text-gray-400 dark:text-gray-500 ${isAddingSpace ? 'animate-spin' : ''}`} />
            <span className="font-medium text-gray-600 dark:text-gray-400 text-lg">
              {isAddingSpace ? t('mainContent.addingSpace') : t('mainContent.addSpace')}
            </span>
          </CardContent>
        </Card>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        spaceName={spaceToDelete?.name || ''}
      />
    </div>
  );
}

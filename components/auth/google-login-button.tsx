'use client';

import React, { useState } from 'react';
import { GoogleLogin, useGoogleLogin } from '@react-oauth/google';
import { useTranslation } from '@/lib/translation-context';
import { useTheme } from 'next-themes';
import { isDarkMode } from '@/lib/utils/theme';
import { toast } from 'sonner';

interface GoogleLoginButtonProps {
  onSuccess: (response: any) => void;
  onError?: (error?: any) => void;
  isLoading?: boolean;
  variant?: 'native' | 'custom';
}

export function GoogleLoginButton({ 
  onSuccess, 
  onError, 
  isLoading = false,
  variant = 'native'
}: GoogleLoginButtonProps) {
  const { t, locale } = useTranslation();
  const { theme } = useTheme();

  // 根据当前语言设置Google登录的locale
  const getGoogleLocale = (currentLocale: string) => {
    switch (currentLocale) {
      case 'zh':
        return 'zh-CN';
      case 'zh-HK':
        return 'zh-TW';
      case 'en':
      default:
        return 'en';
    }
  };

  // 根据主题选择Google登录按钮的主题
  const getGoogleTheme = () => {
    return isDarkMode(theme) ? 'filled_black' : 'outline';
  };

  // 错误处理
  const handleError = (error?: any) => {
    console.error('Google login failed:', error);

    let errorMessage = t('auth.errors.google.default') || 'Google login failed';
    if (error?.error === 'popup_closed_by_user') {
      errorMessage = t('auth.errors.google.cancelled') || 'Login cancelled by user';
    } else if (error?.error === 'access_denied') {
      errorMessage = t('auth.errors.google.denied') || 'Access denied by user';
    } else if (error?.error === 'invalid_client') {
      errorMessage = t('auth.errors.google.config') || 
        'Google client configuration error, please contact administrator';
    }

    toast.error(errorMessage);
    onError?.(error);
  };

  // 自定义按钮样式 - 使用原生GoogleLogin但使用自定义样式
  if (variant === 'custom') {
    return (
      <div className="w-full">
        {/* 使用原生GoogleLogin组件，但隐藏它 */}
        <div style={{ display: 'none' }}>
          <GoogleLogin
            onSuccess={onSuccess}
            onError={handleError}
            width="384"
            text="continue_with"
            shape="rectangular"
            size="large"
            logo_alignment="left"
            theme={getGoogleTheme()}
            locale={getGoogleLocale(locale)}
          />
        </div>
        
        {/* 自定义按钮，点击时触发原生Google登录 */}
        <button
          onClick={() => {
            // 触发原生Google登录
            const googleButton = document.querySelector('[data-testid="google-login-button"]') as HTMLElement;
            if (googleButton) {
              googleButton.click();
            } else {
              // 如果找不到按钮，直接显示原生组件
              const hiddenDiv = document.querySelector('div[style*="display: none"]') as HTMLElement;
              if (hiddenDiv) {
                hiddenDiv.style.display = 'block';
                const button = hiddenDiv.querySelector('div[role="button"]') as HTMLElement;
                if (button) {
                  button.click();
                  hiddenDiv.style.display = 'none';
                }
              }
            }
          }}
          disabled={isLoading}
          className={`
            flex items-center justify-center gap-3 w-full h-12 px-4 
            rounded-lg border font-medium text-sm transition-all duration-200
            ${isDarkMode(theme) 
              ? 'bg-gray-800 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-500' 
              : 'bg-white hover:bg-gray-50 text-gray-900 border-gray-300 hover:border-gray-400'
            }
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
            disabled:opacity-50 disabled:cursor-not-allowed
            shadow-sm hover:shadow-md
          `}
        >
          {isLoading ? (
            <div className={`animate-spin rounded-full h-5 w-5 border-b-2 ${
              isDarkMode(theme) ? 'border-white' : 'border-gray-900'
            }`}></div>
          ) : (
            <svg className="w-5 h-5" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
          )}
          <span>
            {t('auth.login.googleAuth') || 'Continue with Google'}
          </span>
        </button>
      </div>
    );
  }

  // 使用原生GoogleLogin组件
  return (
    <div className="w-full">
      <GoogleLogin
        onSuccess={onSuccess}
        onError={handleError}
        width="384"
        text="continue_with"
        shape="rectangular"
        size="large"
        logo_alignment="left"
        theme={getGoogleTheme()}
        locale={getGoogleLocale(locale)}
        context="signin"
      />
      
      {/* 自定义CSS覆盖，用于更好的主题支持 */}
      <style jsx global>{`
        /* 深色模式下的Google登录按钮样式覆盖 */
        ${isDarkMode(theme) ? `
          .google-login-button {
            background-color: #1f2937 !important;
            border-color: #4b5563 !important;
            color: #f9fafb !important;
          }
          .google-login-button:hover {
            background-color: #374151 !important;
            border-color: #6b7280 !important;
          }
        ` : ''}
        
        /* 响应式调整 */
        @media (max-width: 640px) {
          .google-login-button {
            width: 100% !important;
          }
        }
      `}</style>
    </div>
  );
} 
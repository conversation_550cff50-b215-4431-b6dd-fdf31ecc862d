'use client';
import { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChatMessageItem } from '@/components/chat/chat-message-item';
import { ChatInputArea } from '@/components/chat/chat-input-area';
import { ChatLoadingIndicator } from '@/components/chat/chat-loading-indicator';
import { X, MessageCircle, BookmarkPlus, Plus } from 'lucide-react';
import { CollectNoteModal } from '@/components/notes/collect-note-modal';
import { postLlmChatContentId } from '@/servers/api/wendangneirongliaotian';
import { postLlmChatContentIdStream } from '@/lib/api/streaming';
import { getSpaceNewSessionId } from '@/servers/api/kongjian';
import { useAppDataStore, type Model } from '@/lib/stores/app-data-store';
import type { ChatMessage } from '@/types';
import { useTranslation } from '@/lib/translation-context';
import { toast } from 'sonner';
import { saveChatToStorage, loadChatFromStorage, clearChatFromStorage } from '@/lib/utils/chat-storage';
import { useAppStore } from '@/lib/stores';

interface SpaceChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile?: boolean;
  spaceId: number | null;
}

export function SpaceChatWindow({ isOpen, onClose, isMobile = false, spaceId = null }: SpaceChatWindowProps) {
  const { t } = useTranslation();
  const { models } = useAppDataStore();

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  // 输入框
  const [inputValue, setInputValue] = useState('');
  // 选中的模型
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  // 是否加载中
  const [isLoading, setIsLoading] = useState(false);
  // 是否打开收藏笔记弹窗
  const [isCollectModalOpen, setIsCollectModalOpen] = useState(false);
  // 选中的文本
  const [selectedText, setSelectedText] = useState<string>('');
  // 是否显示网络
  const [showGlobe, setShowGlobe] = useState(false);
  // 多个文件上传（用于ChatInputArea）
  const [uploadedFiles, setUploadedFiles] = useState<Array<{ id: number; name: string; size: number }>>([]);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { openUpgradeModal } = useAppStore();
  // 从本地存储加载聊天记录
  useEffect(() => {
    if (spaceId) {
      const storedMessages = loadChatFromStorage('space', spaceId);
      if (storedMessages.length === 0) {
        // 如果没有存储的消息，添加默认欢迎消息
        setMessages([
          {
            id: '1',
            sender: 'ai',
            text: t('chat.welcomeMessage'),
            timestamp: new Date()
          }
        ]);
      } else {
        setMessages(storedMessages);
      }
    }
  }, [spaceId, t]);

  // 保存聊天记录到本地存储
  useEffect(() => {
    if (spaceId && messages.length > 0) {
      saveChatToStorage('space', spaceId, messages);
    }
  }, [spaceId, messages]);

  // 创建新聊天 - 调用 API 并清空聊天记录和本地存储
  const handleNewChat = useCallback(async () => {
    if (!spaceId) {
      toast.error('缺少有效的空间ID');
      return;
    }

    try {
      // 调用新建会话接口
      await getSpaceNewSessionId({ id: spaceId });

      // 清空聊天记录和本地存储
      const welcomeMessage: ChatMessage = {
        id: '1',
        sender: 'ai',
        text: t('chat.welcomeMessage'),
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
      setInputValue('');
      clearChatFromStorage('space', spaceId);
      toast.success(t('chat.clearSuccess'));
    } catch (error: any) {
      console.error('New session creation failed:', error);
      toast.error(error?.msg || error?.message || t('common.error'));
    }
  }, [spaceId, t]);

  // 当models加载完成后，设置默认选中第一个模型
  useEffect(() => {
    if (models.length > 0 && !selectedModel) {
      setSelectedModel(models[0]);
    }
  }, [models, selectedModel]);

  // 改进的滚动到底部函数，参考 ChatComponent
  const scrollToBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      setTimeout(() => {
        const scrollElement = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement;
        if (scrollElement) {
          scrollElement.scrollTo({
            top: scrollElement.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
  }, []);

  const handleSendMessage = async () => {
    if (inputValue.trim() === '' || isLoading) return;

    console.log('🚀 ~ handleSendMessage ~ inputValue:', inputValue);

    if (!selectedModel) {
      toast.error('请选择一个模型');
      return;
    }

    // 验证 spaceId 是否有效
    if (!spaceId) {
      toast.error('缺少有效的空间ID');
      return;
    }

    const newMessage: ChatMessage = {
      id: String(Date.now()),
      sender: 'user',
      text: inputValue,
      timestamp: new Date()
    };

    setMessages((prev) => [...prev, newMessage]);
    setInputValue('');
    setIsLoading(true);

    // 创建AI响应消息
    const aiMessageId = String(Date.now() + 1);
    const aiResponse: ChatMessage = {
      id: aiMessageId,
      sender: 'ai',
      text: '',
      timestamp: new Date()
    };

    // 添加空的AI响应消息
    setMessages((prev) => [...prev, aiResponse]);

    try {
      // 使用专用的 Space 聊天流式API
      // 将多个文件ID转换为数组形式，以兼容现有的 API
      const uploadIds = uploadedFiles.map((file) => file.id);
      await postLlmChatContentIdStream(
        {
          spaceId,
          question: newMessage.text,
          modelId: selectedModel?.id,
          uploadId: uploadIds
        },
        // onMessage - 接收流式数据
        (chunk: string) => {
          console.log('🔄 Stream chunk:', chunk);
          setMessages((prev) => prev.map((msg) => (msg.id === aiMessageId ? { ...msg, text: msg.text + chunk } : msg)));
        },
        // onError
        (error: Error) => {
          console.error('❌ Stream error - setting loading to false:', error);
          if (error?.code === '30501') {
            openUpgradeModal();
          }
          // 🚨 改进错误处理：显示具体的错误信息
          const errorMessage = error.message || '抱歉，发生了错误，请稍后重试。';
          
          setMessages((prev) =>
            prev.map((msg) => (msg.id === aiMessageId ? { ...msg, text: `❌ ${errorMessage}` } : msg))
          );
          
          // 显示具体的错误信息给用户
          toast.error(errorMessage);
          setIsLoading(false); // 确保在错误时清除 loading 状态
        },
        // onComplete
        () => {
          console.log('✅ Stream completed - setting loading to false');
          setIsLoading(false);
        }
      );
    } catch (error: any) {
      console.error('Chat API error:', error);

      setMessages((prev) =>
        prev.map((msg) => (msg.id === aiMessageId ? { ...msg, text: '抱歉，发生了错误，请稍后重试。' } : msg))
      );
      toast.error('发送消息失败，请稍后重试');
      setIsLoading(false);
    }
  };

  const copyMessage = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(t('chat.copySuccess'));
  };

  const handleCollectNote = (text: string) => {
    console.log('🚀 ~ handleCollectNote ~ text:', text);
    setSelectedText(text);
    setIsCollectModalOpen(true);
  };

  // 当消息更新时自动滚动到底部，参考 ChatComponent
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // 当聊天窗口打开时，滚动到底部
  useEffect(() => {
    if (isOpen) {
      scrollToBottom();
    }
  }, [isOpen, scrollToBottom]);

  const chatContent = (
    <div className="flex flex-col h-full bg-white dark:bg-neutral-900">
      <div className="flex items-center justify-between p-3 md:p-4 border-b dark:border-neutral-700">
        <div className="flex items-center gap-2 md:gap-3">
          <div className="w-7 h-7 md:w-8 md:h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <MessageCircle className="w-3.5 h-3.5 md:w-4 md:h-4 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-sm md:text-base text-gray-900 dark:text-gray-100">{t('chat.aiTitle')}</h3>
            {/* <p className="text-xs text-gray-500 dark:text-gray-400">{t('chat.online')}</p> */}
          </div>
        </div>
        <div className="flex items-center gap-1 md:gap-2">
          {/* New Chat Button - 只在有聊天记录时显示（排除欢迎消息） */}
          {messages.length > 1 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNewChat}
              className="h-7 w-auto md:h-8 text-xs md:text-sm font-medium gap-1.5 hover:bg-gray-50 dark:hover:bg-neutral-800 px-2 md:px-3"
            >
              <Plus className="w-3 h-3 md:w-4 md:h-4" />
              <span className="hidden sm:inline">{t('chat.clear')}</span>
            </Button>
          )}
          <Button variant="ghost" size="icon" onClick={onClose} className="h-7 w-7 md:h-8 md:w-8">
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1 p-3 md:p-4 overflow-hidden" ref={scrollAreaRef}>
        <div className="space-y-3 md:space-y-4 min-w-0">
          {messages.map((msg, index) => (
            <ChatMessageItem
              key={msg.id}
              message={msg}
              onCopyMessage={copyMessage}
              handleCollectNote={handleCollectNote}
              index={index}
              isLoading={isLoading && msg.sender === 'ai' && index === messages.length - 1}
            />
          ))}
        </div>
      </ScrollArea>

      <ChatInputArea
        inputValue={inputValue}
        showGlobe={showGlobe}
        setShowGlobe={setShowGlobe}
        placeholder={t('chat.placeholder')}
        onInputValueChange={setInputValue}
        onSendMessage={handleSendMessage}
        selectedMode={selectedModel?.name || 'Default'}
        onModeChange={(modelName: string) => {
          const foundModel = models.find((m) => m.name === modelName);
          if (foundModel) {
            setSelectedModel(foundModel);
          }
        }}
        isLoading={isLoading}
        availableModels={models}
        uploadedFiles={uploadedFiles}
        onUploadedFilesChange={setUploadedFiles}
      />

      <CollectNoteModal
        isOpen={isCollectModalOpen}
        onClose={() => setIsCollectModalOpen(false)}
        selectedText={selectedText}
        spaceId={spaceId || undefined}
        mode="create-with-content"
      />
    </div>
  );

  if (!isOpen) return null;

  if (isMobile) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-2 sm:p-4">
        <div className="bg-white dark:bg-neutral-900 rounded-lg w-full h-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] flex flex-col shadow-xl">
          {chatContent}
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-y-0 right-0 z-40 h-svh w-[40vw] bg-white dark:bg-neutral-900 shadow-xl border-l dark:border-neutral-700 flex flex-col">
      <div className="mx-auto px-2 lg:px-6 py-3 lg:py-4 flex-shrink-0">
        <div className="h-10"></div>
      </div>
      <div className="flex-1 min-h-0">{chatContent}</div>
    </div>
  );
}

'use client';

import type React from 'react';

import { useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, ArrowRight, Upload, LinkIcon, Check } from 'lucide-react';
import { useTranslation } from '@/lib/translation-context';
import { useRouter } from 'next/navigation';

interface CreateExamModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Define types for exam creation
interface ArticleItem {
  id: string;
  title: string;
  thumbnailUrl?: string;
  selected?: boolean;
}

interface ReferenceMaterial {
  type: 'upload' | 'paste' | null;
  content?: string;
  file?: File;
}

interface ExamPreferences {
  numberOfQuestions: number;
  questionTypes: string;
  durationMinutes?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
}

// Sample articles for selection
const sampleArticles: ArticleItem[] = [
  {
    id: 'article1',
    title: '前端面试复习系列文章',
    thumbnailUrl: '/images/placeholder.svg?height=120&width=200&text=Frontend+Interview'
  },
  {
    id: 'article2',
    title: 'React 面试复习指南',
    thumbnailUrl: '/images/placeholder.svg?height=120&width=200&text=React+Guide'
  },
  {
    id: 'article3',
    title: '深入理解 JavaScript 异步编程',
    thumbnailUrl: '/images/placeholder.svg?height=120&width=200&text=JS+Async'
  }
];

export function CreateExamModal({ isOpen, onClose }: CreateExamModalProps) {
  // State for multi-step form
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);
  const [referenceMaterial, setReferenceMaterial] = useState<ReferenceMaterial>({ type: null });
  const [preferences, setPreferences] = useState<ExamPreferences>({
    numberOfQuestions: 25,
    questionTypes: 'both'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { t } = useTranslation();
  const router = useRouter();

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  // Handle article selection
  const toggleArticleSelection = (articleId: string) => {
    setSelectedArticles((prev) =>
      prev.includes(articleId) ? prev.filter((id) => id !== articleId) : [...prev, articleId]
    );
  };

  // Handle reference material type selection
  const handleReferenceTypeChange = (type: 'upload' | 'paste' | null) => {
    setReferenceMaterial({ type, content: '', file: undefined });
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setReferenceMaterial((prev) => ({ ...prev, file: e.target.files![0] }));
    }
  };

  // Handle text content change
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setReferenceMaterial((prev) => ({ ...prev, content: e.target.value }));
  };

  // Handle preference changes
  const handlePreferenceChange = (field: keyof ExamPreferences, value: any) => {
    setPreferences((prev) => ({ ...prev, [field]: value }));
  };

  // Navigation functions
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSubmit();
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipStep = () => {
    if (currentStep === 2) {
      setReferenceMaterial({ type: null });
    }
    nextStep();
  };

  // Handle final submission
  const handleSubmit = () => {
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      console.log('Creating exam with:', {
        selectedArticles,
        referenceMaterial,
        preferences
      });
      router.push(`/exam/take`);
      setIsSubmitting(false);
      handleClose();
    });
  };

  // Reset and close modal
  const handleClose = () => {
    setCurrentStep(1);
    setSelectedArticles([]);
    setReferenceMaterial({ type: null });
    setPreferences({
      numberOfQuestions: 25,
      questionTypes: 'both'
    });
    onClose();
  };

  // Get step title based on current step
  const getStepTitle = () => {
    switch (currentStep) {
      case 1:
        return t('spacePage.createExam.step1');
      case 2:
        return t('spacePage.createExam.step2');
      case 3:
        return t('spacePage.createExam.step3');
      default:
        return t('spacePage.createExam.step4');
    }
  };

  // Get step description based on current step
  const getStepDescription = () => {
    switch (currentStep) {
      case 1:
        return t('spacePage.createExam.step1Description');
      case 2:
        return t('spacePage.createExam.step2Description');
      case 3:
        return t('spacePage.createExam.step3Description');
      default:
        return '';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[90%] p-0 gap-0 max-h-[90vh] overflow-y-auto">
        {/* Header with progress bar */}
        <div className="p-6 border-b relative">
          {currentStep > 1 && (
            <Button
              variant="ghost"
              size="icon"
              onClick={prevStep}
              className="absolute left-4 top-1/2 transform -translate-y-1/2"
              disabled={isSubmitting}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          <div className="text-center">
            <Progress value={progress} className="w-1/2 mx-auto mb-4 h-1.5" />
            <h2 className="text-xl font-semibold">{getStepTitle()}</h2>
            <p className="text-sm text-muted-foreground mt-1">{getStepDescription()}</p>
          </div>
        </div>

        {/* Step 1: Content Selection */}
        {currentStep === 1 && (
          <div className="p-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {sampleArticles.map((article) => (
                <Card
                  key={article.id}
                  className={`cursor-pointer transition-all ${
                    selectedArticles.includes(article.id) ? 'ring-2 ring-primary shadow-lg' : 'hover:shadow-md'
                  }`}
                  onClick={() => toggleArticleSelection(article.id)}
                >
                  <CardContent className="p-4 space-y-2 relative">
                    {article.thumbnailUrl && (
                      <div className="aspect-video bg-muted rounded overflow-hidden mb-2">
                        <img
                          src={article.thumbnailUrl || '/images/placeholder.svg'}
                          alt={article.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <h3 className="font-medium text-sm leading-tight">{article.title}</h3>
                    {selectedArticles.includes(article.id) && (
                      <div className="absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1">
                        <Check className="h-3 w-3" />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="flex items-center justify-between mt-6 pt-4 border-t">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={selectedArticles.length === sampleArticles.length && sampleArticles.length > 0}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedArticles(sampleArticles.map((a) => a.id));
                    } else {
                      setSelectedArticles([]);
                    }
                  }}
                />
                <Label htmlFor="select-all" className="text-sm">
                  {t('spacePage.createExam.selectAll')} ({selectedArticles.length}/{sampleArticles.length})
                </Label>
              </div>
              <Button onClick={nextStep} disabled={selectedArticles.length === 0}>
                {t('spacePage.createExam.continue')} <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Step 2: Reference Materials */}
        {currentStep === 2 && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card
                className={`cursor-pointer transition-all p-6 ${
                  referenceMaterial.type === 'upload' ? 'ring-2 ring-primary shadow-lg' : 'hover:shadow-md'
                }`}
                onClick={() => handleReferenceTypeChange('upload')}
              >
                <CardContent className="flex flex-col items-center justify-center space-y-2 p-0">
                  <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                  <h3 className="font-semibold">{t('spacePage.createExam.upload')}</h3>
                  <p className="text-sm text-muted-foreground">{t('spacePage.createExam.uploadDesc')}</p>
                  {referenceMaterial.type === 'upload' && (
                    <Input type="file" className="mt-4" onChange={handleFileChange} />
                  )}
                </CardContent>
              </Card>
              <Card
                className={`cursor-pointer transition-all p-6 ${
                  referenceMaterial.type === 'paste' ? 'ring-2 ring-primary shadow-lg' : 'hover:shadow-md'
                }`}
                onClick={() => handleReferenceTypeChange('paste')}
              >
                <CardContent className="flex flex-col items-center justify-center space-y-2 p-0">
                  <LinkIcon className="h-10 w-10 text-muted-foreground mb-2" />
                  <h3 className="font-semibold">{t('spacePage.createExam.paste')}</h3>
                  <p className="text-sm text-muted-foreground">{t('spacePage.createExam.pasteDesc')}</p>
                  {referenceMaterial.type === 'paste' && (
                    <Textarea
                      placeholder={t('spacePage.createExam.pastePlaceholder')}
                      className="mt-4 h-24"
                      value={referenceMaterial.content}
                      onChange={handleContentChange}
                    />
                  )}
                </CardContent>
              </Card>
            </div>

            <div className="flex justify-center mt-6 pt-4 border-t">
              <div className="flex gap-4">
                <Button variant="outline" onClick={skipStep} disabled={isSubmitting}>
                  {t('spacePage.createExam.skip')}
                </Button>
                <Button
                  onClick={nextStep}
                  disabled={
                    (referenceMaterial.type === 'upload' && !referenceMaterial.file) ||
                    (referenceMaterial.type === 'paste' && !referenceMaterial.content?.trim())
                  }
                >
                  {t('spacePage.createExam.continue')} <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Preferences */}
        {currentStep === 3 && (
          <div className="p-6">
            <div className="space-y-6 max-w-md mx-auto">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="numberOfQuestions" className="flex items-center">
                    {t('spacePage.createExam.numberOfQuestions')} <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="numberOfQuestions"
                    type="number"
                    value={preferences.numberOfQuestions}
                    onChange={(e) => handlePreferenceChange('numberOfQuestions', Number.parseInt(e.target.value) || 0)}
                    min="1"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="questionTypes" className="flex items-center">
                    {t('spacePage.createExam.questionTypes')} <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Select
                    value={preferences.questionTypes}
                    onValueChange={(value) => handlePreferenceChange('questionTypes', value)}
                  >
                    <SelectTrigger id="questionTypes">
                      <SelectValue placeholder={t('spacePage.createExam.questionTypes')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="both">{t('spacePage.createExam.both')}</SelectItem>
                      <SelectItem value="multiple-choice">{t('spacePage.createExam.multipleChoice')}</SelectItem>
                      <SelectItem value="fill-in-the-blank">{t('spacePage.createExam.fillInTheBlank')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="durationMinutes">{t('spacePage.createExam.durationMinutes')}</Label>
                <Input
                  id="durationMinutes"
                  type="number"
                  placeholder={t('spacePage.createExam.durationMinutesPlaceholder')}
                  value={preferences.durationMinutes || ''}
                  onChange={(e) =>
                    handlePreferenceChange('durationMinutes', Number.parseInt(e.target.value) || undefined)
                  }
                  min="0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="difficulty">{t('spacePage.createExam.difficulty')}</Label>
                <Select
                  value={preferences.difficulty || 'medium'}
                  onValueChange={(value) => handlePreferenceChange('difficulty', value)}
                >
                  <SelectTrigger id="difficulty">
                    <SelectValue placeholder={t('spacePage.createExam.difficulty')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">{t('spacePage.createExam.easy')}</SelectItem>
                    <SelectItem value="medium">{t('spacePage.createExam.medium')}</SelectItem>
                    <SelectItem value="hard">{t('spacePage.createExam.hard')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-center pt-4 border-t">
                <div className="flex gap-4">
                  <Button variant="outline" onClick={skipStep} disabled={isSubmitting}>
                    {t('spacePage.createExam.skip')}
                  </Button>
                  <Button onClick={handleSubmit} disabled={!preferences.numberOfQuestions || isSubmitting}>
                    {isSubmitting ? t('spacePage.createExam.processing') : t('spacePage.createExam.start')}
                    {!isSubmitting && <ArrowRight className="ml-2 h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

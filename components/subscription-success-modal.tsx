"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Check, Loader2 } from "lucide-react";
import { useTranslation } from "@/lib/translation-context";
import { postStripeCreatePortalSession } from "@/servers/api/zhifu";
import { toast } from "sonner";

interface SubscriptionSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string;
}

export function SubscriptionSuccessModal({ 
  isOpen, 
  onClose, 
  sessionId 
}: SubscriptionSuccessModalProps) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const handleManageBilling = async () => {
    setLoading(true);
    try {
      const response = await postStripeCreatePortalSession({ sessionId });
      if (response.data) {
        window.location.href = response.data;
      } else {
        throw new Error('No portal URL returned');
      }
    } catch (error) {
      console.error('Error creating portal session:', error);
      toast.error(t('upgradeModal.errors.portalFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="sr-only">
            {t('upgradeModal.success.title')}
          </DialogTitle>
        </DialogHeader>
        
        <Card className="border-0 shadow-none">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-4">
              <Check className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <CardTitle className="text-2xl text-green-600 dark:text-green-400">
              {t('upgradeModal.success.title')}
            </CardTitle>
            <CardDescription className="text-base mt-2">
              {t('upgradeModal.success.description')}
            </CardDescription>
          </CardHeader>
          
          <CardFooter className="flex flex-col gap-3 pt-4">
            <Button 
              onClick={handleManageBilling} 
              disabled={loading} 
              className="w-full"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('upgradeModal.success.manageBilling')}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleClose}
              className="w-full"
              disabled={loading}
            >
              {t('upgradeModal.success.continue')}
            </Button>
          </CardFooter>
        </Card>
      </DialogContent>
    </Dialog>
  );
} 